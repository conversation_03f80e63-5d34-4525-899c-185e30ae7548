<div class="container mx-auto p-4">
  <div *ngIf="loading" class="flex justify-center my-8">
    <mat-spinner></mat-spinner>
  </div>

  <div
    *ngIf="error && !encuestaCompletada"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded my-4"
  >
    Error al cargar la encuesta. Por favor, intente nuevamente.
  </div>

  <div
    *ngIf="encuestaCompletada"
    class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded my-4 flex items-center"
  >
    <mat-icon class="text-green-500 mr-2">check_circle</mat-icon>
    <div>
      <p class="font-medium">Ya has completado esta encuesta anteriormente</p>
      <p class="text-sm">A continuación puedes ver tus respuestas</p>
    </div>
  </div>

  <!-- Sección para mostrar las respuestas completadas -->
  <div *ngIf="encuesta && !loading && !error && encuestaCompletada">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
        {{ encuesta.titulo }}
      </h1>

      <button
        mat-raised-button
        color="primary"
        [routerLink]="['/encuestas']"
        class="bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800 px-4 py-2 rounded-md transition-colors flex items-center gap-2"
      >
        <mat-icon class="text-white">arrow_back</mat-icon>
        <span>Volver al listado</span>
      </button>
    </div>

    <mat-card class="mb-6 dark:bg-gray-800">
      <mat-card-content>
        <p class="dark:text-gray-300 mb-4">{{ encuesta.descripcion }}</p>

        <div class="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg mt-4">
          <div class="flex items-center">
            <mat-icon class="text-blue-700 dark:text-blue-300 mr-2"
              >info</mat-icon
            >
            <span class="text-blue-700 dark:text-blue-300">
              Encuesta completada el
              {{ respuestaCompletada?.fechaFin | date : "dd/MM/yyyy HH:mm" }}
            </span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Preguntas y respuestas -->
    <div
      *ngIf="encuesta.preguntas && encuesta.preguntas.length > 0"
      class="space-y-6"
    >
      <mat-card
        *ngFor="let pregunta of encuesta.preguntas; let i = index"
        class="dark:bg-gray-800"
      >
        <mat-card-header>
          <mat-card-title class="dark:text-white">
            {{ i + 1 }}. {{ pregunta.enunciado }}
          </mat-card-title>
          <mat-card-subtitle
            *ngIf="pregunta.descripcion"
            class="dark:text-gray-300"
          >
            {{ pregunta.descripcion }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content class="mt-4">
          <!-- Respuesta del usuario -->
          <div
            *ngIf="
              respuestaCompletada && respuestaCompletada.detallesRespuestas
            "
            class="mt-2 ml-4"
          >
            <ng-container
              *ngFor="let detalle of respuestaCompletada.detallesRespuestas"
            >
              <div
                *ngIf="detalle.preguntaId === pregunta.id"
                class="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg"
              >
                <p class="font-medium text-gray-700 dark:text-gray-300">
                  Tu respuesta:
                </p>

                <!-- Respuesta de opción múltiple o escala -->
                <div *ngIf="detalle.opcionId" class="mt-1">
                  <ng-container *ngFor="let opcion of pregunta.opciones">
                    <div
                      *ngIf="opcion.id === detalle.opcionId"
                      class="text-blue-600 dark:text-blue-300 font-medium"
                    >
                      {{ opcion.texto }}
                    </div>
                  </ng-container>
                </div>

                <!-- Respuesta de texto -->
                <div
                  *ngIf="detalle.respuestaTexto"
                  class="mt-1 text-blue-600 dark:text-blue-300 font-medium"
                >
                  {{ detalle.respuestaTexto }}
                </div>

                <!-- Respuesta numérica -->
                <div
                  *ngIf="
                    detalle.respuestaNumero !== null &&
                    detalle.respuestaNumero !== undefined
                  "
                  class="mt-1 text-blue-600 dark:text-blue-300 font-medium"
                >
                  {{ detalle.respuestaNumero }}
                </div>

                <!-- Respuesta de fecha -->
                <div
                  *ngIf="detalle.respuestaFecha"
                  class="mt-1 text-blue-600 dark:text-blue-300 font-medium"
                >
                  {{ detalle.respuestaFecha | date : "dd/MM/yyyy" }}
                </div>
              </div>
            </ng-container>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Sección para responder la encuesta -->
  <div *ngIf="encuesta && !loading && !error && !encuestaCompletada">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
        {{ encuesta.titulo }}
      </h1>

      <div
        *ngIf="tiempoRestante !== null"
        class="bg-blue-100 dark:bg-blue-900 px-4 py-2 rounded-lg flex items-center"
      >
        <mat-icon class="text-blue-500 dark:text-blue-300 mr-2">timer</mat-icon>
        <span class="text-blue-700 dark:text-blue-300 font-medium"
          >Tiempo restante: {{ formatTiempo() }}</span
        >
      </div>
    </div>

    <mat-card class="mb-6 dark:bg-gray-800">
      <mat-card-content>
        <p class="dark:text-gray-300 mb-4">{{ encuesta.descripcion }}</p>

        <div
          *ngIf="encuesta.esAnonima"
          class="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-lg mt-4"
        >
          <div class="flex items-center">
            <mat-icon class="text-yellow-700 dark:text-yellow-300 mr-2"
              >info</mat-icon
            >
            <span class="text-yellow-700 dark:text-yellow-300"
              >Esta encuesta es anónima. Sus respuestas no serán asociadas a su
              identidad.</span
            >
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <form [formGroup]="form" (ngSubmit)="onSubmit()" *ngIf="form">
      <div
        *ngIf="encuesta.preguntas && encuesta.preguntas.length > 0"
        class="space-y-6"
      >
        <mat-card
          *ngFor="let pregunta of encuesta.preguntas; let i = index"
          class="dark:bg-gray-800"
        >
          <mat-card-header>
            <mat-card-title class="dark:text-white">
              {{ i + 1 }}. {{ pregunta.enunciado }}
              <span *ngIf="pregunta.esObligatoria" class="text-red-500">*</span>
            </mat-card-title>
            <mat-card-subtitle
              *ngIf="pregunta.descripcion"
              class="dark:text-gray-300"
            >
              {{ pregunta.descripcion }}
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content class="mt-4">
            <div [ngSwitch]="pregunta.tipo">
              <!-- Opciones múltiples -->
              <div *ngSwitchCase="tipoPregunta.OPCION_MULTIPLE" class="ml-4">
                <mat-radio-group [formControlName]="'pregunta_' + pregunta.id">
                  <div *ngFor="let opcion of pregunta.opciones" class="mb-2">
                    <mat-radio-button
                      [value]="opcion.id.toString()"
                      class="dark:text-gray-300"
                    >
                      {{ opcion.texto }}
                    </mat-radio-button>
                  </div>
                </mat-radio-group>
                <mat-error
                  *ngIf="
                    form?.get('pregunta_' + pregunta.id)?.touched &&
                    form?.get('pregunta_' + pregunta.id)?.hasError('required')
                  "
                >
                  Esta pregunta es obligatoria
                </mat-error>
              </div>

              <!-- Selección múltiple -->
              <div *ngSwitchCase="tipoPregunta.SELECCION_MULTIPLE" class="ml-4">
                <div *ngFor="let opcion of pregunta.opciones" class="mb-2">
                  <mat-checkbox
                    [value]="opcion.id.toString()"
                    [checked]="isOptionSelected(pregunta.id, opcion.id)"
                    (change)="
                      onCheckboxChange(pregunta.id, opcion.id, $event.checked)
                    "
                    class="dark:text-gray-300"
                  >
                    {{ opcion.texto }}
                  </mat-checkbox>
                </div>
                <mat-error
                  *ngIf="
                    form?.get('pregunta_' + pregunta.id)?.touched &&
                    form?.get('pregunta_' + pregunta.id)?.hasError('required')
                  "
                >
                  Esta pregunta es obligatoria
                </mat-error>
              </div>

              <!-- Escala Likert -->
              <div *ngSwitchCase="tipoPregunta.ESCALA_LIKERT" class="ml-4">
                <mat-radio-group
                  [formControlName]="'pregunta_' + pregunta.id"
                  class="flex justify-between"
                >
                  <div
                    *ngFor="let opcion of pregunta.opciones"
                    class="text-center"
                  >
                    <div class="mb-2">
                      <mat-radio-button
                        [value]="opcion.id.toString()"
                        class="dark:text-gray-300"
                      ></mat-radio-button>
                    </div>
                    <div class="text-sm dark:text-gray-300">
                      {{ opcion.texto }}
                    </div>
                  </div>
                </mat-radio-group>
                <mat-error
                  *ngIf="
                    form?.get('pregunta_' + pregunta.id)?.touched &&
                    form?.get('pregunta_' + pregunta.id)?.hasError('required')
                  "
                >
                  Esta pregunta es obligatoria
                </mat-error>
              </div>

              <!-- Texto libre -->
              <div *ngSwitchCase="tipoPregunta.TEXTO_LIBRE" class="ml-4">
                <mat-form-field appearance="outline" class="w-full">
                  <mat-label>Respuesta</mat-label>
                  <textarea
                    matInput
                    [formControlName]="'pregunta_' + pregunta.id"
                    rows="3"
                    placeholder="Escriba su respuesta aquí"
                    class="dark:bg-gray-700 dark:text-white"
                  ></textarea>
                  <mat-error
                    *ngIf="
                      form?.get('pregunta_' + pregunta.id)?.touched &&
                      form?.get('pregunta_' + pregunta.id)?.hasError('required')
                    "
                  >
                    Esta pregunta es obligatoria
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Fecha -->
              <div *ngSwitchCase="tipoPregunta.FECHA" class="ml-4">
                <mat-form-field appearance="outline">
                  <mat-label>Fecha</mat-label>
                  <input
                    matInput
                    [matDatepicker]="picker"
                    [formControlName]="'pregunta_' + pregunta.id"
                    class="dark:bg-gray-700 dark:text-white"
                  />
                  <mat-datepicker-toggle
                    matSuffix
                    [for]="picker"
                  ></mat-datepicker-toggle>
                  <mat-datepicker #picker></mat-datepicker>
                  <mat-error
                    *ngIf="
                      form?.get('pregunta_' + pregunta.id)?.touched &&
                      form?.get('pregunta_' + pregunta.id)?.hasError('required')
                    "
                  >
                    Esta pregunta es obligatoria
                  </mat-error>
                </mat-form-field>
              </div>

              <!-- Número -->
              <div *ngSwitchCase="tipoPregunta.NUMERO" class="ml-4">
                <mat-form-field appearance="outline">
                  <mat-label>Número</mat-label>
                  <input
                    matInput
                    type="number"
                    [formControlName]="'pregunta_' + pregunta.id"
                    class="dark:bg-gray-700 dark:text-white"
                  />
                  <mat-error
                    *ngIf="
                      form?.get('pregunta_' + pregunta.id)?.touched &&
                      form?.get('pregunta_' + pregunta.id)?.hasError('required')
                    "
                  >
                    Esta pregunta es obligatoria
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div
        *ngIf="!encuesta.preguntas || encuesta.preguntas.length === 0"
        class="p-6 text-center text-gray-500 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
      >
        Esta encuesta no tiene preguntas.
      </div>

      <div class="flex justify-between mt-6">
        <button
          type="button"
          mat-raised-button
          [routerLink]="['/encuestas']"
          class="bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 px-4 py-2 rounded-md transition-colors"
        >
          Cancelar
        </button>
        <button
          type="submit"
          mat-raised-button
          color="primary"
          [disabled]="form.invalid || submitting"
          class="bg-green-600 hover:bg-green-700 text-white dark:bg-green-700 dark:hover:bg-green-800 px-4 py-2 rounded-md transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <mat-spinner
            *ngIf="submitting"
            diameter="20"
            class="mr-2"
          ></mat-spinner>
          <span>Finalizar Encuesta</span>
        </button>
      </div>
    </form>
  </div>
</div>
