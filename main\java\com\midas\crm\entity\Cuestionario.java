package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entidad que representa un cuestionario asociado a una lección
 */
@Entity
@Table(name = "cuestionarios")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Cuestionario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String titulo;

    @Column(length = 500)
    private String descripcion;

    @Column(name = "tiempo_limite")
    private Integer tiempoLimite; // Tiempo límite en minutos (null = sin límite)

    @Column(name = "puntaje_aprobacion")
    private Integer puntajeAprobacion; // Puntaje mínimo para aprobar (0-100)

    @Column(name = "intentos_maximos")
    private Integer intentosMaximos; // Número máximo de intentos (null = ilimitados)

    @Column(name = "mostrar_respuestas")
    private Boolean mostrarRespuestas = true; // Mostrar respuestas correctas al finalizar

    @Column(name = "aleatorizar_preguntas")
    private Boolean aleatorizarPreguntas = false; // Mostrar preguntas en orden aleatorio

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "leccion_id", nullable = false, unique = true)
    private Leccion leccion;

    @OneToMany(mappedBy = "cuestionario", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Pregunta> preguntas = new ArrayList<>();

    @Column(nullable = false, length = 1)
    private String estado = "A"; // A: Activo, I: Inactivo

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;
}
