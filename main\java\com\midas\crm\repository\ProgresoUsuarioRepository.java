package com.midas.crm.repository;

import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.ProgresoUsuario;
import com.midas.crm.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProgresoUsuarioRepository extends JpaRepository<ProgresoUsuario, Long> {
    Optional<ProgresoUsuario> findByUsuarioAndLeccion(User usuario, Leccion leccion);
    List<ProgresoUsuario> findByUsuarioId(Long usuarioId);

    @Query("SELECT p FROM ProgresoUsuario p JOIN p.leccion l JOIN l.seccion s JOIN s.modulo m WHERE m.curso.id = :cursoId AND p.usuario.id = :usuarioId")
    List<ProgresoUsuario> findByCursoIdAndUsuarioId(Long cursoId, Long usuarioId);

    @Query("SELECT COUNT(p) FROM ProgresoUsuario p JOIN p.leccion l JOIN l.seccion s JOIN s.modulo m WHERE m.curso.id = :cursoId AND p.usuario.id = :usuarioId AND p.completado = true")
    Long countCompletedLeccionesByCursoAndUsuario(Long cursoId, Long usuarioId);

    @Query("SELECT COUNT(l) FROM Leccion l JOIN l.seccion s JOIN s.modulo m WHERE m.curso.id = :cursoId")
    Long countTotalLeccionesByCurso(Long cursoId);
}
