.cuestionario-container {
  width: 100%;
  padding: 1rem;
  
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    
    mat-icon {
      font-size: 2rem;
      height: 2rem;
      width: 2rem;
      margin-bottom: 1rem;
    }
  }
  
  .cuestionario-info {
    h2 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: #333;
    }
    
    .cuestionario-descripcion {
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }
    
    .cuestionario-detalles {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 2rem;
      
      .detalle-item {
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        
        mat-icon {
          margin-right: 0.5rem;
          color: #666;
        }
      }
    }
    
    .cuestionario-acciones {
      margin-top: 1.5rem;
      
      .intentos-agotados {
        margin-top: 1rem;
        color: #f44336;
        display: flex;
        align-items: center;
        
        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
  }
  
  .cuestionario-progreso {
    .progreso-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      
      .progreso-info, .tiempo-restante {
        display: flex;
        flex-direction: column;
        
        span {
          margin-bottom: 0.5rem;
        }
      }
      
      .tiempo-restante {
        text-align: right;
        
        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
    
    .pregunta-container {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      
      .pregunta-enunciado {
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
        color: #333;
      }
      
      .opciones-container {
        margin-bottom: 1.5rem;
        
        .opcion-multiple, .seleccion-multiple, .verdadero-falso, .texto-libre {
          width: 100%;
        }
        
        .opciones-radio-group, .opciones-checkbox-group {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }
        
        .opcion-item {
          margin-bottom: 0.5rem;
        }
        
        .verdadero-falso-group {
          width: 100%;
          display: flex;
          
          .mat-button-toggle {
            flex: 1;
            text-align: center;
          }
        }
        
        .texto-libre-field {
          width: 100%;
        }
      }
      
      .pregunta-acciones {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
  
  .cuestionario-resultados {
    h2 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: #333;
    }
    
    .resultado-header {
      display: flex;
      align-items: center;
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      
      &.aprobado {
        background-color: #e8f5e9;
        color: #2e7d32;
      }
      
      &.reprobado {
        background-color: #ffebee;
        color: #c62828;
      }
      
      mat-icon {
        font-size: 2rem;
        height: 2rem;
        width: 2rem;
        margin-right: 1rem;
      }
      
      h3 {
        font-size: 1.5rem;
        margin: 0;
      }
    }
    
    .resultado-detalles {
      background-color: #f5f5f5;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      
      .detalle-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e0e0e0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .detalle-label {
          font-weight: 500;
          color: #666;
        }
        
        .detalle-valor {
          font-weight: 500;
        }
      }
    }
    
    .respuestas-detalle {
      margin-top: 2rem;
      
      h3 {
        font-size: 1.2rem;
        margin-bottom: 1rem;
        color: #333;
      }
      
      .respuesta-item {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        padding: 1rem;
        margin-bottom: 1rem;
        
        &.correcta {
          border-left: 4px solid #4caf50;
        }
        
        &.incorrecta {
          border-left: 4px solid #f44336;
        }
        
        .respuesta-pregunta {
          display: flex;
          align-items: center;
          margin-bottom: 0.5rem;
          
          mat-icon {
            margin-right: 0.5rem;
            
            &.correcta {
              color: #4caf50;
            }
            
            &.incorrecta {
              color: #f44336;
            }
          }
        }
        
        .respuesta-texto {
          margin-left: 1.5rem;
          
          .respuesta-label {
            font-weight: 500;
            margin-right: 0.5rem;
            color: #666;
          }
        }
      }
    }
    
    .resultado-acciones {
      margin-top: 2rem;
      display: flex;
      justify-content: center;
    }
  }
}

// Estilos para tema oscuro
:host-context(.dark-theme) {
  .cuestionario-container {
    .cuestionario-info {
      h2 {
        color: #fff;
      }
      
      .cuestionario-detalles {
        .detalle-item {
          background-color: #424242;
          color: #fff;
          
          mat-icon {
            color: #bbb;
          }
        }
      }
    }
    
    .cuestionario-progreso {
      .pregunta-container {
        background-color: #424242;
        
        .pregunta-enunciado {
          color: #fff;
        }
      }
    }
    
    .cuestionario-resultados {
      h2, h3 {
        color: #fff;
      }
      
      .resultado-detalles {
        background-color: #424242;
        
        .detalle-item {
          border-bottom-color: #616161;
          
          .detalle-label {
            color: #bbb;
          }
          
          .detalle-valor {
            color: #fff;
          }
        }
      }
      
      .respuestas-detalle {
        .respuesta-item {
          background-color: #424242;
          
          .respuesta-texto {
            .respuesta-label {
              color: #bbb;
            }
          }
        }
      }
    }
  }
}
