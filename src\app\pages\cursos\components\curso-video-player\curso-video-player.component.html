<div class="video-player-container">
  <div class="video-header" *ngIf="title">
    <h3>{{ title }}</h3>
  </div>

  <div class="video-wrapper">
    <app-spinner *ngIf="loading"></app-spinner>

    <div *ngIf="error" class="error-message">
      <mat-icon>error</mat-icon>
      <p>{{ errorMessage }}</p>
    </div>

    <video
      #videoPlayer
      *ngIf="videoUrl && !error"
      [src]="videoUrl"
      [controls]="controls"
      [autoplay]="autoplay"
      class="video-element"
      [class.loaded]="!loading"
      (loadeddata)="onVideoLoad()"
      (error)="onVideoError($event)">
      <track
        #subtitlesTrack
        *ngIf="subtitlesUrl && subtitlesUrl !== ''"
        kind="subtitles"
        srclang="es"
        label="Español"
        [src]="subtitlesUrl"
        [default]="showSubtitles"
        (load)="onSubtitlesLoad()"
        (error)="onSubtitlesError($event)">
      Tu navegador no soporta la reproducción de videos.
    </video>

    <!-- Botón para activar/desactivar subtítulos -->
    <div class="subtitles-control" *ngIf="subtitlesUrl && subtitlesUrl !== '' && !error">
      <button mat-icon-button (click)="toggleSubtitles()" [matTooltip]="showSubtitles ? 'Desactivar subtítulos' : 'Activar subtítulos'">
        <mat-icon>{{ showSubtitles ? 'closed_caption' : 'closed_caption_disabled' }}</mat-icon>
      </button>
    </div>
  </div>
</div>
