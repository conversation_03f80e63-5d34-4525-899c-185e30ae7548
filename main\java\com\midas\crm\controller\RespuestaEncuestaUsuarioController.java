package com.midas.crm.controller;

import com.midas.crm.entity.DTO.encuesta.DetalleRespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioDTO;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.RespuestaEncuestaUsuarioService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.respuestas-encuesta}")
@RequiredArgsConstructor
@Slf4j
public class RespuestaEncuestaUsuarioController {

    private final RespuestaEncuestaUsuarioService respuestaEncuestaUsuarioService;

    /**
     * Inicia una nueva respuesta de usuario a una encuesta
     * Implementado con programación funcional
     */
    @PostMapping("/iniciar")
    public ResponseEntity<GenericResponse<RespuestaEncuestaUsuarioDTO>> iniciarRespuestaEncuesta(
            @Valid @RequestBody RespuestaEncuestaUsuarioCreateDTO dto,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        // Si no se proporciona un usuario en el DTO, usar el usuario autenticado
        if (dto.getUsuarioId() == null) {
            dto.setUsuarioId(userPrincipal.getId());
        }
        
        return Optional.ofNullable(dto)
            .map(respuestaEncuestaUsuarioService::iniciarRespuestaEncuesta)
            .map(respuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta de encuesta iniciada exitosamente", respuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Guarda una respuesta a una pregunta específica
     * Implementado con programación funcional
     */
    @PostMapping("/{respuestaEncuestaUsuarioId}/responder")
    public ResponseEntity<GenericResponse<RespuestaEncuestaUsuarioDTO>> responderPregunta(
            @PathVariable Long respuestaEncuestaUsuarioId,
            @Valid @RequestBody DetalleRespuestaEncuestaUsuarioCreateDTO dto) {
        
        return Optional.ofNullable(dto)
            .map(detalleDTO -> respuestaEncuestaUsuarioService.responderPregunta(respuestaEncuestaUsuarioId, detalleDTO))
            .map(respuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta respondida exitosamente", respuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Finaliza una respuesta de encuesta
     * Implementado con programación funcional
     */
    @PostMapping("/{respuestaEncuestaUsuarioId}/finalizar")
    public ResponseEntity<GenericResponse<RespuestaEncuestaUsuarioDTO>> finalizarRespuestaEncuesta(
            @PathVariable Long respuestaEncuestaUsuarioId) {
        
        return Optional.ofNullable(respuestaEncuestaUsuarioId)
            .map(respuestaEncuestaUsuarioService::finalizarRespuestaEncuesta)
            .map(respuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta de encuesta finalizada exitosamente", respuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una respuesta de encuesta por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<RespuestaEncuestaUsuarioDTO>> getRespuestaEncuestaUsuarioById(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(respuestaEncuestaUsuarioService::getRespuestaEncuestaUsuarioById)
            .map(respuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta de encuesta encontrada", respuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las respuestas de un usuario a una encuesta específica
     * Implementado con programación funcional
     */
    @GetMapping("/usuario/{usuarioId}/encuesta/{encuestaId}")
    public ResponseEntity<GenericResponse<List<RespuestaEncuestaUsuarioDTO>>> getRespuestasByUsuarioAndEncuesta(
            @PathVariable Long usuarioId,
            @PathVariable Long encuestaId) {
        
        return Optional.of(new Long[]{usuarioId, encuestaId})
            .map(ids -> respuestaEncuestaUsuarioService.getRespuestasByUsuarioAndEncuesta(ids[0], ids[1]))
            .map(respuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuestas de encuesta encontradas", respuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las respuestas a una encuesta específica
     * Implementado con programación funcional
     */
    @GetMapping("/encuesta/{encuestaId}")
    public ResponseEntity<GenericResponse<List<RespuestaEncuestaUsuarioDTO>>> getRespuestasByEncuesta(
            @PathVariable Long encuestaId) {
        
        return Optional.ofNullable(encuestaId)
            .map(respuestaEncuestaUsuarioService::getRespuestasByEncuesta)
            .map(respuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuestas de encuesta encontradas", respuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Verifica si un usuario ha completado una encuesta
     * Implementado con programación funcional
     */
    @GetMapping("/usuario/{usuarioId}/encuesta/{encuestaId}/completada")
    public ResponseEntity<GenericResponse<Boolean>> hasUsuarioCompletadoEncuesta(
            @PathVariable Long usuarioId,
            @PathVariable Long encuestaId) {
        
        return Optional.of(new Long[]{usuarioId, encuestaId})
            .map(ids -> respuestaEncuestaUsuarioService.hasUsuarioCompletadoEncuesta(ids[0], ids[1]))
            .map(completada -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Verificación de encuesta completada", completada)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene estadísticas de respuestas para una encuesta
     * Implementado con programación funcional
     */
    @GetMapping("/encuesta/{encuestaId}/estadisticas")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getEstadisticasRespuestas(
            @PathVariable Long encuestaId) {
        
        return Optional.ofNullable(encuestaId)
            .map(respuestaEncuestaUsuarioService::getEstadisticasRespuestas)
            .map(estadisticas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Estadísticas de respuestas encontradas", estadisticas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }
}
