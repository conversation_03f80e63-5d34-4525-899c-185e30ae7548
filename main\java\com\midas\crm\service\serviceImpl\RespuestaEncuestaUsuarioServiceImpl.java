package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DetalleRespuestaEncuestaUsuario;
import com.midas.crm.entity.DTO.encuesta.DetalleRespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.DetalleRespuestaEncuestaUsuarioDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioDTO;
import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.OpcionRespuestaEncuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import com.midas.crm.entity.RespuestaEncuestaUsuario;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.DetalleRespuestaEncuestaUsuarioMapper;
import com.midas.crm.mapper.RespuestaEncuestaUsuarioMapper;
import com.midas.crm.repository.*;
import com.midas.crm.service.RespuestaEncuestaUsuarioService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RespuestaEncuestaUsuarioServiceImpl implements RespuestaEncuestaUsuarioService {

    private final RespuestaEncuestaUsuarioRepository respuestaEncuestaUsuarioRepository;
    private final DetalleRespuestaEncuestaUsuarioRepository detalleRespuestaEncuestaUsuarioRepository;
    private final EncuestaRepository encuestaRepository;
    private final UserRepository userRepository;
    private final PreguntaEncuestaRepository preguntaEncuestaRepository;
    private final OpcionRespuestaEncuestaRepository opcionRespuestaEncuestaRepository;

    @Override
    @Transactional
    public RespuestaEncuestaUsuarioDTO iniciarRespuestaEncuesta(RespuestaEncuestaUsuarioCreateDTO dto) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(dto.getEncuestaId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Obtener el usuario (puede ser null si la encuesta es anónima)
        User usuario = null;
        if (dto.getUsuarioId() != null) {
            usuario = userRepository.findById(dto.getUsuarioId())
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
        } else if (!encuesta.getEsAnonima()) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_REQUIRED_NON_ANONYMOUS);
        }

        // Verificar si ya existe una respuesta no completada para este usuario y
        // encuesta
        if (usuario != null) {
            respuestaEncuestaUsuarioRepository
                    .findFirstByUsuarioIdAndEncuestaIdAndCompletadaFalseOrderByFechaCreacionDesc(
                            usuario.getId(), encuesta.getId())
                    .ifPresent(respuesta -> {
                        throw new MidasExceptions(MidasErrorMessage.RESPUESTA_ENCUESTA_EN_PROGRESO);
                    });
        }

        // Crear la respuesta
        RespuestaEncuestaUsuario respuesta = RespuestaEncuestaUsuarioMapper.toEntity(dto, usuario, encuesta);
        respuesta = respuestaEncuestaUsuarioRepository.save(respuesta);

        return RespuestaEncuestaUsuarioMapper.toDTO(respuesta);
    }

    @Override
    @Transactional
    public RespuestaEncuestaUsuarioDTO responderPregunta(Long respuestaEncuestaUsuarioId,
                                                         DetalleRespuestaEncuestaUsuarioCreateDTO dto) {
        // Obtener la respuesta de encuesta
        RespuestaEncuestaUsuario respuesta = respuestaEncuestaUsuarioRepository.findById(respuestaEncuestaUsuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_ENCUESTA_USUARIO_NOT_FOUND));

        // Verificar que la respuesta no esté completada
        if (respuesta.getCompletada()) {
            throw new MidasExceptions(MidasErrorMessage.RESPUESTA_ENCUESTA_YA_COMPLETADA);
        }

        // Obtener la pregunta
        PreguntaEncuesta pregunta = preguntaEncuestaRepository.findById(dto.getPreguntaId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_ENCUESTA_NOT_FOUND));

        // Verificar que la pregunta pertenece a la encuesta
        if (!pregunta.getEncuesta().getId().equals(respuesta.getEncuesta().getId())) {
            throw new MidasExceptions(MidasErrorMessage.PREGUNTA_NO_PERTENECE_ENCUESTA);
        }

        // Obtener la opción (si aplica)
        OpcionRespuestaEncuesta opcion = null;
        if (dto.getOpcionId() != null) {
            opcion = opcionRespuestaEncuestaRepository.findById(dto.getOpcionId())
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.OPCION_RESPUESTA_ENCUESTA_NOT_FOUND));

            // Verificar que la opción pertenece a la pregunta
            if (!opcion.getPregunta().getId().equals(pregunta.getId())) {
                throw new MidasExceptions(MidasErrorMessage.OPCION_NO_PERTENECE_PREGUNTA);
            }
        }

        // Validar que el tipo de respuesta coincide con el tipo de pregunta
        switch (pregunta.getTipo()) {
            case OPCION_MULTIPLE:
            case SELECCION_MULTIPLE:
            case ESCALA_LIKERT:
                if (opcion == null) {
                    throw new MidasExceptions(MidasErrorMessage.OPCION_REQUIRED);
                }
                break;
            case TEXTO_LIBRE:
                if (dto.getRespuestaTexto() == null || dto.getRespuestaTexto().trim().isEmpty()) {
                    throw new MidasExceptions(MidasErrorMessage.TEXTO_REQUIRED);
                }
                break;
            case NUMERO:
                if (dto.getRespuestaNumero() == null) {
                    throw new MidasExceptions(MidasErrorMessage.NUMERO_REQUIRED);
                }
                break;
            case FECHA:
                if (dto.getRespuestaFecha() == null) {
                    throw new MidasExceptions(MidasErrorMessage.FECHA_REQUIRED);
                }
                break;
        }

        // Eliminar respuestas previas a esta pregunta (si existen)
        detalleRespuestaEncuestaUsuarioRepository.findByRespuestaEncuestaUsuarioIdAndPreguntaId(
                        respuestaEncuestaUsuarioId, pregunta.getId())
                .forEach(detalleRespuestaEncuestaUsuarioRepository::delete);

        // Crear el detalle de respuesta
        DetalleRespuestaEncuestaUsuario detalle = DetalleRespuestaEncuestaUsuarioMapper.toEntity(
                dto, respuesta, pregunta, opcion);
        detalleRespuestaEncuestaUsuarioRepository.save(detalle);

        // Retornar la respuesta actualizada con todos sus detalles
        return getRespuestaEncuestaUsuarioById(respuestaEncuestaUsuarioId);
    }

    @Override
    @Transactional
    public RespuestaEncuestaUsuarioDTO finalizarRespuestaEncuesta(Long respuestaEncuestaUsuarioId) {
        // Obtener la respuesta de encuesta
        RespuestaEncuestaUsuario respuesta = respuestaEncuestaUsuarioRepository.findById(respuestaEncuestaUsuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_ENCUESTA_USUARIO_NOT_FOUND));

        // Verificar que la respuesta no esté completada
        if (respuesta.getCompletada()) {
            throw new MidasExceptions(MidasErrorMessage.RESPUESTA_ENCUESTA_YA_COMPLETADA);
        }

        // Obtener todas las preguntas obligatorias de la encuesta
        List<PreguntaEncuesta> preguntasObligatorias = preguntaEncuestaRepository
                .findByEncuestaAndEstadoOrderByOrdenAsc(
                        respuesta.getEncuesta(), "A")
                .stream()
                .filter(PreguntaEncuesta::getEsObligatoria)
                .collect(Collectors.toList());

        // Obtener todas las respuestas del usuario
        List<DetalleRespuestaEncuestaUsuario> detallesRespuestas = detalleRespuestaEncuestaUsuarioRepository
                .findByRespuestaEncuestaUsuario(respuesta);

        // Verificar que se han respondido todas las preguntas obligatorias
        for (PreguntaEncuesta pregunta : preguntasObligatorias) {
            boolean respondida = detallesRespuestas.stream()
                    .anyMatch(detalle -> detalle.getPregunta().getId().equals(pregunta.getId()));

            if (!respondida) {
                throw new MidasExceptions(MidasErrorMessage.RESPUESTA_ENCUESTA_PREGUNTAS_OBLIGATORIAS);
            }
        }

        // Marcar como completada
        respuesta.setCompletada(true);
        respuesta.setFechaFin(LocalDateTime.now());
        respuesta = respuestaEncuestaUsuarioRepository.save(respuesta);

        // Retornar la respuesta completada con todos sus detalles
        return getRespuestaEncuestaUsuarioById(respuestaEncuestaUsuarioId);
    }

    @Override
    @Transactional(readOnly = true)
    public RespuestaEncuestaUsuarioDTO getRespuestaEncuestaUsuarioById(Long id) {
        // Obtener la respuesta de encuesta
        RespuestaEncuestaUsuario respuesta = respuestaEncuestaUsuarioRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_ENCUESTA_USUARIO_NOT_FOUND));

        // Obtener detalles de respuestas
        List<DetalleRespuestaEncuestaUsuario> detalles = detalleRespuestaEncuestaUsuarioRepository
                .findByRespuestaEncuestaUsuario(respuesta);

        // Convertir detalles a DTOs
        List<DetalleRespuestaEncuestaUsuarioDTO> detallesDTO = detalles.stream()
                .map(DetalleRespuestaEncuestaUsuarioMapper::toDTO)
                .collect(Collectors.toList());

        // Crear DTO completo
        return RespuestaEncuestaUsuarioMapper.toDTOWithDetalles(respuesta, detallesDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RespuestaEncuestaUsuarioDTO> getRespuestasByUsuarioAndEncuesta(Long usuarioId, Long encuestaId) {
        // Verificar que el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
        }

        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        // Obtener respuestas
        List<RespuestaEncuestaUsuario> respuestas = respuestaEncuestaUsuarioRepository
                .findByUsuarioIdAndEncuestaId(usuarioId, encuestaId);

        // Convertir a DTOs
        return respuestas.stream()
                .map(respuesta -> {
                    // Obtener detalles de respuestas
                    List<DetalleRespuestaEncuestaUsuario> detalles = detalleRespuestaEncuestaUsuarioRepository
                            .findByRespuestaEncuestaUsuario(respuesta);

                    // Convertir detalles a DTOs
                    List<DetalleRespuestaEncuestaUsuarioDTO> detallesDTO = detalles.stream()
                            .map(DetalleRespuestaEncuestaUsuarioMapper::toDTO)
                            .collect(Collectors.toList());

                    // Crear DTO completo
                    return RespuestaEncuestaUsuarioMapper.toDTOWithDetalles(respuesta, detallesDTO);
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<RespuestaEncuestaUsuarioDTO> getRespuestasByEncuesta(Long encuestaId) {
        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        // Obtener respuestas
        List<RespuestaEncuestaUsuario> respuestas = respuestaEncuestaUsuarioRepository.findByEncuestaId(encuestaId);

        // Convertir a DTOs (sin detalles para evitar sobrecarga)
        return respuestas.stream()
                .map(RespuestaEncuestaUsuarioMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasUsuarioCompletadoEncuesta(Long usuarioId, Long encuestaId) {
        // Verificar que el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
        }

        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        return respuestaEncuestaUsuarioRepository.existsByUsuarioIdAndEncuestaIdAndCompletadaTrue(usuarioId,
                encuestaId);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getEstadisticasRespuestas(Long encuestaId) {
        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        // Obtener estadísticas básicas
        Long totalRespuestas = respuestaEncuestaUsuarioRepository.countByEncuestaId(encuestaId);
        Long respuestasCompletadas = respuestaEncuestaUsuarioRepository.countCompletadasByEncuestaId(encuestaId);

        // Crear mapa de estadísticas
        Map<String, Object> estadisticas = new HashMap<>();
        estadisticas.put("totalRespuestas", totalRespuestas);
        estadisticas.put("respuestasCompletadas", respuestasCompletadas);

        return estadisticas;
    }
}
