import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  OnDestroy,
  Output,
  HostListener,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { Router } from '@angular/router';
import { User } from '@app/models/backend/user/index';
import { UserStatus } from '@app/models/backend/user/user-status.model';
import { NotificationService, ThemeService } from '@app/services';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { MatExpansionPanel } from '@angular/material/expansion';
import { Store, select } from '@ngrx/store';
import { Subscription } from 'rxjs';
import * as WebSocketActions from '@app/services/websocket/websocket.actions';
import * as UsersActions from '@app/services/user/user.actions';
import * as fromUser from '@app/store/user';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '@src/environments/environment';

@Component({
  selector: 'app-menu-list',
  templateUrl: './menu-list.component.html',
})
export class MenuListComponent implements OnInit, OnDestroy {
  isLandingOpen: boolean = false;

  @Output() menuToggle = new EventEmitter<void>();
  @Input() isAuthorized!: boolean | null;
  @Input() user: User | null = null;
  @Output() signOut = new EventEmitter<void>();
  @Output() closeMenuEvent = new EventEmitter<void>();

  @ViewChild('registroPanel') registroPanel!: MatExpansionPanel;
  @ViewChild('coordinadoresPanel') coordinadoresPanel!: MatExpansionPanel;
  @ViewChild('usuariosPanel') usuariosPanel!: MatExpansionPanel;
  @ViewChild('ventasPanel') ventasPanel!: MatExpansionPanel;

  isMenuOpen = false;
  usersStatus: UserStatus[] = [];
  userConnected: boolean = false;
  isDarkTheme: boolean = false;
  private subscription = new Subscription();

  constructor(
    private router: Router,
    private notification: NotificationService,
    private store: Store,
    private webSocketService: WebSocketService,
    private themeService: ThemeService,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    // Suscribirse al servicio de tema para detectar cambios en el modo oscuro
    this.subscription.add(
      this.themeService.darkMode$.subscribe((isDarkMode) => {
        this.isDarkTheme = isDarkMode;
      })
    );

    // No ejecutar checkToken ni conectar WebSocket si estamos en la página de login
    if (this.router.url.includes('/auth/login')) {
      console.log(
        'En página de login, no se ejecutará ninguna acción en MenuListComponent'
      );
      return;
    }

    // Verificar si hay un token válido
    const token = localStorage.getItem('token');
    if (!token) {
      console.log(
        'No hay token, no se ejecutarán acciones que requieran autenticación'
      );
      return;
    }

    // Verificar si hay un usuario válido
    const userId = localStorage.getItem('user')
      ? JSON.parse(localStorage.getItem('user') || '{}').id
      : null;
    if (!userId) {
      console.log(
        'No hay usuario válido, no se ejecutarán acciones que requieran ID de usuario'
      );
      return;
    }

    this.checkToken();

    // No conectamos el WebSocket aquí, ahora se hace en el login exitoso
    // y no necesitamos solicitar el estado de usuarios, ya se hace automáticamente

    // Suscribirse a cambios en el estado de usuarios
    this.subscription.add(
      this.store
        .select((state) => (state as any).userStatus.usersStatus)
        .subscribe((usersStatus: UserStatus[]) => {
          if (usersStatus) {
            this.usersStatus = usersStatus;
          }
        })
    );

    // Obtener el estado del usuario del localStorage
    const userStatus = localStorage.getItem('userStatus');
    if (userStatus && this.user) {
      this.userConnected = userStatus === 'ONLINE';
    }

    // Suscribirse a cambios en el estado del usuario desde el store
    this.subscription.add(
      this.store
        .pipe(select(fromUser.getUserStatus))
        .subscribe((status: string) => {
          this.userConnected = status === 'ONLINE';
        })
    );

    // Suscribirse a cambios en el estado de la conexión WebSocket
    this.subscription.add(
      this.webSocketService
        .getConnectionStatus()
        .subscribe((connected: boolean) => {
          if (!connected) {
            // Si se desconecta el WebSocket, actualizar el estado del usuario a OFFLINE
            this.store.dispatch(new fromUser.UpdateUserStatus('OFFLINE'));
            localStorage.setItem('userStatus', 'OFFLINE');
          }
        })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  @HostListener('window:resize')
  onResize(): void {
    if (window.innerWidth > 992) {
      this.isMenuOpen = false;
    }
  }

  private checkToken(): void {
    const token = localStorage.getItem('token');
    if (!token) {
      this.router.navigate(['/auth/login']);
      this.notification.error('No hay sesión activa');
      this.signOut.emit();
    }
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
    this.menuToggle.emit();
  }

  closeMenu(): void {
    this.closeMenuEvent.emit();
    this.isMenuOpen = false;
    this.menuToggle.emit();
  }

  // Variable para evitar múltiples cierres de sesión
  private isSigningOut = false;

  onSignOut(): void {
    // Evitar múltiples cierres de sesión simultáneos
    if (this.isSigningOut) {
      console.log(
        'MenuListComponent: Ya hay un proceso de cierre de sesión en curso'
      );
      return;
    }

    // Marcar como en proceso de cierre de sesión
    this.isSigningOut = true;
    console.log('MenuListComponent: Iniciando proceso de cierre de sesión');

    try {
      // Obtener el ID del usuario antes de limpiar localStorage
      const userStr = localStorage.getItem('user');
      let userId: number | null = null;

      if (userStr) {
        try {
          const userObj = JSON.parse(userStr);
          userId = userObj.id;
        } catch (e) {
          console.error('Error al parsear usuario:', e);
        }
      }

      // Desconectar el WebSocket directamente
      if (this.webSocketService.isConnected()) {
        console.log('MenuListComponent: Desconectando WebSocket');
        this.webSocketService.disconnect();
      }

      // Llamar al endpoint de cierre de sesión en el backend para liberar conexiones
      if (userId) {
        console.log(
          'MenuListComponent: Llamando al endpoint de cierre de sesión para el usuario',
          userId
        );

        // Obtener el token antes de limpiarlo
        const token = localStorage.getItem('token');

        if (token) {
          // Crear headers con el token de autorización
          const headers = new HttpHeaders({
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          });

          // Llamar al endpoint de cierre de sesión
          this.http
            .post(
              `${environment.url}api/authentication/sign-out`,
              { userId },
              { headers }
            )
            .subscribe({
              next: (response: any) => {
                console.log(
                  'MenuListComponent: Respuesta del endpoint de cierre de sesión:',
                  response
                );
                this.completeSignOut();
              },
              error: (error) => {
                console.error(
                  'MenuListComponent: Error al llamar al endpoint de cierre de sesión:',
                  error
                );
                this.completeSignOut();
              },
              complete: () => {
                this.isSigningOut = false;
              },
            });
        } else {
          this.completeSignOut();
          this.isSigningOut = false;
        }
      } else {
        // Si no hay ID de usuario, completar el cierre de sesión directamente
        this.completeSignOut();
        this.isSigningOut = false;
      }
    } catch (error) {
      console.error(
        'MenuListComponent: Error durante el cierre de sesión:',
        error
      );
      // Asegurar que se complete el cierre de sesión incluso en caso de error
      this.completeSignOut();
      this.isSigningOut = false;
    }
  }

  /**
   * Completa el proceso de cierre de sesión
   */
  private completeSignOut(): void {
    // Limpiar localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('coordinador');
    localStorage.removeItem('userStatus');
    localStorage.removeItem('asesores');
    localStorage.removeItem('darkMode');

    // Resetear la bandera global de WebSocket
    (window as any).wsConnectionInitiated = false;

    // Emitir evento de cierre de sesión
    this.signOut.emit();
    this.closeMenu();
  }

  isAdmin(): boolean {
    return this.user?.role === 'ADMIN' && !!localStorage.getItem('token');
  }

  isBackOffice(): boolean {
    return this.user?.role === 'BACKOFFICE' && !!localStorage.getItem('token');
  }
  isBackOfficeTramitador(): boolean {
    return (
      (this.user?.role === 'BACKOFFICETRAMITADOR' ||
        this.user?.role === 'BACKOFFICESEGUIMIENTO') &&
      !!localStorage.getItem('token')
    );
  }
  isCoordinador(): boolean {
    return this.user?.role === 'COORDINADOR' && !!localStorage.getItem('token');
  }

  isAsesor(): boolean {
    return this.user?.role === 'ASESOR' && !!localStorage.getItem('token');
  }

  isAuditor(): boolean {
    return this.user?.role === 'AUDITOR' && !!localStorage.getItem('token');
  }
  isPROGRAMADOR(): boolean {
    return this.user?.role === 'PROGRAMADOR' && !!localStorage.getItem('token');
  }

  isGERENCIA(): boolean {
    return this.user?.role === 'GERENCIA' && !!localStorage.getItem('token');
  }

  isPsicologo(): boolean {
    return this.user?.role === 'PSICOLOGO' && !!localStorage.getItem('token');
  }

  /**
   * Verifica si un usuario está conectado
   */
  isUserConnected(userId: number): boolean {
    // Si es el usuario actual, usar el estado del store
    if (this.user && userId === this.user.id) {
      return this.userConnected;
    }
    // Para otros usuarios, usar el estado de WebSocket
    return this.usersStatus.some(
      (status) => status.userId === userId && status.status === 'ONLINE'
    );
  }

  // Método para cerrar otros paneles cuando se abre uno
  closeOtherPanels(panel: MatExpansionPanel) {
    if (panel !== this.registroPanel) {
      this.registroPanel.close();
    }
    if (panel !== this.coordinadoresPanel) {
      this.coordinadoresPanel.close();
    }
    if (panel !== this.usuariosPanel) {
      this.usuariosPanel.close();
    }
    if (panel !== this.ventasPanel) {
      this.ventasPanel.close();
    }
  }
}
