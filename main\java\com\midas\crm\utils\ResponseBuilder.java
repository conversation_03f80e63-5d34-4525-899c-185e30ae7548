package com.midas.crm.utils;

import com.midas.crm.utils.GenericResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class ResponseBuilder {

    public static <T> ResponseEntity<GenericResponse<T>> buildErrorResponse(String message, T details) {
        return ResponseEntity.badRequest().body(
                new GenericResponse<>(
                        GenericResponseConstants.ERROR,
                        message,
                        details));
    }

    public static <T> ResponseEntity<GenericResponse<T>> buildSuccessResponse(String message, T data) {
        return ResponseEntity.ok(
                new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        message,
                        data));
    }

    public static <T> ResponseEntity<GenericResponse<T>> error(String message) {
        return ResponseEntity.badRequest().body(
                new GenericResponse<>(
                        GenericResponseConstants.ERROR,
                        message,
                        null));
    }

    public static <T> ResponseEntity<GenericResponse<T>> success(T data, String message) {
        return ResponseEntity.ok(
                new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        message,
                        data));
    }

    public static <T> ResponseEntity<GenericResponse<T>> notFound(String message) {
        return ResponseEntity.status(404).body(
                new GenericResponse<>(
                        GenericResponseConstants.ERROR,
                        message,
                        null));
    }

    public static <T> ResponseEntity<GenericResponse<T>> created(T data, String message) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new GenericResponse<>(1, message, data));
    }

    public static <T> ResponseEntity<GenericResponse<T>> error(MidasErrorMessage error) {
        return ResponseEntity.status(mapToHttpStatus(error))
                .body(new GenericResponse<>(0, error.getErrorMessage(), null));
    }

    public static <T> ResponseEntity<GenericResponse<T>> error(String message, T data) {
        return ResponseEntity.badRequest()
                .body(new GenericResponse<>(0, message, data));
    }

    private static HttpStatus mapToHttpStatus(MidasErrorMessage error) {
        return switch (error) {
            case USUARIO_NOT_FOUND, CLIENTERESIDENCIAL_NOT_FOUND,
                    COORDINADOR_NOT_FOUND, ASESOR_NOT_FOUND,
                    SEDE_NOT_FOUND, ANUNCIO_NOT_FOUND ->
                HttpStatus.NOT_FOUND;
            case USUARIO_ALREADY_EXISTS, CLIENTERESIDENCIAL_ALREADY_EXISTS,
                    COORDINADOR_ALREADY_EXISTS, ASESOR_ALREADY_EXISTS,
                    SEDE_ALREADY_EXISTS ->
                HttpStatus.CONFLICT;
            case USUARIO_INVALID_LOGIN -> HttpStatus.UNAUTHORIZED;
            case TOKEN_INVALIDO -> HttpStatus.FORBIDDEN;
            case ERROR_INTERNAL -> HttpStatus.INTERNAL_SERVER_ERROR;
            default -> HttpStatus.BAD_REQUEST;
        };
    }
}