package com.midas.crm.controller;

import com.midas.crm.entity.DTO.cuestionario.PreguntaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaUpdateDTO;
import com.midas.crm.service.PreguntaService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.pregunta}")
@RequiredArgsConstructor
@Slf4j
public class PreguntaController {

    private final PreguntaService preguntaService;

    /**
     * Crea una nueva pregunta
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<PreguntaDTO>> createPregunta(@Valid @RequestBody PreguntaCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(preguntaService::createPregunta)
            .map(pregunta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta creada exitosamente", pregunta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las preguntas
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<PreguntaDTO>>> listPreguntas() {
        List<PreguntaDTO> preguntas = preguntaService.listPreguntas();
        return ResponseEntity.ok(
            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de preguntas", preguntas)
        );
    }

    /**
     * Obtiene las preguntas por cuestionario
     * Implementado con programación funcional
     */
    @GetMapping("/cuestionario/{cuestionarioId}")
    public ResponseEntity<GenericResponse<List<PreguntaDTO>>> listPreguntasByCuestionarioId(@PathVariable Long cuestionarioId) {
        return Optional.ofNullable(cuestionarioId)
            .map(preguntaService::listPreguntasByCuestionarioId)
            .map(preguntas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de preguntas por cuestionario", preguntas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una pregunta por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<PreguntaDTO>> getPregunta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(preguntaService::getPreguntaById)
            .map(pregunta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta encontrada", pregunta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Actualiza una pregunta existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<PreguntaDTO>> updatePregunta(@PathVariable Long id, @Valid @RequestBody PreguntaUpdateDTO dto) {
        return Optional.ofNullable(dto)
            .map(updateDto -> preguntaService.updatePregunta(id, updateDto))
            .map(pregunta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta actualizada exitosamente", pregunta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina una pregunta
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Object>> deletePregunta(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().build();
        }

        preguntaService.deletePregunta(id);
        return ResponseEntity.ok(
            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta eliminada exitosamente", null)
        );
    }
}
