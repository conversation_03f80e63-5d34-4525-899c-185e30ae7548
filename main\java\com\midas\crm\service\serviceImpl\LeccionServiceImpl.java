package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.leccion.LeccionCreateDTO;
import com.midas.crm.entity.DTO.leccion.LeccionDTO;
import com.midas.crm.entity.DTO.leccion.LeccionUpdateDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.Seccion;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.LeccionMapper;
import com.midas.crm.repository.CuestionarioRepository;
import com.midas.crm.repository.LeccionRepository;
import com.midas.crm.repository.SeccionRepository;
import com.midas.crm.repository.ProgresoUsuarioRepository;
import com.midas.crm.service.LeccionService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LeccionServiceImpl implements LeccionService {

    private final LeccionRepository leccionRepository;
    private final SeccionRepository seccionRepository;
    private final CuestionarioRepository cuestionarioRepository;
    private final ProgresoUsuarioRepository progresoUsuarioRepository;

    @Override
    @Transactional
    public LeccionDTO createLeccion(LeccionCreateDTO dto) {
        // Verificar si ya existe una lección con el mismo título en la misma sección
        if (leccionRepository.existsByTituloAndSeccionId(dto.getTitulo(), dto.getSeccionId())) {
            throw new MidasExceptions(MidasErrorMessage.LECCION_ALREADY_EXISTS);
        }

        // Obtener la sección
        Seccion seccion = seccionRepository.findById(dto.getSeccionId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SECCION_NOT_FOUND));

        // Crear la lección
        Leccion leccion = LeccionMapper.toEntity(dto, seccion);

        return LeccionMapper.toDTO(leccionRepository.save(leccion));
    }

    @Override
    @Transactional(readOnly = true)
    public List<LeccionDTO> listLecciones() {
        return leccionRepository.findAll().stream()
                .map(LeccionMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<LeccionDTO> listLeccionesBySeccionId(Long seccionId) {
        return leccionRepository.findBySeccionIdOrderByOrdenAsc(seccionId).stream()
                .map(LeccionMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<LeccionDTO> listLeccionesByModuloId(Long moduloId) {
        return leccionRepository.findAllByModuloIdOrderBySeccionOrdenAndLeccionOrden(moduloId).stream()
                .map(LeccionMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<LeccionDTO> listLeccionesByCursoId(Long cursoId) {
        return leccionRepository.findAllByCursoIdOrderByModuloOrdenAndSeccionOrdenAndLeccionOrden(cursoId).stream()
                .map(LeccionMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public LeccionDTO getLeccionById(Long id) {
        return leccionRepository.findById(id)
                .map(LeccionMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));
    }

    @Override
    @Transactional
    public LeccionDTO updateLeccion(Long id, LeccionUpdateDTO dto) {
        Leccion leccion = leccionRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));

        // Código corregido
        LeccionMapper.updateEntityFromDTO(leccion, dto);

        return LeccionMapper.toDTO(leccionRepository.save(leccion));
    }

    @Override
    @Transactional
    public void deleteLeccion(Long id) {
        Leccion leccion = leccionRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));

        try {
            // Primero, verificar si hay un cuestionario asociado y eliminarlo
            if (cuestionarioRepository.existsByLeccionId(id)) {
                cuestionarioRepository.findByLeccionId(id).ifPresent(cuestionario -> {
                    // Eliminar el cuestionario (esto también eliminará preguntas y respuestas)
                    cuestionarioRepository.delete(cuestionario);
                });
            }

            // Eliminar los registros de progreso asociados a esta lección
            progresoUsuarioRepository.findAll().stream()
                    .filter(progreso -> progreso.getLeccion().getId().equals(id))
                    .forEach(progresoUsuarioRepository::delete);

            // Finalmente, eliminar la lección
            leccionRepository.delete(leccion);
        } catch (Exception e) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL, "Error al eliminar la lección: " + e.getMessage());
        }
    }
}
