package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entidad que representa una encuesta para el área de psicología
 */
@Entity
@Table(name = "encuestas")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Encuesta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 100)
    private String titulo;

    @Column(length = 500)
    private String descripcion;

    @Column(name = "fecha_inicio")
    private LocalDateTime fechaInicio;

    @Column(name = "fecha_fin")
    private LocalDateTime fechaFin;

    @Column(name = "tiempo_limite")
    private Integer tiempoLimite; // Tiempo límite en minutos (null = sin límite)

    @Column(name = "es_anonima")
    private Boolean esAnonima = false; // Indica si la encuesta es anónima

    @Column(name = "mostrar_resultados")
    private Boolean mostrarResultados = false; // Indica si se muestran los resultados al finalizar

    @Column(name = "tipo_asignacion", nullable = false)
    @Enumerated(EnumType.STRING)
    private TipoAsignacion tipoAsignacion = TipoAsignacion.TODOS; // Tipo de asignación de la encuesta

    // Relación con la sede (si tipoAsignacion = SEDE)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sede_id")
    private Sede sede;

    // Relación con el coordinador (si tipoAsignacion = COORDINACION)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "coordinador_id")
    private User coordinador;

    // Relación con el usuario específico (si tipoAsignacion = PERSONAL)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id")
    private User usuario;

    // Relación con el usuario que crea la encuesta
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creador_id", nullable = false)
    private User creador;

    @OneToMany(mappedBy = "encuesta", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PreguntaEncuesta> preguntas = new ArrayList<>();

    @Column(nullable = false, length = 1)
    private String estado = "A"; // A: Activo, I: Inactivo

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;

    /**
     * Enum que define los tipos de asignación de encuestas
     */
    public enum TipoAsignacion {
        TODOS,          // Encuesta para todos los usuarios
        SEDE,           // Encuesta para usuarios de una sede específica
        COORDINACION,   // Encuesta para usuarios de un coordinador específico
        PERSONAL        // Encuesta para un usuario específico
    }
}
