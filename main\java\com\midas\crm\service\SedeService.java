package com.midas.crm.service;


import com.midas.crm.entity.DTO.SedeDTO;
import com.midas.crm.entity.DTO.SedePaginadoResponse;
import com.midas.crm.entity.Sede;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface SedeService {

    /**
     * Guarda una nueva sede
     * @param sedeDTO datos de la sede a guardar
     * @return la sede guardada
     */
    SedeDTO guardarSede(SedeDTO sedeDTO);

    /**
     * Actualiza una sede existente
     * @param id identificador de la sede
     * @param sedeDTO datos actualizados de la sede
     * @return la sede actualizada
     */
    SedeDTO actualizarSede(Long id, SedeDTO sedeDTO);

    /**
     * Obtiene una sede por su ID
     * @param id identificador de la sede
     * @return la sede encontrada o vacío si no existe
     */
    Optional<SedeDTO> obtenerSedePorId(Long id);

    /**
     * Obtiene todas las sedes
     * @return lista de todas las sedes
     */
    List<SedeDTO> obtenerTodasLasSedes();

    /**
     * Obtiene todas las sedes activas
     * @return lista de sedes activas
     */
    List<SedeDTO> obtenerSedesActivas();

    /**
     * Elimina una sede (baja lógica)
     * @param id identificador de la sede
     * @return true si se eliminó correctamente
     */
    boolean eliminarSede(Long id);

    /**
     * Obtiene sedes paginadas con posibilidad de búsqueda
     * @param search término de búsqueda (opcional)
     * @param pageable configuración de paginación
     * @return respuesta paginada con sedes
     */
    SedePaginadoResponse obtenerSedesPaginadas(String search, Pageable pageable);

    /**
     * Obtiene una sede por su nombre
     * @param nombre nombre de la sede
     * @return la sede encontrada o vacío si no existe
     */
    Optional<Sede> obtenerSedePorNombre(String nombre);
}
