package com.midas.crm.config;

import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.datatype.hibernate5.jakarta.Hibernate5JakartaModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuración del módulo Hibernate para Jackson
 * Permite la serialización adecuada de entidades Hibernate
 */
@Configuration
public class HibernateModule {

    /**
     * Crea un módulo Hibernate para Jackson que maneja correctamente
     * las referencias lazy y otras características específicas de Hibernate
     *
     * @return Módulo configurado para <PERSON>
     */
    @Bean
    public Module hibernateJacksonModule() {
        Hibernate5JakartaModule module = new Hibernate5JakartaModule();
        // Configurar para que no falle en propiedades lazy no inicializadas
        module.configure(Hibernate5JakartaModule.Feature.FORCE_LAZY_LOADING, false);
        return module;
    }
}
