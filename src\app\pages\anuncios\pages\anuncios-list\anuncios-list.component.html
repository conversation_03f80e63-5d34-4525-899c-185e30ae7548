<div class="anuncios-container">
  <div class="header">
    <h2>Listado de anuncios</h2>
    <div class="actions">
      <div class="search-field">
        <input placeholder="Buscar" [(ngModel)]="searchTerm">
        <mat-icon>search</mat-icon>
      </div>
      <button mat-raised-button class="nuevo-btn" (click)="openNuevoAnuncioDialog()">
        <mat-icon>add</mat-icon>
        Nuevo anuncio
      </button>
      <button mat-raised-button class="refresh-btn" (click)="recargarAnuncios()" title="Recargar anuncios">
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <!-- Indicador de carga -->
  <div class="loading-container" *ngIf="loading | async">
    <app-spinner></app-spinner>
    <p class="loading-text">Cargando anuncios...</p>
  </div>

  <!-- Mensaje de error -->
  <ng-container *ngIf="error | async as errorState">
    <div class="error-container" *ngIf="errorState.error">
      <mat-card class="error-card">
        <mat-card-content>
          <mat-icon class="error-icon">error</mat-icon>
          <p class="error-message">{{ errorState.message }}</p>
          <button mat-raised-button color="primary" (click)="recargarAnuncios()" class="faq-add-button">
            <mat-icon>refresh</mat-icon> Intentar nuevamente
          </button>
        </mat-card-content>
      </mat-card>
    </div>
  </ng-container>

  <!-- Indicador de modo de respaldo HTTP -->
  <div class="fallback-indicator" *ngIf="usingHttpFallback">
    <mat-icon>warning</mat-icon>
    <span>Usando modo de respaldo (sin actualizaciones en tiempo real)</span>
  </div>

  <div class="anuncios-grid" *ngIf="anuncios$ | async as anuncios">
    <mat-card class="anuncio-card" *ngFor="let anuncio of anuncios | filter:searchTerm">
      <div class="clickable-image" (click)="openImageViewer(anuncio.imagenUrl || pictureDefault, anuncio.titulo)">
        <img [src]="anuncio.imagenUrl || pictureDefault" [alt]="anuncio.titulo">
      </div>
      <mat-card-content>
        <h3>{{anuncio.titulo}}</h3>
        <div class="tipo-badge" [ngClass]="anuncio.categoria"></div>
        <p class="descripcion">{{anuncio.descripcion}}</p>

        <!-- Información del anuncio -->
        <div class="anuncio-info">
          <!-- Categoría -->
          <div class="info-item categoria-container">
            <mat-icon>category</mat-icon>
            <span>{{anuncio.categoria}}</span>
          </div>

          <!-- Estado del anuncio -->
          <div class="info-item estado-container">
            <mat-icon>{{ (anuncio.estado || 'ACTIVO') === 'ACTIVO' ? 'check_circle' : 'cancel' }}</mat-icon>
            <div class="estado-badge" [ngClass]="anuncio.estado || 'ACTIVO'">
              {{ anuncio.estado || 'ACTIVO' }}
            </div>
          </div>

          <!-- Orden de visualización -->
          <div class="info-item orden-container" *ngIf="anuncio.orden !== undefined && anuncio.orden !== null">
            <mat-icon>sort</mat-icon>
            <span>Orden: {{ anuncio.orden }}</span>
          </div>

          <!-- Sede del anuncio -->
          <div class="info-item sede-container" *ngIf="anuncio.sedeId || anuncio.nombreSede">
            <mat-icon>location_city</mat-icon>
            <span>Sede: {{ anuncio.nombreSede || (anuncio.sedeId ? 'ID: ' + anuncio.sedeId : 'Todas') }}</span>
          </div>
          <div class="info-item sede-container" *ngIf="!anuncio.sedeId && !anuncio.nombreSede">
            <mat-icon>public</mat-icon>
            <span>Sede: Todas</span>
          </div>
        </div>

        <!-- Fechas de vigencia -->
        <div class="fechas-container">
          <div class="fecha-header">
            <mat-icon>date_range</mat-icon>
            <span>Vigencia</span>
          </div>

          <div class="fechas-grid">
            <div class="fecha-item" *ngIf="anuncio.fechaInicio">
              <span class="fecha-label">Desde:</span>
              <span class="fecha-value">{{ formatDate(anuncio.fechaInicio) }}</span>
            </div>

            <div class="fecha-item" *ngIf="anuncio.fechaFin">
              <span class="fecha-label">Hasta:</span>
              <span class="fecha-value">{{ formatDate(anuncio.fechaFin) }}</span>
            </div>

            <div class="fecha-item" *ngIf="!anuncio.fechaInicio">
              <span class="fecha-label">Desde:</span>
              <span class="fecha-value">No definida</span>
            </div>

            <div class="fecha-item" *ngIf="!anuncio.fechaFin">
              <span class="fecha-label">Hasta:</span>
              <span class="fecha-value">No definida</span>
            </div>
          </div>
        </div>
      </mat-card-content>
      <div class="card-actions">
        <button mat-button class="action-btn update" (click)="openEditarDialog(anuncio)">Actualizar</button>
        <button mat-button class="action-btn delete" (click)="eliminarAnuncio(anuncio.id)">
          <mat-icon>delete</mat-icon>
        </button>
      </div>
    </mat-card>
  </div>

  <ng-container *ngIf="totalElements$ | async as total">
    <div class="paginator-container">
      <mat-paginator
        *ngIf="total > 0"
        [length]="total"
        [pageSize]="pageSize"
        [pageIndex]="(currentPage$ | async) || 0"
        [pageSizeOptions]="[5, 8, 10, 20, 50]"
        [hidePageSize]="false"
        [showFirstLastButtons]="true"
        (page)="onPageChange($event.pageIndex, $event.pageSize)"
        aria-label="Seleccionar página">
      </mat-paginator>
    </div>
  </ng-container>
</div>

<ng-template #formDialog>
  <app-anuncios-nuevo></app-anuncios-nuevo>
</ng-template>