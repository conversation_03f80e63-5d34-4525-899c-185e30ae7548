<div class="asignar-usuarios-container" [ngClass]="{'dark-theme': false}">
  <h2 mat-dialog-title>Asignar Usuarios al Curso: {{ curso.nombre }}</h2>

  <mat-dialog-content>
    <div class="search-container">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Buscar usuarios</mat-label>
        <input matInput [formControl]="searchControl" placeholder="Buscar por username, nombre, DNI, email o rol">
        <mat-icon *ngIf="!searchLoading && !searchControl.value" matSuffix>search</mat-icon>
        <mat-spinner *ngIf="searchLoading" matSuffix diameter="20"></mat-spinner>
        <button *ngIf="searchControl.value && !searchLoading" matSuffix mat-icon-button aria-label="Limpiar" (click)="clearSearch()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
    </div>

    <!-- <PERSON><PERSON><PERSON> de b<PERSON> -->
    <div *ngIf="searchTerm && filteredUsuarios.length > 0" class="search-results-info">
      <mat-icon>info</mat-icon>
      <span>Mostrando {{ filteredUsuarios.length }} resultados para "{{ searchTerm }}"</span>
    </div>

    <!-- Mensaje de no resultados -->
    <div *ngIf="searchTerm && filteredUsuarios.length === 0 && !searchLoading" class="no-results">
      <mat-icon>search_off</mat-icon>
      <span>No se encontraron usuarios para "{{ searchTerm }}"</span>
      <button mat-button color="primary" (click)="clearSearch()">Limpiar búsqueda</button>
    </div>

    <div *ngIf="loading && !searchLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Cargando usuarios...</p>
    </div>

    <div *ngIf="error" class="error-message">
      <mat-icon>error</mat-icon>
      <span>{{ error }}</span>
    </div>

    <div *ngIf="!loading || searchTerm" class="usuarios-list-container">
      <!-- Mensaje cuando no hay usuarios -->
      <div *ngIf="filteredUsuarios.length === 0 && !searchTerm && !loading" class="no-results">
        <mat-icon>people_outline</mat-icon>
        <span>No hay usuarios disponibles</span>
        <button mat-button color="primary" (click)="loadUsuarios()">Cargar usuarios</button>
      </div>

      <mat-selection-list #selectionList (selectionChange)="onSelectionChange($event)" multiple>
        <mat-list-option *ngFor="let usuario of filteredUsuarios; "
                         [value]="usuario.id"
                         [selected]="isUsuarioAsignado(usuario.id)"
                         [checkboxPosition]="'before'">
          <div class="usuario-item">
            <div class="usuario-avatar">
              <div class="avatar-circle" [ngStyle]="{'background-color': getAvatarColor(usuario.username)}">
                {{ getInitials(usuario.nombre, usuario.apellido) }}
              </div>
            </div>
            <div class="usuario-info">
              <div class="usuario-nombre-container">
                <span class="usuario-nombre">{{ usuario.nombre || '' }} {{ usuario.apellido || '' }}</span>
                <span class="usuario-username">({{ usuario.username }})</span>
              </div>
              <div class="usuario-detalles">
                <span class="usuario-email">{{ usuario.email }}</span>
                <span class="usuario-dni">{{ usuario.dni }}</span>
                <span class="usuario-sede" *ngIf="usuario.sede">{{ usuario.sede }}</span>
              </div>
              <div class="usuario-rol-container">
                <span class="usuario-rol" [ngClass]="'role-' + usuario.role?.toLowerCase()">{{ usuario.role }}</span>
              </div>
            </div>
            <div class="usuario-status">
              <mat-icon *ngIf="isUsuarioAsignado(usuario.id)" class="asignado-icon" color="primary">check_circle</mat-icon>
            </div>
          </div>
        </mat-list-option>
      </mat-selection-list>

      <div class="seleccion-info">
        <p>Usuarios seleccionados: {{ usuariosSeleccionados.length }}</p>
        <p>Total de usuarios disponibles: {{ totalItems }}</p>
        <p *ngIf="!allUsersLoaded && !searchTerm && !loading" class="loading-more">
          <button mat-button color="primary" (click)="loadMoreUsuarios()">
            <mat-icon>refresh</mat-icon> Cargar más usuarios
          </button>
        </p>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" [disabled]="loading">Cancelar</button>
    <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="loading">
      <mat-icon>save</mat-icon>
      <span *ngIf="!loading">Guardar</span>
      <span *ngIf="loading">Guardando...</span>
    </button>
  </mat-dialog-actions>
</div>
