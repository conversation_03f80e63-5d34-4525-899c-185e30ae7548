package com.midas.crm.controller;

import com.midas.crm.entity.DTO.encuesta.EncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.EncuestaDTO;
import com.midas.crm.entity.DTO.encuesta.EncuestaUpdateDTO;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.EncuestaService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.encuestas}")
@RequiredArgsConstructor
@Slf4j
public class EncuestaController {

    private final EncuestaService encuestaService;

    /**
     * Crea una nueva encuesta
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<EncuestaDTO>> createEncuesta(
            @Valid @RequestBody EncuestaCreateDTO dto,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        return Optional.ofNullable(dto)
            .map(encuestaDTO -> encuestaService.createEncuesta(encuestaDTO, userPrincipal.getId()))
            .map(encuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuesta creada exitosamente", encuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una encuesta por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<EncuestaDTO>> getEncuesta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(encuestaService::getEncuestaById)
            .map(encuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuesta encontrada", encuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una encuesta completa (con preguntas y opciones) por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}/completa")
    public ResponseEntity<GenericResponse<EncuestaDTO>> getEncuestaCompleta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(encuestaService::getEncuestaCompleta)
            .map(encuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuesta completa encontrada", encuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Actualiza una encuesta existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<EncuestaDTO>> updateEncuesta(
            @PathVariable Long id,
            @Valid @RequestBody EncuestaUpdateDTO dto) {
        
        return Optional.ofNullable(dto)
            .map(encuestaDTO -> encuestaService.updateEncuesta(id, encuestaDTO))
            .map(encuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuesta actualizada exitosamente", encuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina una encuesta (cambio de estado a inactivo)
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Void>> deleteEncuesta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(encuestaId -> {
                encuestaService.deleteEncuesta(encuestaId);
                return ResponseEntity.ok(
                    new GenericResponse<Void>(GenericResponseConstants.SUCCESS, "Encuesta eliminada exitosamente", null)
                );
            })
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las encuestas
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<EncuestaDTO>>> getAllEncuestas() {
        return Optional.of(encuestaService.getAllEncuestas())
            .map(encuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuestas encontradas", encuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las encuestas activas
     * Implementado con programación funcional
     */
    @GetMapping("/activas")
    public ResponseEntity<GenericResponse<List<EncuestaDTO>>> getAllEncuestasActivas() {
        return Optional.of(encuestaService.getAllEncuestasActivas())
            .map(encuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuestas activas encontradas", encuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las encuestas disponibles para un usuario específico
     * Implementado con programación funcional
     */
    @GetMapping("/disponibles")
    public ResponseEntity<GenericResponse<List<EncuestaDTO>>> getEncuestasDisponiblesParaUsuario(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        return Optional.of(userPrincipal.getId())
            .map(encuestaService::getEncuestasDisponiblesParaUsuario)
            .map(encuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuestas disponibles encontradas", encuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las encuestas creadas por un usuario específico
     * Implementado con programación funcional
     */
    @GetMapping("/creadas")
    public ResponseEntity<GenericResponse<List<EncuestaDTO>>> getEncuestasByCreador(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        return Optional.of(userPrincipal.getId())
            .map(encuestaService::getEncuestasByCreador)
            .map(encuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuestas creadas encontradas", encuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las encuestas con paginación y filtro
     * Implementado con programación funcional
     */
    @GetMapping("/pageable")
    public ResponseEntity<GenericResponse<Page<EncuestaDTO>>> getEncuestasPageable(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("asc") ? Sort.by(sortBy).ascending() : Sort.by(sortBy).descending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        return Optional.of(encuestaService.getEncuestasPageable(search, pageable))
            .map(encuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Encuestas encontradas", encuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene estadísticas generales de una encuesta
     * Implementado con programación funcional
     */
    @GetMapping("/{id}/estadisticas")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getEstadisticasEncuesta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(encuestaService::getEstadisticasEncuesta)
            .map(estadisticas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Estadísticas encontradas", estadisticas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }
}
