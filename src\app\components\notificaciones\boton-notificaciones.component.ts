import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { Subscription } from 'rxjs';
import { NotificacionesWsService } from '@app/services/notificaciones/notificaciones-ws.service';

@Component({
  selector: 'app-boton-notificaciones',
  templateUrl: './boton-notificaciones.component.html',
})
export class BotonNotificacionesComponent implements OnInit, OnDestroy {
  contadorNoLeidas = 0;
  private subscripciones: Subscription[] = [];
  @ViewChild(MatMenuTrigger) menuTrigger: MatMenuTrigger;

  constructor(private notificacionesService: NotificacionesWsService) {}

  ngOnInit(): void {
    console.log('Inicializando BotonNotificacionesComponent');

    // Limpiar cualquier suscripción existente
    this.subscripciones.forEach((sub) => sub.unsubscribe());
    this.subscripciones = [];

    // Verificar si el servicio ya está inicializado
    if (!this.notificacionesService.isInitialized()) {
      console.log('Inicializando servicio de notificaciones desde el botón');
      // Configurar suscripciones WebSocket
      this.notificacionesService.setupSubscriptions();
    } else {
      console.log('Servicio de notificaciones ya inicializado');
    }

    // Suscribirse al contador de no leídas
    this.subscripciones.push(
      this.notificacionesService.getContadorNoLeidas().subscribe((contador) => {
        // Usar setTimeout para evitar ExpressionChangedAfterItHasBeenCheckedError
        setTimeout(() => {
          console.log(
            'Contador de notificaciones no leídas actualizado:',
            contador
          );
          this.contadorNoLeidas = contador;
        }, 0);
      })
    );
  }

  ngOnDestroy(): void {
    console.log('Destruyendo BotonNotificacionesComponent');
    // Cancelar todas las suscripciones
    this.subscripciones.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Carga las notificaciones desde el servidor
   */
  cargarNotificaciones(): void {
    console.log('Solicitando notificaciones desde el botón');
    this.notificacionesService.solicitarNotificaciones();
  }
}
