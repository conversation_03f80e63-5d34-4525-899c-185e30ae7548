package com.midas.crm.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "faq_respuestas")
@Data
public class FaqRespuesta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String contenido;

    // Usuario que crea la respuesta
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id", referencedColumnName = "codi_usuario")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "asesores", "coordinador"})
    private User usuario;

    // FAQ al que pertenece esta respuesta
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "faq_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "respuestas"})
    private Faq faq;

    // Archivos adjuntos a esta respuesta
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "faq_respuesta_id")
    private List<FileFaqRespuesta> archivos = new ArrayList<>();

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
