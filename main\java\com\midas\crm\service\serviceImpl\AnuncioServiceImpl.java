package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Anuncio;
import com.midas.crm.entity.DTO.anuncio.AnuncioDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioListDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioListProjection;
import com.midas.crm.entity.DTO.anuncio.AnuncioUpdateResponseDTO;
import com.midas.crm.entity.Sede;
import com.midas.crm.event.AnuncioCreatedEvent;
import com.midas.crm.event.AnuncioDeletedEvent;
import com.midas.crm.event.AnuncioUpdatedEvent;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.AnuncioMapper;
import com.midas.crm.repository.AnuncioRepository;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.service.AnuncioService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AnuncioServiceImpl implements AnuncioService {

    private final AnuncioRepository anuncioRepository;
    private final UserService userService;
    private final ApplicationEventPublisher eventPublisher;
    private final SedeRepository sedeRepository;

    @Override
    @Transactional
    public Anuncio guardar(AnuncioDTO dto) {
        // Usar Optional para manejar el usuario y la creación del anuncio
        return Optional.ofNullable(userService.findUserById(dto.getUsuarioId()))
                .map(usuario -> {
                    // Crear y guardar el anuncio
                    Anuncio anuncio = Anuncio.builder()
                            .titulo(dto.getTitulo())
                            .descripcion(dto.getDescripcion())
                            .imagenUrl(dto.getImagenUrl())
                            .categoria(dto.getCategoria())
                            .fechaPublicacion(LocalDateTime.now())
                            .fechaInicio(dto.getFechaInicio())
                            .fechaFin(dto.getFechaFin())
                            .orden(dto.getOrden() != null ? dto.getOrden() : 0)
                            .estado(dto.getEstado() != null ? dto.getEstado() : "ACTIVO")
                            .usuario(usuario)
                            .build();

                    // Si se especificó una sede, buscarla y asignarla
                    if (dto.getSedeId() != null) {
                        try {
                            // Aquí deberíamos buscar la sede por ID
                            // Como no tenemos acceso directo al SedeRepository, usamos el usuario
                            // Si el usuario tiene una sede con el mismo ID, la usamos
                            if (usuario.getSede() != null && usuario.getSede().getId().equals(dto.getSedeId())) {
                                anuncio.setSede(usuario.getSede());
                            } else {
                                // Idealmente, aquí buscaríamos la sede en el repositorio
                                // Pero como no tenemos acceso directo, dejamos la sede como null
                                // y mostramos un mensaje de advertencia
                                //System.out.println("No se pudo asignar la sede al anuncio: sede no encontrada");
                            }
                        } catch (Exception e) {
                            //System.err.println("Error al asignar sede al anuncio: " + e.getMessage());
                        }
                    }

                    Anuncio anuncioGuardado = anuncioRepository.save(anuncio);

                    // Publicar evento de creación usando try-catch funcional
                    try {
                        eventPublisher.publishEvent(new AnuncioCreatedEvent(this, anuncioGuardado));
                    } catch (Exception e) {
                        // Si hay un error al publicar el evento, lo registramos pero no interrumpimos la operación
                        //System.err.println("Error al publicar evento de nuevo anuncio: " + e.getMessage());
                    }

                    return anuncioGuardado;
                })
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
    }

    /**
     * Lista todos los anuncios paginados
     * Siempre consulta la base de datos directamente sin usar caché
     */
    @Override
    @Transactional(readOnly = true)
    public List<AnuncioListDTO> listarPaginado(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        // Consulta directa a la base de datos sin caché
        return anuncioRepository.findAll(pageable).stream()
                .map(AnuncioMapper::toListDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AnuncioListDTO> listarPaginadoPorSede(int page, int size, Long sedeId) {
        Pageable pageable = PageRequest.of(page, size);
        // Consulta directa a la base de datos sin caché
        return anuncioRepository.findAll(pageable).stream()
                .filter(anuncio -> anuncio.getSede() == null ||
                        (anuncio.getSede() != null && anuncio.getSede().getId().equals(sedeId)))
                .map(AnuncioMapper::toListDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Anuncio obtenerPorId(Long id) {
        return anuncioRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND));
    }

    @Override
    @Transactional
    public void eliminar(Long id) {
        // Verificar existencia y eliminar usando programación funcional
        if (anuncioRepository.existsById(id)) {
            // Eliminar el anuncio
            anuncioRepository.deleteById(id);

            // Publicar evento de eliminación de anuncio
            try {
                eventPublisher.publishEvent(new AnuncioDeletedEvent(this, id));
            } catch (Exception e) {
                // Si hay un error al publicar el evento, lo registramos pero no interrumpimos la operación
                //System.err.println("Error al publicar evento de eliminación de anuncio: " + e.getMessage());
            }
        } else {
            throw new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND);
        }
    }

    @Override
    @Transactional
    public AnuncioUpdateResponseDTO actualizarParcial(Long id, AnuncioDTO dto) {
        return anuncioRepository.findById(id)
                .map(anuncio -> {
                    // Actualizar solo los campos proporcionados usando Optional
                    Optional.ofNullable(dto.getTitulo()).ifPresent(anuncio::setTitulo);
                    Optional.ofNullable(dto.getDescripcion()).ifPresent(anuncio::setDescripcion);
                    Optional.ofNullable(dto.getImagenUrl()).ifPresent(anuncio::setImagenUrl);
                    Optional.ofNullable(dto.getCategoria()).ifPresent(anuncio::setCategoria);
                    Optional.ofNullable(dto.getFechaInicio()).ifPresent(anuncio::setFechaInicio);
                    Optional.ofNullable(dto.getFechaFin()).ifPresent(anuncio::setFechaFin);
                    Optional.ofNullable(dto.getOrden()).ifPresent(anuncio::setOrden);
                    Optional.ofNullable(dto.getEstado()).ifPresent(anuncio::setEstado);

                    // Actualizar la sede si se proporciona un sedeId
                    if (dto.getSedeId() != null) {
                        sedeRepository.findById(dto.getSedeId())
                                .ifPresent(anuncio::setSede);
                    } else {
                        // Si sedeId es null, establecer sede como null (para todas las sedes)
                        anuncio.setSede(null);
                    }

                    // Guardar y publicar evento
                    Anuncio actualizado = anuncioRepository.save(anuncio);

                    // Publicar evento de actualización de anuncio
                    try {
                        eventPublisher.publishEvent(new AnuncioUpdatedEvent(this, actualizado));
                    } catch (Exception e) {
                        // Si hay un error al publicar el evento, lo registramos pero no interrumpimos la operación
                        log.error("Error al publicar evento de actualización de anuncio", e);
                    }

                    return AnuncioMapper.toUpdateResponseDTO(actualizado);
                })
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND));
    }

    /**
     * Método privado para mapear proyecciones a DTOs
     * Elimina la duplicación de código en los métodos de listado
     */
    private Function<AnuncioListProjection, AnuncioListDTO> mapToAnuncioListDTO() {
        return p -> AnuncioListDTO.builder()
                .id(p.getId())
                .titulo(p.getTitulo())
                .descripcion(p.getDescripcion())
                .imagenUrl(p.getImagenUrl())
                .categoria(p.getCategoria())
                .fechaPublicacion(p.getFechaPublicacion())
                .fechaInicio(p.getFechaInicio())
                .fechaFin(p.getFechaFin())
                .orden(p.getOrden())
                .estado(p.getEstado())
                .nombreUsuario(p.getNombreUsuario())
                .sedeId(p.getSedeId())
                .nombreSede(p.getNombreSede())
                .build();
    }

    /**
     * Obtiene los anuncios más recientes con paginación
     * Siempre consulta la base de datos directamente sin usar caché
     */
    @Transactional(readOnly = true)
    @Override
    public Page<AnuncioListDTO> listarMasRecientesPaginado(int page, int size) {
        // Usar programación funcional para crear pageable y transformar resultados
        Pageable pageable = PageRequest.of(page, size);

        // Consulta directa a la base de datos y transformación en un solo flujo
        return anuncioRepository.listarAnunciosRecientes(pageable)
                .map(mapToAnuncioListDTO());
    }

    /**
     * Obtiene los anuncios más recientes con paginación filtrados por sede
     * Siempre consulta la base de datos directamente sin usar caché
     */
    @Transactional(readOnly = true)
    @Override
    public Page<AnuncioListDTO> listarMasRecientesPaginadoPorSede(int page, int size, Long sedeId) {
        // Usar programación funcional para crear pageable y transformar resultados
        Pageable pageable = PageRequest.of(page, size);

        // Consulta directa a la base de datos y transformación en un solo flujo
        return anuncioRepository.listarAnunciosRecientesPorSede(sedeId, pageable)
                .map(mapToAnuncioListDTO());
    }

    /**
     * Lista anuncios activos y vigentes (no expirados)
     * Un anuncio es vigente si su fecha de fin es mayor o igual a la fecha actual,
     * o si no tiene fecha de fin definida
     * Siempre consulta la base de datos directamente sin usar caché
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AnuncioListDTO> listarAnunciosActivosYVigentes(int page, int size) {
        // Usar programación funcional para crear pageable y transformar resultados
        Pageable pageable = PageRequest.of(page, size);

        // Consulta directa a la base de datos y transformación en un solo flujo
        return anuncioRepository.listarAnunciosRecientes(pageable)
                .map(mapToAnuncioListDTO());
    }

    /**
     * Lista anuncios activos y vigentes (no expirados) filtrados por sede
     * Un anuncio es vigente si su fecha de fin es mayor o igual a la fecha actual,
     * o si no tiene fecha de fin definida
     * Siempre consulta la base de datos directamente sin usar caché
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AnuncioListDTO> listarAnunciosActivosYVigentesPorSede(int page, int size, Long sedeId) {
        // Usar programación funcional para crear pageable y transformar resultados
        Pageable pageable = PageRequest.of(page, size);

        // Consulta directa a la base de datos y transformación en un solo flujo
        return anuncioRepository.listarAnunciosRecientesPorSede(sedeId, pageable)
                .map(mapToAnuncioListDTO());
    }

    /**
     * Lista todos los anuncios sin filtrar por estado o fecha
     * Siempre consulta la base de datos directamente sin usar caché
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AnuncioListDTO> listarTodosLosAnuncios(int page, int size) {
        // Usar programación funcional para crear pageable y transformar resultados
        Pageable pageable = PageRequest.of(page, size);

        // Consulta directa a la base de datos y transformación en un solo flujo
        return anuncioRepository.listarTodosLosAnuncios(pageable)
                .map(mapToAnuncioListDTO());
    }

    /**
     * Lista todos los anuncios sin filtrar por estado o fecha, filtrados por sede
     * Siempre consulta la base de datos directamente sin usar caché
     */
    @Override
    @Transactional(readOnly = true)
    public Page<AnuncioListDTO> listarTodosLosAnunciosPorSede(int page, int size, Long sedeId) {
        // Usar programación funcional para crear pageable y transformar resultados
        Pageable pageable = PageRequest.of(page, size);

        // Consulta directa a la base de datos y transformación en un solo flujo
        return anuncioRepository.listarTodosLosAnunciosPorSede(sedeId, pageable)
                .map(mapToAnuncioListDTO());
    }

    /**
     * Actualiza el orden de un anuncio
     */
    @Override
    @Transactional
    public AnuncioUpdateResponseDTO actualizarOrden(Long id, Integer orden) {
        // Usar programación funcional para todo el flujo
        return anuncioRepository.findById(id)
                .map(anuncio -> {
                    // Actualizar orden
                    anuncio.setOrden(orden);
                    Anuncio actualizado = anuncioRepository.save(anuncio);

                    // Publicar evento de actualización
                    try {
                        eventPublisher.publishEvent(new AnuncioUpdatedEvent(this, actualizado));
                    } catch (Exception e) {
                        // Si hay un error al publicar el evento, lo registramos pero no interrumpimos la operación
                    }

                    return AnuncioMapper.toUpdateResponseDTO(actualizado);
                })
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ANUNCIO_NOT_FOUND));
    }

    /**
     * Actualiza el estado de los anuncios expirados a INACTIVO
     * Se ejecuta periódicamente mediante una tarea programada
     * @return Número de anuncios actualizados
     */
    @Override
    @Transactional
    public int actualizarAnunciosExpirados() {
        LocalDateTime ahora = LocalDateTime.now();

        // Buscar anuncios activos cuya fecha de fin ha pasado
        List<Anuncio> anunciosExpirados = anuncioRepository.findByEstadoAndFechaFinLessThan("ACTIVO", ahora);

        // Usar programación funcional para procesar la lista
        return anunciosExpirados.stream()
                .map(anuncio -> {
                    // Actualizar estado
                    anuncio.setEstado("INACTIVO");
                    Anuncio actualizado = anuncioRepository.save(anuncio);

                    // Publicar evento de actualización
                    try {
                        eventPublisher.publishEvent(new AnuncioUpdatedEvent(this, actualizado));
                    } catch (Exception e) {
                        // Si hay un error al publicar el evento, lo registramos pero no interrumpimos la operación
                    }

                    return actualizado;
                })
                .collect(Collectors.toList())
                .size();
    }

    @Override
    @Transactional(readOnly = true)
    public List<Sede> obtenerTodasLasSedes() {
        // Obtener todas las sedes activas del repositorio
        return sedeRepository.findAllActive();
    }
}
