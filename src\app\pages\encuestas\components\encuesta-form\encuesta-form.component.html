<div class="p-4">
  <h2 mat-dialog-title class="text-xl font-bold mb-4 dark:text-white">
    {{ data.isNew ? "Crear nueva encuesta" : "Editar encuesta" }}
  </h2>

  <form [formGroup]="form" (ngSubmit)="onSubmit()">
    <mat-dialog-content class="mat-typography">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Título -->
        <mat-form-field appearance="outline" class="col-span-2">
          <mat-label>Título</mat-label>
          <input
            matInput
            formControlName="titulo"
            placeholder="Título de la encuesta"
            class="dark:bg-gray-700 dark:text-white"
          />
          <mat-error *ngIf="form.get('titulo')?.hasError('required')">
            El título es obligatorio
          </mat-error>
          <mat-error *ngIf="form.get('titulo')?.hasError('maxlength')">
            El título no puede tener más de 100 caracteres
          </mat-error>
        </mat-form-field>

        <!-- Descripción -->
        <mat-form-field appearance="outline" class="col-span-2">
          <mat-label>Descripción</mat-label>
          <textarea
            matInput
            formControlName="descripcion"
            placeholder="Descripción de la encuesta"
            rows="3"
            class="dark:bg-gray-700 dark:text-white"
          ></textarea>
          <mat-error *ngIf="form.get('descripcion')?.hasError('maxlength')">
            La descripción no puede tener más de 500 caracteres
          </mat-error>
        </mat-form-field>

        <!-- Fecha de inicio -->
        <mat-form-field appearance="outline">
          <mat-label>Fecha de inicio</mat-label>
          <input
            matInput
            [matDatepicker]="pickerInicio"
            formControlName="fechaInicio"
            class="dark:bg-gray-700 dark:text-white"
          />
          <mat-datepicker-toggle
            matSuffix
            [for]="pickerInicio"
          ></mat-datepicker-toggle>
          <mat-datepicker #pickerInicio></mat-datepicker>
        </mat-form-field>

        <!-- Fecha de fin -->
        <mat-form-field appearance="outline">
          <mat-label>Fecha de fin</mat-label>
          <input
            matInput
            [matDatepicker]="pickerFin"
            formControlName="fechaFin"
            class="dark:bg-gray-700 dark:text-white"
          />
          <mat-datepicker-toggle
            matSuffix
            [for]="pickerFin"
          ></mat-datepicker-toggle>
          <mat-datepicker #pickerFin></mat-datepicker>
        </mat-form-field>

        <!-- Tiempo límite -->
        <mat-form-field appearance="outline">
          <mat-label>Tiempo límite (minutos)</mat-label>
          <input
            matInput
            type="number"
            formControlName="tiempoLimite"
            placeholder="Tiempo límite en minutos"
            class="dark:bg-gray-700 dark:text-white"
          />
          <mat-hint>Dejar en blanco para sin límite</mat-hint>
          <mat-error *ngIf="form.get('tiempoLimite')?.hasError('min')">
            El tiempo límite debe ser mayor a 0
          </mat-error>
        </mat-form-field>

        <!-- Tipo de asignación -->
        <mat-form-field appearance="outline">
          <mat-label>Tipo de asignación</mat-label>
          <mat-select
            formControlName="tipoAsignacion"
            class="dark:bg-gray-700 dark:text-white"
          >
            <mat-option [value]="tipoAsignacion.TODOS"
              >Todos los usuarios</mat-option
            >
            <mat-option [value]="tipoAsignacion.SEDE"
              >Usuarios de sede específica</mat-option
            >
            <mat-option [value]="tipoAsignacion.COORDINACION"
              >Usuarios de coordinador específico</mat-option
            >
            <mat-option [value]="tipoAsignacion.PERSONAL"
              >Usuario específico</mat-option
            >
          </mat-select>
        </mat-form-field>

        <!-- Sede (si tipoAsignacion = SEDE) -->
        <mat-form-field
          appearance="outline"
          *ngIf="form.get('tipoAsignacion')?.value === tipoAsignacion.SEDE"
        >
          <mat-label>Sede</mat-label>
          <mat-select
            formControlName="sedeId"
            class="dark:bg-gray-700 dark:text-white"
          >
            <mat-option *ngFor="let sede of sedes" [value]="sede.id">
              {{ sede.nombre }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="form.get('sedeId')?.hasError('required')">
            La sede es obligatoria
          </mat-error>
        </mat-form-field>

        <!-- Coordinador (si tipoAsignacion = COORDINACION) -->
        <mat-form-field
          appearance="outline"
          *ngIf="
            form.get('tipoAsignacion')?.value === tipoAsignacion.COORDINACION
          "
        >
          <mat-label>Coordinador</mat-label>
          <mat-select
            formControlName="coordinadorId"
            class="dark:bg-gray-700 dark:text-white"
          >
            <mat-option
              *ngFor="let coordinador of coordinadores"
              [value]="coordinador.id"
            >
              {{ coordinador.nombre }} {{ coordinador.apellido }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="form.get('coordinadorId')?.hasError('required')">
            El coordinador es obligatorio
          </mat-error>
        </mat-form-field>

        <!-- Usuario (si tipoAsignacion = PERSONAL) -->
        <mat-form-field
          appearance="outline"
          *ngIf="form.get('tipoAsignacion')?.value === tipoAsignacion.PERSONAL"
        >
          <mat-label>Usuario</mat-label>
          <mat-select
            formControlName="usuarioId"
            class="dark:bg-gray-700 dark:text-white"
          >
            <mat-option *ngFor="let usuario of usuarios" [value]="usuario.id">
              {{ usuario.nombre }} {{ usuario.apellido }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="form.get('usuarioId')?.hasError('required')">
            El usuario es obligatorio
          </mat-error>
        </mat-form-field>

        <!-- Opciones adicionales -->
        <div class="col-span-2 flex flex-col md:flex-row gap-4">
          <mat-slide-toggle
            formControlName="esAnonima"
            color="primary"
            class="dark:text-white"
          >
            Encuesta anónima
          </mat-slide-toggle>

          <mat-slide-toggle
            formControlName="mostrarResultados"
            color="primary"
            class="dark:text-white"
          >
            Mostrar resultados al finalizar
          </mat-slide-toggle>

          <!-- Estado (solo para edición) -->
          <mat-slide-toggle
            *ngIf="!data.isNew"
            formControlName="estado"
            [checked]="form.get('estado')?.value === 'A'"
            (change)="form.get('estado')?.setValue($event.checked ? 'A' : 'I')"
            color="primary"
            class="dark:text-white"
          >
            Encuesta activa
          </mat-slide-toggle>
        </div>
      </div>

      <!-- Sección de preguntas -->
      <div class="mt-8">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium dark:text-white">Preguntas</h3>
          <button
            type="button"
            mat-mini-fab
            color="primary"
            (click)="addPregunta()"
            matTooltip="Agregar pregunta"
          >
            <mat-icon>add</mat-icon>
          </button>
        </div>

        <div formArrayName="preguntas" class="space-y-6">
          <div
            *ngFor="let preguntaGroup of preguntasForm.controls; let i = index"
            [formGroupName]="i"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
          >
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-md font-medium dark:text-white">
                Pregunta {{ i + 1 }}
              </h4>
              <button
                type="button"
                mat-icon-button
                color="warn"
                (click)="removePregunta(i)"
                matTooltip="Eliminar pregunta"
              >
                <mat-icon>delete</mat-icon>
              </button>
            </div>

            <!-- Enunciado -->
            <mat-form-field appearance="outline" class="w-full">
              <mat-label>Enunciado</mat-label>
              <textarea
                matInput
                formControlName="enunciado"
                placeholder="Enunciado de la pregunta"
                rows="2"
                class="dark:bg-gray-700 dark:text-white"
              ></textarea>
              <mat-error
                *ngIf="preguntaGroup.get('enunciado')?.hasError('required')"
              >
                El enunciado es obligatorio
              </mat-error>
            </mat-form-field>

            <!-- Descripción -->
            <mat-form-field appearance="outline" class="w-full">
              <mat-label>Descripción (opcional)</mat-label>
              <textarea
                matInput
                formControlName="descripcion"
                placeholder="Descripción o instrucciones adicionales"
                rows="2"
                class="dark:bg-gray-700 dark:text-white"
              ></textarea>
            </mat-form-field>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <!-- Tipo de pregunta -->
              <mat-form-field appearance="outline">
                <mat-label>Tipo de pregunta</mat-label>
                <mat-select
                  formControlName="tipo"
                  class="dark:bg-gray-700 dark:text-white"
                >
                  <mat-option [value]="tipoPregunta.OPCION_MULTIPLE"
                    >Opción múltiple (una respuesta)</mat-option
                  >
                  <mat-option [value]="tipoPregunta.SELECCION_MULTIPLE"
                    >Selección múltiple (varias respuestas)</mat-option
                  >
                  <mat-option [value]="tipoPregunta.ESCALA_LIKERT"
                    >Escala de valoración</mat-option
                  >
                  <mat-option [value]="tipoPregunta.TEXTO_LIBRE"
                    >Texto libre</mat-option
                  >
                  <mat-option [value]="tipoPregunta.FECHA">Fecha</mat-option>
                  <mat-option [value]="tipoPregunta.NUMERO">Número</mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Orden -->
              <mat-form-field appearance="outline">
                <mat-label>Orden</mat-label>
                <input
                  matInput
                  type="number"
                  formControlName="orden"
                  class="dark:bg-gray-700 dark:text-white"
                />
              </mat-form-field>

              <!-- Es obligatoria -->
              <div class="flex items-center">
                <mat-slide-toggle
                  formControlName="esObligatoria"
                  color="primary"
                  class="dark:text-white"
                >
                  Pregunta obligatoria
                </mat-slide-toggle>
              </div>
            </div>

            <!-- Opciones de respuesta (solo para tipos que lo requieren) -->
            <div
              *ngIf="
                preguntaGroup.get('tipo')?.value ===
                  tipoPregunta.OPCION_MULTIPLE ||
                preguntaGroup.get('tipo')?.value ===
                  tipoPregunta.SELECCION_MULTIPLE ||
                preguntaGroup.get('tipo')?.value === tipoPregunta.ESCALA_LIKERT
              "
            >
              <div class="flex justify-between items-center mb-2">
                <h5 class="text-sm font-medium dark:text-white">
                  Opciones de respuesta
                </h5>
                <button
                  type="button"
                  mat-mini-fab
                  color="primary"
                  (click)="addOpcion(i)"
                  matTooltip="Agregar opción"
                >
                  <mat-icon>add</mat-icon>
                </button>
              </div>

              <div formArrayName="opciones" class="space-y-3">
                <div
                  *ngFor="
                    let opcionGroup of getOpciones(i).controls;
                    let j = index
                  "
                  [formGroupName]="j"
                  class="flex items-center gap-2 bg-gray-50 dark:bg-gray-700 p-2 rounded border border-gray-100 dark:border-gray-600"
                >
                  <!-- Texto de opción -->
                  <mat-form-field appearance="outline" class="flex-1 !m-0">
                    <mat-label>Texto</mat-label>
                    <input
                      matInput
                      formControlName="texto"
                      placeholder="Texto de la opción"
                      class="dark:bg-gray-700 dark:text-white"
                    />
                    <mat-error
                      *ngIf="opcionGroup.get('texto')?.hasError('required')"
                    >
                      El texto es obligatorio
                    </mat-error>
                  </mat-form-field>

                  <!-- Valor (para escala Likert) -->
                  <mat-form-field
                    appearance="outline"
                    *ngIf="
                      preguntaGroup.get('tipo')?.value ===
                      tipoPregunta.ESCALA_LIKERT
                    "
                    class="w-24 !m-0"
                  >
                    <mat-label>Valor</mat-label>
                    <input
                      matInput
                      type="number"
                      formControlName="valor"
                      placeholder="Valor"
                      class="dark:bg-gray-700 dark:text-white"
                    />
                  </mat-form-field>

                  <!-- Botón eliminar -->
                  <button
                    type="button"
                    mat-icon-button
                    color="warn"
                    (click)="removeOpcion(i, j)"
                    matTooltip="Eliminar opción"
                    [disabled]="getOpciones(i).length <= 2"
                  >
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mensaje cuando no hay preguntas -->
        <div
          *ngIf="preguntasForm.length === 0"
          class="p-6 text-center text-gray-500 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
        >
          No hay preguntas. Haga clic en el botón "+" para agregar una pregunta.
        </div>
      </div>
    </mat-dialog-content>

    <mat-dialog-actions
      align="end"
      class="flex justify-end gap-4 pt-4 border-t border-gray-200 dark:border-gray-700"
    >
      <button
        type="button"
        mat-button
        (click)="onCancel()"
        [disabled]="submitting"
        class="bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 px-4 py-2 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Cancelar
      </button>
      <button
        type="submit"
        mat-raised-button
        color="primary"
        [disabled]="submitting"
        class="bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800 px-4 py-2 rounded-md transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <mat-spinner
          *ngIf="submitting"
          diameter="20"
          class="mr-2"
        ></mat-spinner>
        <span>{{ data.isNew ? "Crear" : "Actualizar" }}</span>
      </button>
    </mat-dialog-actions>
  </form>
</div>
