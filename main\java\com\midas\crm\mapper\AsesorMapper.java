package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.User;

public class AsesorMapper {

    public static AsesorDTO toDTO(User user) {
        // Obtener el nombre de la sede, primero desde sedeNombre y si es null, desde la relación sede
        String nombreSede = user.getSedeNombre();
        if (nombreSede == null && user.getSede() != null) {
            nombreSede = user.getSede().getNombre();
        }

        return new AsesorDTO(
                user.getId(),
                user.getNombre(),
                user.getApellido(),
                user.getUsername(),
                user.getDni(),
                user.getEmail(),
                user.getTelefono(),
                nombreSede
        );
    }
}
