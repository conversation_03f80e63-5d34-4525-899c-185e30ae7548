package com.midas.crm.controller;

import com.midas.crm.entity.Anuncio;
import com.midas.crm.entity.DTO.anuncio.AnuncioDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioListDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioUpdateResponseDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.event.AnuncioCreatedEvent;
import com.midas.crm.event.AnuncioDeletedEvent;
import com.midas.crm.event.AnuncioUpdatedEvent;
import com.midas.crm.mapper.AnuncioMapper;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.AnuncioService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("${api.route.anuncios}")
@RequiredArgsConstructor
@Slf4j
public class AnuncioController {

    private final AnuncioService anuncioService;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Clase para almacenar parámetros de paginación
     */
    private static class PaginationParams {
        final int page;
        final int size;
        final Long sedeId;

        PaginationParams(int page, int size) {
            this.page = page;
            this.size = size;
            this.sedeId = null;
        }

        PaginationParams(int page, int size, Long sedeId) {
            this.page = page;
            this.size = size;
            this.sedeId = sedeId;
        }
    }

    /**
     * Extrae los parámetros de paginación del payload
     * @param payload Payload con los parámetros
     * @param defaultPage Página por defecto
     * @param defaultSize Tamaño por defecto
     * @return Objeto con los parámetros de paginación
     */
    private PaginationParams extractPaginationParams(Map<String, Object> payload, int defaultPage, int defaultSize) {
        return Optional.ofNullable(payload)
                .map(p -> {
                    int page = Optional.ofNullable(p.get("page"))
                            .map(Object::toString)
                            .map(Integer::parseInt)
                            .orElse(defaultPage);

                    int size = Optional.ofNullable(p.get("size"))
                            .map(Object::toString)
                            .map(Integer::parseInt)
                            .orElse(defaultSize);

                    Long sedeId = Optional.ofNullable(p.get("sedeId"))
                            .map(Object::toString)
                            .map(Long::parseLong)
                            .orElse(null);

                    return new PaginationParams(page, size, sedeId);
                })
                .orElse(new PaginationParams(defaultPage, defaultSize));
    }

    @PostMapping
    public ResponseEntity<GenericResponse<Anuncio>> crearAnuncio(@RequestBody AnuncioDTO dto) {
        return Optional.ofNullable(dto)
                .map(anuncioService::guardar)
                .map(creado -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Anuncio creado", creado)
                ))
                .orElseGet(() -> ResponseEntity.badRequest().build());
    }

    @GetMapping
    public ResponseEntity<GenericResponse<List<AnuncioListDTO>>> listar(
            @RequestParam(defaultValue = "0") int page) {

        return Optional.of(page)
                .map(p -> anuncioService.listarPaginado(p, 8))
                .map(anuncios -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Lista de anuncios", anuncios)
                ))
                .orElseGet(() -> ResponseEntity.badRequest().build());
    }


    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<String>> eliminar(@PathVariable Long id) {
        return Optional.ofNullable(id)
                .map(anuncioId -> {
                    anuncioService.eliminar(anuncioId);
                    return ResponseEntity.ok(
                            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Anuncio eliminado", "ID: " + anuncioId)
                    );
                })
                .orElseGet(() -> ResponseEntity.badRequest().build());
    }

    @PostMapping("/update/{id}")
    public ResponseEntity<GenericResponse<AnuncioUpdateResponseDTO>> actualizarConPost(
            @PathVariable Long id,
            @RequestBody AnuncioDTO dto) {

        return Optional.ofNullable(dto)
                .map(anuncioDTO -> anuncioService.actualizarParcial(id, anuncioDTO))
                .map(responseDTO -> ResponseEntity.ok(
                        new GenericResponse<>(
                                GenericResponseConstants.SUCCESS,
                                "Anuncio actualizado correctamente",
                                responseDTO
                        )
                ))
                .orElseGet(() -> ResponseEntity.badRequest().build());
    }

    @GetMapping("/recientes")
    public ResponseEntity<GenericResponse<Page<AnuncioListDTO>>> listarMasRecientes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "6") int size,
            @RequestParam(required = false) Long sedeId) {

        // Obtener anuncios recientes
        Page<AnuncioListDTO> anunciosDTO;

        if (sedeId != null) {
            // Si se especificó una sede, filtrar por sede
            anunciosDTO = anuncioService.listarMasRecientesPaginadoPorSede(page, size, sedeId);
        } else {
            // Si no se especificó una sede, obtener todos
            anunciosDTO = anuncioService.listarMasRecientesPaginado(page, size);
        }

        // Filtrar anuncios inactivos
        List<AnuncioListDTO> anunciosActivos = anunciosDTO.getContent().stream()
                .filter(a -> "ACTIVO".equals(a.getEstado()))
                .collect(Collectors.toList());

        // Crear una nueva página con solo los anuncios activos
        Page<AnuncioListDTO> anunciosFiltrados = new PageImpl<>(
                anunciosActivos,
                anunciosDTO.getPageable(),
                anunciosActivos.size()
        );

        // Devolver respuesta
        return ResponseEntity.ok(new GenericResponse<>(
                GenericResponseConstants.SUCCESS,
                "Anuncios más recientes",
                anunciosFiltrados
        ));
    }

    @GetMapping("/activos")
    public ResponseEntity<GenericResponse<Page<AnuncioListDTO>>> listarAnunciosActivos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "6") int size,
            @RequestParam(required = false) Long sedeId) {

        if (sedeId != null) {
            // Si se especificó una sede, filtrar por sede
            return Optional.of(new PaginationParams(page, size, sedeId))
                    .map(params -> anuncioService.listarAnunciosActivosYVigentesPorSede(params.page, params.size, params.sedeId))
                    .map(anunciosDTO -> ResponseEntity.ok(
                            new GenericResponse<>(
                                    GenericResponseConstants.SUCCESS,
                                    "Anuncios activos y vigentes para la sede",
                                    anunciosDTO
                            )
                    ))
                    .orElseGet(() -> ResponseEntity.badRequest().build());
        } else {
            // Si no se especificó una sede, obtener todos
            return Optional.of(new PaginationParams(page, size))
                    .map(params -> anuncioService.listarAnunciosActivosYVigentes(params.page, params.size))
                    .map(anunciosDTO -> ResponseEntity.ok(
                            new GenericResponse<>(
                                    GenericResponseConstants.SUCCESS,
                                    "Anuncios activos y vigentes",
                                    anunciosDTO
                            )
                    ))
                    .orElseGet(() -> ResponseEntity.badRequest().build());
        }
    }

    @GetMapping("/todos")
    public ResponseEntity<GenericResponse<Page<AnuncioListDTO>>> listarTodosLosAnuncios(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        // Siempre mostrar todos los anuncios de todas las sedes
        return Optional.of(new PaginationParams(page, size))
                .map(params -> anuncioService.listarTodosLosAnuncios(params.page, params.size))
                .map(anunciosDTO -> ResponseEntity.ok(
                        new GenericResponse<>(
                                GenericResponseConstants.SUCCESS,
                                "Todos los anuncios",
                                anunciosDTO
                        )
                ))
                .orElseGet(() -> ResponseEntity.badRequest().build());
    }

    // ==================== WebSocket Endpoints ====================

    /**
     * Endpoint WebSocket para listar todos los anuncios
     */
    @MessageMapping("/anuncios.list")
    @SendTo("/topic/anuncios")
    public List<AnuncioListDTO> listarAnunciosWs(@Payload(required = false) Map<String, Object> payload) {
        PaginationParams params = extractPaginationParams(payload, 0, 8);

        if (params.sedeId != null) {
            // Si se especificó una sede, filtrar por sede
            return anuncioService.listarPaginadoPorSede(params.page, params.size, params.sedeId);
        } else {
            // Si no se especificó una sede, obtener todos
            return anuncioService.listarPaginado(params.page, params.size);
        }
    }

    /**
     * Endpoint WebSocket para listar anuncios activos y vigentes
     */
    @MessageMapping("/anuncios.activos")
    @SendTo("/topic/anuncios/activos")
    public Page<AnuncioListDTO> listarAnunciosActivosWs(@Payload(required = false) Map<String, Object> payload) {
        PaginationParams params = extractPaginationParams(payload, 0, 8);

        if (params.sedeId != null) {
            // Si se especificó una sede, filtrar por sede
            return anuncioService.listarAnunciosActivosYVigentesPorSede(params.page, params.size, params.sedeId);
        } else {
            // Si no se especificó una sede, obtener todos
            return anuncioService.listarAnunciosActivosYVigentes(params.page, params.size);
        }
    }

    /**
     * Endpoint WebSocket para listar todos los anuncios sin filtrar
     */
    @MessageMapping("/anuncios.todos")
    @SendTo("/topic/anuncios/todos")
    public Page<AnuncioListDTO> listarTodosAnunciosWs(@Payload(required = false) Map<String, Object> payload) {
        PaginationParams params = extractPaginationParams(payload, 0, 10);

        // Siempre mostrar todos los anuncios de todas las sedes
        return anuncioService.listarTodosLosAnuncios(params.page, params.size);
    }

    /**
     * Endpoint WebSocket para notificar eliminación de anuncios
     */
    @MessageMapping("/anuncios.delete")
    @SendTo("/topic/anuncios/delete")
    public Long notificarEliminacionAnuncioWs(@Payload Long anuncioId) {
        // Simplemente reenviar el ID del anuncio eliminado
        return anuncioId;
    }

    /**
     * Endpoint WebSocket para notificar eliminación de anuncios recientes
     */
    @MessageMapping("/anuncios.recientes.delete")
    @SendTo("/topic/anuncios/recientes/delete")
    public Long notificarEliminacionAnuncioRecienteWs(@Payload Long anuncioId) {
        // Simplemente reenviar el ID del anuncio eliminado
        return anuncioId;
    }

    /**
     * Endpoint WebSocket para notificar eliminación de anuncios en el listado
     */
    @MessageMapping("/anuncios.list.delete")
    @SendTo("/topic/anuncios/list/delete")
    public Long notificarEliminacionAnuncioListWs(@Payload Long anuncioId) {
        // Simplemente reenviar el ID del anuncio eliminado
        return anuncioId;
    }

    /**
     * Endpoint WebSocket para listar anuncios recientes
     * Solo devuelve anuncios activos directamente desde la base de datos
     * OPTIMIZADO:
     * - Envía los anuncios al tópico específico de la sede solicitada
     * - Si el usuario es ADMIN, muestra todos los anuncios sin filtrar por sede
     */
    @MessageMapping("/anuncios.recientes")
    public void listarAnunciosRecientesWs(@Payload(required = false) Map<String, Object> payload,
                                          SimpMessageHeaderAccessor headerAccessor) {
        PaginationParams params = extractPaginationParams(payload, 0, 6);

        // Verificar si el usuario es ADMIN
        boolean isAdmin = false;
        Authentication authentication = (Authentication) headerAccessor.getUser();
        if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal) {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            if (userPrincipal.getUser() != null && userPrincipal.getUser().getRole() == Role.ADMIN) {
                isAdmin = true;
                log.debug("Usuario ADMIN solicitando anuncios recientes");
            }
        }

        // Determinar el destino del mensaje según la sede y el rol
        String destination;
        Page<AnuncioListDTO> anunciosDTO;

        if (isAdmin) {
            // Si es ADMIN, obtener todos los anuncios sin filtrar por sede
            anunciosDTO = anuncioService.listarMasRecientesPaginado(params.page, params.size);

            // Si se especificó una sede en la solicitud, usar ese tópico específico
            if (params.sedeId != null) {
                destination = "/topic/anuncios/recientes/sede/" + params.sedeId;
            } else {
                destination = "/topic/anuncios/recientes";
            }
        } else if (params.sedeId != null) {
            // Si no es ADMIN y se especificó una sede, filtrar por sede
            anunciosDTO = anuncioService.listarMasRecientesPaginadoPorSede(params.page, params.size, params.sedeId);
            destination = "/topic/anuncios/recientes/sede/" + params.sedeId;
        } else {
            // Si no es ADMIN y no se especificó sede, obtener todos (comportamiento por defecto)
            anunciosDTO = anuncioService.listarMasRecientesPaginado(params.page, params.size);
            destination = "/topic/anuncios/recientes";
        }

        // Filtrar anuncios inactivos
        List<AnuncioListDTO> anunciosActivos = anunciosDTO.getContent().stream()
                .filter(a -> "ACTIVO".equals(a.getEstado()))
                .collect(Collectors.toList());

        // Crear una nueva página con solo los anuncios activos
        Page<AnuncioListDTO> anunciosFiltrados = new PageImpl<>(
                anunciosActivos,
                anunciosDTO.getPageable(),
                anunciosActivos.size()
        );

        // Enviar los anuncios al destino correspondiente
        messagingTemplate.convertAndSend(destination, anunciosFiltrados);
    }

    // ==================== Event Listeners ====================

    /**
     * Escucha el evento de creación de anuncio y notifica a los clientes
     */
    @EventListener
    public void handleAnuncioCreatedEvent(AnuncioCreatedEvent event) {
        Optional.ofNullable(event)
                .map(AnuncioCreatedEvent::getAnuncio)
                .ifPresent(anuncio -> {
                    //log.info("Notificando nuevo anuncio: {}", anuncio.getTitulo());

                    // Notificar a todos los clientes sobre el nuevo anuncio
                    messagingTemplate.convertAndSend("/topic/anuncios/new", AnuncioMapper.toListDTO(anuncio));

                    // Actualizar las listas de anuncios
                    actualizarListasAnuncios();
                });
    }

    /**
     * Escucha el evento de actualización de anuncio y notifica a los clientes
     */
    @EventListener
    public void handleAnuncioUpdatedEvent(AnuncioUpdatedEvent event) {
        Optional.ofNullable(event)
                .map(AnuncioUpdatedEvent::getAnuncio)
                .ifPresent(anuncio -> {
                    //log.info("Notificando actualización de anuncio: {}", anuncio.getTitulo());

                    // Notificar a todos los clientes sobre la actualización del anuncio
                    messagingTemplate.convertAndSend("/topic/anuncios/update", AnuncioMapper.toListDTO(anuncio));

                    // Actualizar las listas de anuncios
                    actualizarListasAnuncios();
                });
    }

    /**
     * Escucha el evento de eliminación de anuncio y notifica a los clientes
     */
    @EventListener
    public void handleAnuncioDeletedEvent(AnuncioDeletedEvent event) {
        Optional.ofNullable(event)
                .map(AnuncioDeletedEvent::getAnuncioId)
                .ifPresent(anuncioId -> {
                    //log.info("Notificando eliminación de anuncio: {}", anuncioId);

                    // Notificar a todos los clientes sobre la eliminación del anuncio
                    messagingTemplate.convertAndSend("/topic/anuncios/delete", anuncioId);

                    // Notificar específicamente a los tópicos de anuncios recientes y listado general
                    messagingTemplate.convertAndSend("/topic/anuncios/recientes/delete", anuncioId);
                    messagingTemplate.convertAndSend("/topic/anuncios/list/delete", anuncioId);

                    // Notificar a los tópicos específicos por sede
                    try {
                        anuncioService.obtenerTodasLasSedes().forEach(sede -> {
                            Long sedeId = sede.getId();
                            if (sedeId != null) {
                                messagingTemplate.convertAndSend("/topic/anuncios/recientes/sede/" + sedeId + "/delete", anuncioId);
                            }
                        });
                    } catch (Exception e) {
                        System.err.println("Error al enviar notificaciones de eliminación por sede: " + e.getMessage());
                    }

                    // Actualizar las listas de anuncios
                    actualizarListasAnuncios();
                });
    }

    /**
     * Actualiza las listas de anuncios en tiempo real
     * Obtiene los datos directamente de la base de datos sin usar caché
     * OPTIMIZADO: Envía actualizaciones a tópicos específicos por sede
     */
    private void actualizarListasAnuncios() {
        // Definir las actualizaciones a realizar
        record AnuncioUpdate<T>(String topic, T data) {}

        // Obtener anuncios recientes generales y filtrar solo activos
        Page<AnuncioListDTO> anunciosRecientes = anuncioService.listarMasRecientesPaginado(0, 6);
        List<AnuncioListDTO> anunciosRecientesActivos = anunciosRecientes.getContent().stream()
                .filter(a -> "ACTIVO".equals(a.getEstado()))
                .collect(Collectors.toList());
        Page<AnuncioListDTO> anunciosRecientesFiltrados = new PageImpl<>(
                anunciosRecientesActivos,
                anunciosRecientes.getPageable(),
                anunciosRecientesActivos.size()
        );

        // Crear y enviar las actualizaciones a tópicos generales
        List.of(
                new AnuncioUpdate<>("/topic/anuncios", anuncioService.listarPaginado(0, 8)),
                new AnuncioUpdate<>("/topic/anuncios/list", anuncioService.listarPaginado(0, 8)), // Tópico adicional para listado
                new AnuncioUpdate<>("/topic/anuncios/recientes", anunciosRecientesFiltrados),
                new AnuncioUpdate<>("/topic/anuncios/activos", anuncioService.listarAnunciosActivosYVigentes(0, 8)),
                new AnuncioUpdate<>("/topic/anuncios/todos", anuncioService.listarTodosLosAnuncios(0, 10))
        ).forEach(update -> messagingTemplate.convertAndSend(update.topic(), update.data()));

        // Obtener todas las sedes activas para enviar actualizaciones específicas por sede
        try {
            // Obtener anuncios recientes para cada sede
            anuncioService.obtenerTodasLasSedes().forEach(sede -> {
                Long sedeId = sede.getId();
                if (sedeId != null) {
                    // Obtener anuncios recientes para esta sede
                    Page<AnuncioListDTO> anunciosRecientesSede = anuncioService.listarMasRecientesPaginadoPorSede(0, 6, sedeId);

                    // Filtrar anuncios inactivos
                    List<AnuncioListDTO> anunciosActivosSede = anunciosRecientesSede.getContent().stream()
                            .filter(a -> "ACTIVO".equals(a.getEstado()))
                            .collect(Collectors.toList());

                    // Crear una nueva página con solo los anuncios activos
                    Page<AnuncioListDTO> anunciosFiltradosSede = new PageImpl<>(
                            anunciosActivosSede,
                            anunciosRecientesSede.getPageable(),
                            anunciosActivosSede.size()
                    );

                    // Enviar anuncios recientes específicos para esta sede (solo activos)
                    messagingTemplate.convertAndSend(
                            "/topic/anuncios/recientes/sede/" + sedeId,
                            anunciosFiltradosSede
                    );

                    // Enviar también al tópico específico para actualizaciones de listado por sede
                    messagingTemplate.convertAndSend(
                            "/topic/anuncios/list/sede/" + sedeId,
                            anunciosFiltradosSede
                    );

                    // También enviar anuncios activos específicos para esta sede
                    messagingTemplate.convertAndSend(
                            "/topic/anuncios/activos/sede/" + sedeId,
                            anuncioService.listarAnunciosActivosYVigentesPorSede(0, 6, sedeId)
                    );

                    // Y la lista completa de anuncios para esta sede
                    messagingTemplate.convertAndSend(
                            "/topic/anuncios/todos/sede/" + sedeId,
                            anuncioService.listarTodosLosAnunciosPorSede(0, 10, sedeId)
                    );
                }
            });
        } catch (Exception e) {
            // Si hay algún error al obtener las sedes, lo registramos pero continuamos
            // para no interrumpir el flujo principal
            System.err.println("Error al enviar actualizaciones específicas por sede: " + e.getMessage());
        }
    }
}
