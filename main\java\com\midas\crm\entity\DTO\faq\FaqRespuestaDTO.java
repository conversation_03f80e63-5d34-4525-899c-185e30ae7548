package com.midas.crm.entity.DTO.faq;

import com.midas.crm.entity.DTO.user.UserDTO;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class FaqRespuestaDTO {
    private Long id;
    private String contenido;

    // FAQ al que pertenece esta respuesta
    private Long faqId;

    // Usuario que crea la respuesta
    private Long usuarioId;
    private String usuarioNombre;
    private UserDTO usuario;

    // Archivos adjuntos
    private List<FileFaqRespuestaDTO> archivos = new ArrayList<>();

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
