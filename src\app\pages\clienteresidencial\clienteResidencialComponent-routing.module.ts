import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@app/guards/auth/auth.guard';

const routes: Routes = [
  {
    path: 'listar',
    loadChildren: () =>
      import(
        './pages/clienteresidencial-list/clienteResidencial-list.module'
      ).then((m) => m.ClienteResidencialListModule),
    canActivate: [AuthGuard],
  },
  {
    path: 'estadisticas',
    loadChildren: () =>
      import('./pages/graficos-sede-page/graficos-sede-page.module').then(
        (m) => m.GraficosSedePageModule
      ),
    canActivate: [AuthGuard],
  },
  {
    path: '**',
    pathMatch: 'full',
    redirectTo: 'list',
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ClienteResidencialRoutingModule {}
