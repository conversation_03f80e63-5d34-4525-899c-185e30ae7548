package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "curso_usuario")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CursoUsuario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "curso_id", nullable = false)
    private Curso curso;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User usuario;

    @Column(name = "fecha_asignacion", nullable = false)
    private LocalDateTime fechaAsignacion;

    @Column(name = "estado", nullable = false, length = 1)
    private String estado; // A: Activo, I: Inactivo

    @Column(name = "completado", nullable = false)
    private boolean completado = false;

    @Column(name = "fecha_completado")
    private LocalDateTime fechaCompletado;

    @Column(name = "porcentaje_completado", nullable = false)
    private int porcentajeCompletado = 0;

    @Column(name = "ultima_visualizacion")
    private LocalDateTime ultimaVisualizacion;
}
