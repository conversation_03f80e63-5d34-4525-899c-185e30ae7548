package com.midas.crm.entity.DTO.cuestionario;

import com.midas.crm.entity.Pregunta.TipoPregunta;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreguntaUpdateDTO {
    private String enunciado;
    private String explicacion;
    
    @Min(value = 1, message = "El puntaje debe ser al menos 1")
    private Integer puntaje;
    
    private Integer orden;
    private TipoPregunta tipo;
    private String estado;
}
