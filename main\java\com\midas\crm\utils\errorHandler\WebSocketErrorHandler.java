package com.midas.crm.utils.errorHandler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageDeliveryException;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.StompSubProtocolErrorHandler;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * Manejador de errores para WebSockets
 * Captura y procesa errores en las comunicaciones WebSocket
 */
@Component
@Slf4j
public class WebSocketErrorHandler extends StompSubProtocolErrorHandler {

    private final ObjectProvider<SimpMessageSendingOperations> messagingTemplateProvider;

    public WebSocketErrorHandler(ObjectProvider<SimpMessageSendingOperations> messagingTemplateProvider) {
        this.messagingTemplateProvider = messagingTemplateProvider;
    }

    @Override
    public Message<byte[]> handleClientMessageProcessingError(Message<byte[]> clientMessage, Throwable ex) {
        log.error("Error procesando mensaje WebSocket: {}", ex.getMessage());

        // Extraer información del mensaje original
        StompHeaderAccessor accessor = StompHeaderAccessor.wrap(clientMessage);
        StompCommand command = accessor.getCommand();

        // Crear respuesta de error
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("type", "ERROR");
        errorDetails.put("command", command != null ? command.toString() : "UNKNOWN");
        errorDetails.put("message", "Error procesando mensaje: " + ex.getMessage());

        // Enviar notificación de error al cliente
        try {
            SimpMessageSendingOperations messagingTemplate = messagingTemplateProvider.getIfAvailable();
            if (messagingTemplate != null) {
                messagingTemplate.convertAndSend("/topic/errors", errorDetails);
            }
        } catch (MessageDeliveryException mde) {
            log.warn("No se pudo enviar notificación de error: {}", mde.getMessage());
        }

        // Crear mensaje de error para el cliente
        StompHeaderAccessor errorAccessor = StompHeaderAccessor.create(StompCommand.ERROR);
        errorAccessor.setMessage(ex.getMessage());
        errorAccessor.setSessionId(accessor.getSessionId());

        return MessageBuilder.createMessage(
                ("Error en WebSocket: " + ex.getMessage()).getBytes(StandardCharsets.UTF_8),
                errorAccessor.getMessageHeaders());
    }

    @Override
    protected Message<byte[]> handleInternal(StompHeaderAccessor errorHeaderAccessor, byte[] errorPayload, Throwable cause, StompHeaderAccessor clientHeaderAccessor) {
        log.error("Error interno en WebSocket: {}", cause != null ? cause.getMessage() : "Desconocido");
        return super.handleInternal(errorHeaderAccessor, errorPayload, cause, clientHeaderAccessor);
    }
}