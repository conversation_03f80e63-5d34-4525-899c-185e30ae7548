import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { MapaTipificacionMapboxComponent } from './mapa-tipificacion-mapbox.component';
import { MapaTipificacionMapboxRoutingModule } from './mapa-tipificacion-mapbox-routing.module';

@NgModule({
  declarations: [
    MapaTipificacionMapboxComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MapaTipificacionMapboxRoutingModule
  ],
  exports: [
    MapaTipificacionMapboxComponent
  ]
})
export class MapaTipificacionMapboxModule { }
