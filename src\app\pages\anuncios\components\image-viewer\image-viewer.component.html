<div class="image-viewer-container" (click)="onClose()">
  <div class="image-viewer-content" @zoomInOut>
    <div class="header">
      <h2>{{ data.title }}</h2>
      <button mat-icon-button (click)="onClose()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    
    <div class="image-container">
      <app-spinner *ngIf="loading"></app-spinner>
      <img [src]="data.imageUrl" 
           [alt]="data.title"
           (load)="onImageLoad()"
           [class.loaded]="!loading">
    </div>
  </div>
</div>
