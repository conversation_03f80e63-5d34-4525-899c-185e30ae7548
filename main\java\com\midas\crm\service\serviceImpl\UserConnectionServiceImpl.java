package com.midas.crm.service.serviceImpl;

import com.midas.crm.config.WebSocketAuthInterceptor;
import com.midas.crm.entity.DTO.websocket.UserStatusDTO;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.UserConnectionService;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Implementación del servicio para gestionar las conexiones de usuarios
 */
@Service
@Slf4j
public class UserConnectionServiceImpl implements UserConnectionService {

    private final UserRepository userRepository;
    private final ObjectProvider<SimpMessagingTemplate> messagingTemplateProvider;

    @Autowired
    private DataSource dataSource;

    @Autowired
    private WebSocketAuthInterceptor webSocketAuthInterceptor;  // Quitar el 'final'

    // Mapa para almacenar los usuarios conectados y su última actividad
    private final Map<Long, LocalDateTime> connectedUsers = new ConcurrentHashMap<>();

    // Mapa para contar las conexiones activas por usuario
    private final Map<Long, AtomicInteger> connectionCounts = new ConcurrentHashMap<>();

    // Mapa para almacenar los IDs de sesión por usuario
    private final Map<Long, Map<String, LocalDateTime>> userSessions = new ConcurrentHashMap<>();

    public UserConnectionServiceImpl(UserRepository userRepository,
                                     ObjectProvider<SimpMessagingTemplate> messagingTemplateProvider) {
        this.userRepository = userRepository;
        this.messagingTemplateProvider = messagingTemplateProvider;
    }

    /**
     * Libera las conexiones inactivas del pool de conexiones
     * Este método debe ser llamado cuando un usuario cierra sesión
     * para evitar que las conexiones queden abiertas
     */
    private void releaseIdleConnections() {
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;

                // Obtener las conexiones inactivas
                int idleConnections = hikariDataSource.getHikariPoolMXBean().getIdleConnections();

                // Suavemente cerrar las conexiones inactivas
                if (idleConnections > 0) {
                    hikariDataSource.getHikariPoolMXBean().softEvictConnections();
                }
            }
        } catch (Exception e) {
            // Ignorar errores al liberar conexiones
        }
    }

    /**
     * Obtiene el mapa de usuarios conectados
     * @return Mapa de usuarios conectados
     */
    public Map<Long, LocalDateTime> getConnectedUsers() {
        return connectedUsers;
    }

    @Override
    public UserStatusDTO connectUser(Long userId) {
        return connectUser(userId, false);
    }

    /**
     * Registra la conexión de un usuario con opción para indicar si es un refresh
     * @param userId ID del usuario que se conecta
     * @param isRefresh Indica si la conexión es resultado de un refresh de página
     * @return DTO con el estado actualizado del usuario
     */
    public UserStatusDTO connectUser(Long userId, boolean isRefresh) {


        // Manejar conexiones anónimas (userId = 0 o null)
        if (userId == null || userId == 0) {

            UserStatusDTO anonymousStatus = new UserStatusDTO();
            anonymousStatus.setUserId(0L);
            anonymousStatus.setUsername("anonymous");
            anonymousStatus.setNombre("Usuario");
            anonymousStatus.setApellido("Anónimo");
            anonymousStatus.setStatus("ONLINE");
            anonymousStatus.setLastActivity(LocalDateTime.now());
            return anonymousStatus;
        }

        // Buscar usuario en la base de datos - necesario para todos los casos
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("Usuario no encontrado"));

        // Si es un refresh y el usuario ya está conectado, simplemente actualizar la actividad
        if (isRefresh && connectedUsers.containsKey(userId)) {


            // Actualizar timestamp de actividad
            connectedUsers.put(userId, LocalDateTime.now());

            // Crear DTO con el estado del usuario
            UserStatusDTO statusDTO = createUserStatusDTO(user, "ONLINE");

            // No incrementar el contador de conexiones en caso de refresh
            // Esto evita que se acumulen conexiones fantasma
            return statusDTO;
        }

        // Verificar si el usuario ya está conectado
        boolean alreadyConnected = connectedUsers.containsKey(userId);

        // Actualizar o registrar la conexión del usuario
        connectedUsers.put(userId, LocalDateTime.now());

        // Incrementar contador de conexiones solo si no es un refresh
        AtomicInteger count = connectionCounts.computeIfAbsent(userId, k -> new AtomicInteger(0));
        int newCount = count.incrementAndGet();

        // Crear DTO con el estado del usuario
        UserStatusDTO statusDTO = createUserStatusDTO(user, "ONLINE");

        // Si es la primera conexión o no estaba conectado previamente, notificar a todos los clientes
        if (newCount == 1 || !alreadyConnected) {
            // Obtener el template de mensajería si está disponible
            SimpMessagingTemplate messagingTemplate = messagingTemplateProvider.getIfAvailable();
            if (messagingTemplate != null) {
                try {
                    // Notificar a todos los clientes sobre la conexión del usuario
                    messagingTemplate.convertAndSend("/topic/users/status", statusDTO);

                    // Enviar una notificación adicional con más detalles
                    Map<String, Object> notification = new HashMap<>();
                    notification.put("type", "USER_CONNECTED");
                    notification.put("userId", user.getId());
                    notification.put("username", user.getUsername());
                    notification.put("status", "ONLINE");
                    notification.put("timestamp", LocalDateTime.now());
                    notification.put("isRefresh", isRefresh);
                    messagingTemplate.convertAndSend("/topic/notifications", notification);
                } catch (Exception e) {
                    // Capturar excepciones para evitar que fallen los inicios de sesión por problemas de WebSocket
                    // Ignorar errores de WebSocket
                }
            }
        }

        return statusDTO;
    }

    /**
     * Registra una sesión específica para un usuario
     * @param userId ID del usuario
     * @param sessionId ID de la sesión
     */
    public void registerSession(Long userId, String sessionId) {
        if (userId == null || userId == 0 || sessionId == null) {
            return;
        }

        // Obtener o crear el mapa de sesiones para este usuario
        Map<String, LocalDateTime> sessions = userSessions.computeIfAbsent(userId, k -> new ConcurrentHashMap<>());

        // Registrar la sesión con la hora actual
        sessions.put(sessionId, LocalDateTime.now());

    }

    /**
     * Elimina una sesión específica para un usuario
     * @param sessionId ID de la sesión
     */
    public void removeSession(String sessionId) {
        if (sessionId == null) {
            return;
        }

        // Buscar el usuario que tiene esta sesión
        for (Map.Entry<Long, Map<String, LocalDateTime>> entry : userSessions.entrySet()) {
            Long userId = entry.getKey();
            Map<String, LocalDateTime> sessions = entry.getValue();

            if (sessions.containsKey(sessionId)) {
                // Eliminar la sesión
                sessions.remove(sessionId);


                // Si no quedan sesiones, desconectar al usuario
                if (sessions.isEmpty()) {
                    disconnectUser(userId);
                }

                return;
            }
        }
    }

    // Mapa para evitar procesar desconexiones duplicadas
    private final Map<Long, Long> processedDisconnections = new ConcurrentHashMap<>();

    // Tiempo de deduplicación para desconexiones (5 segundos)
    private static final long DISCONNECT_DEDUPLICATION_WINDOW = 5000;

    /**
     * Verifica si una desconexión de usuario es duplicada
     * Evita procesar múltiples desconexiones para el mismo usuario en un corto período de tiempo
     * @param userId ID del usuario que se está desconectando
     * @return true si es una desconexión duplicada, false en caso contrario
     */
    private boolean isDuplicateDisconnection(Long userId) {
        long now = System.currentTimeMillis();
        Long lastTime = processedDisconnections.put(userId, now);

        // Limpiar desconexiones antiguas periódicamente
        if (processedDisconnections.size() > 100) {
            processedDisconnections.entrySet().removeIf(entry -> now - entry.getValue() > DISCONNECT_DEDUPLICATION_WINDOW);
        }

        // Es duplicado si ya existe y fue procesado hace menos del tiempo de deduplicación para desconexiones
        return lastTime != null && now - lastTime < DISCONNECT_DEDUPLICATION_WINDOW;
    }

    @Override
    public UserStatusDTO disconnectUser(Long userId) {
        // Verificar si esta desconexión ya fue procesada recientemente
        if (isDuplicateDisconnection(userId)) {

            // Buscar usuario en la base de datos para devolver un DTO válido
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("Usuario no encontrado"));

            // Crear DTO con el estado del usuario (OFFLINE aunque no hayamos hecho nada)
            return createUserStatusDTO(user, "OFFLINE");
        }

        // Forzar la desconexión independientemente del contador
        // Esto asegura que cuando un usuario cierra sesión, se marca como desconectado
        // incluso si hay otras conexiones activas

        // Buscar usuario en la base de datos
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("Usuario no encontrado"));

        // Eliminar la conexión del usuario
        connectedUsers.remove(userId);

        // Resetear el contador de conexiones
        connectionCounts.remove(userId);

        // Limpiar todas las sesiones del usuario
        userSessions.remove(userId);

        // Crear DTO con el estado del usuario
        UserStatusDTO statusDTO = createUserStatusDTO(user, "OFFLINE");

        // Obtener el template de mensajería si está disponible
        SimpMessagingTemplate messagingTemplate = messagingTemplateProvider.getIfAvailable();
        if (messagingTemplate != null) {
            try {
                // Notificar a todos los clientes sobre la desconexión del usuario
                // Enviamos el estado individual primero
                messagingTemplate.convertAndSend("/topic/users/status", statusDTO);

                // Luego enviamos la lista completa de estados para asegurar consistencia
                List<UserStatusDTO> allStatuses = getAllUserStatuses();
                messagingTemplate.convertAndSend("/topic/users/status/all", allStatuses);

                // Enviar una notificación adicional con más detalles
                Map<String, Object> notification = new HashMap<>();
                notification.put("type", "USER_DISCONNECTED");
                notification.put("userId", user.getId());
                notification.put("username", user.getUsername());
                notification.put("nombre", user.getNombre());
                notification.put("apellido", user.getApellido());
                notification.put("status", "OFFLINE");
                notification.put("online", false);
                notification.put("timestamp", LocalDateTime.now());
                messagingTemplate.convertAndSend("/topic/notifications", notification);

            } catch (Exception e) {
                // Ignorar errores de WebSocket
            }
        }



        // Liberar conexiones inactivas para evitar problemas con el pool de conexiones
        releaseIdleConnections();

        return statusDTO;
    }

    @Override
    public List<UserStatusDTO> getAllUserStatuses() {
        // Obtener todos los usuarios y mapearlos a DTOs con su estado actual
        List<UserStatusDTO> statusList = userRepository.findAll().stream()
                .map(user -> createUserStatusDTO(user, isUserConnected(user.getId()) ? "ONLINE" : "OFFLINE"))
                .collect(Collectors.toList());

        // Enviar notificación si el template de mensajería está disponible
        Optional.ofNullable(messagingTemplateProvider.getIfAvailable())
            .ifPresent(messagingTemplate -> {
                Map<String, Object> notification = Map.of(
                    "type", "USERS_STATUS_UPDATED",
                    "payload", statusList,
                    "timestamp", LocalDateTime.now()
                );
                messagingTemplate.convertAndSend("/topic/notifications", notification);
            });

        return statusList;
    }

    /**
     * Marca a todos los usuarios como desconectados
     * Útil cuando se reinicia el servidor o cuando hay problemas de conexión
     */
    @Override
    public void markAllUsersOffline() {

        // Limpiar todas las estructuras de datos
        connectedUsers.clear();
        connectionCounts.clear();
        userSessions.clear();

        // Obtener todos los usuarios y crear DTOs con estado OFFLINE usando streams
        List<UserStatusDTO> statusList = userRepository.findAll().stream()
            .map(user -> createUserStatusDTO(user, "OFFLINE"))
            .collect(Collectors.toList());

        // Enviar notificación si el template de mensajería está disponible
        Optional.ofNullable(messagingTemplateProvider.getIfAvailable())
            .ifPresent(messagingTemplate -> {
                try {
                    // Enviar solo la lista completa de estados
                    messagingTemplate.convertAndSend("/topic/users/status/all", statusList);

                    // Enviar una notificación consolidada con todos los datos
                    Map<String, Object> notification = Map.of(
                        "type", "ALL_USERS_DISCONNECTED",
                        "count", statusList.size(),
                        "timestamp", LocalDateTime.now(),
                        "payload", statusList
                    );
                    messagingTemplate.convertAndSend("/topic/notifications", notification);

                    // Liberar conexiones inactivas
                    releaseIdleConnections();
                } catch (Exception e) {
                    // Ignorar errores de WebSocket
                }
            });
    }

    @Override
    public boolean isUserConnected(Long userId) {
        return connectedUsers.containsKey(userId);
    }

    @Override
    public void updateUserActivity(Long userId) {
        if (connectedUsers.containsKey(userId)) {
            LocalDateTime now = LocalDateTime.now();
            connectedUsers.put(userId, now);

            // Buscar el usuario y actualizar su actividad
            userRepository.findById(userId).ifPresent(user -> {
                // Crear DTO con el estado actualizado
                UserStatusDTO statusDTO = createUserStatusDTO(user, "ONLINE");

                // Enviar notificaciones si el template de mensajería está disponible
                Optional.ofNullable(messagingTemplateProvider.getIfAvailable())
                    .ifPresent(messagingTemplate -> {
                        // Notificar a todos los clientes sobre la actividad del usuario
                        messagingTemplate.convertAndSend("/topic/users/status", statusDTO);

                        // Enviar una notificación adicional con más detalles
                        Map<String, Object> notification = Map.of(
                            "type", "USER_ACTIVITY_UPDATED",
                            "userId", user.getId(),
                            "username", user.getUsername(),
                            "nombre", user.getNombre(),
                            "apellido", user.getApellido(),
                            "status", "ONLINE",
                            "lastActivity", now,
                            "timestamp", now
                        );
                        messagingTemplate.convertAndSend("/topic/notifications", notification);
                    });
            });
        }
    }

    /**
     * Crea un DTO con el estado de conexión de un usuario
     */
    private UserStatusDTO createUserStatusDTO(User user, String status) {
        return UserStatusDTO.builder()
            .userId(user.getId())
            .username(user.getUsername())
            .nombre(user.getNombre())
            .apellido(user.getApellido())
            .status(status)
            .online("ONLINE".equals(status))
            .lastActivity(connectedUsers.getOrDefault(user.getId(), null))
            .build();
    }


    /**
     * Limpia las sesiones huérfanas (sesiones sin autenticación)
     * Este método es llamado periódicamente por el programador de tareas
     */
    public void cleanupOrphanedSessions() {
        // Obtener todas las sesiones registradas usando flatMap
        userSessions.values().stream()
            .flatMap(sessions -> sessions.keySet().stream())
            .filter(sessionId -> !webSocketAuthInterceptor.isSessionActive(sessionId))
            .forEach(this::removeSession);


    }

    /**
     * Obtiene el número de conexiones activas para un usuario
     */
    public int getConnectionCount(Long userId) {
        AtomicInteger count = connectionCounts.get(userId);
        return count != null ? count.get() : 0;
    }

    /**
     * Limpia las conexiones inactivas
     * Este método es llamado periódicamente por el programador de tareas
     */
    public void cleanupInactiveSessions() {
        LocalDateTime threshold = LocalDateTime.now().minusMinutes(30); // 30 minutos de inactividad

        // Usar streams para identificar y desconectar usuarios inactivos
        new HashMap<>(connectedUsers).entrySet().stream()
            .filter(entry -> entry.getValue().isBefore(threshold))
            .peek(entry -> log.info("Usuario inactivo detectado: {} (última actividad: {})",
                  entry.getKey(), entry.getValue()))
            .map(Map.Entry::getKey)
            .forEach(this::disconnectUser);
    }
}