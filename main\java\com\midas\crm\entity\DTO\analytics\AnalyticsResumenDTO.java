package com.midas.crm.entity.DTO.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * DTO para el resumen de analytics de clientes residenciales
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AnalyticsResumenDTO {
    // Métricas generales
    private long totalClientes;
    private long totalInteracciones;
    private long totalSesiones;
    
    // Tasas
    private double tasaRebote;
    private double tasaConversion;
    private double tasaRetencion;
    
    // Tendencias
    private List<ClientesPorPeriodoDTO> clientesPorPeriodo;
    
    // Distribuciones
    private Map<String, DistribucionDTO> distribucionDispositivos;
    private Map<String, DistribucionDTO> distribucionGenero;
    private Map<String, DistribucionDTO> distribucionEdad;
    private Map<String, DistribucionDTO> distribucionUbicacion;
    
    // Comparativas con periodos anteriores
    private double cambioTotalClientes;
    private double cambioTasaConversion;
    private double cambioTasaRetencion;
}
