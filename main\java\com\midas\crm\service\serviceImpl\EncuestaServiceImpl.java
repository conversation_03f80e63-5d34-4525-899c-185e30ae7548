package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.encuesta.*;
import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.EncuestaMapper;
import com.midas.crm.mapper.OpcionRespuestaEncuestaMapper;
import com.midas.crm.mapper.PreguntaEncuestaMapper;
import com.midas.crm.repository.*;
import com.midas.crm.service.EncuestaService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class EncuestaServiceImpl implements EncuestaService {

    private final EncuestaRepository encuestaRepository;
    private final PreguntaEncuestaRepository preguntaEncuestaRepository;
    private final OpcionRespuestaEncuestaRepository opcionRespuestaEncuestaRepository;
    private final RespuestaEncuestaUsuarioRepository respuestaEncuestaUsuarioRepository;
    private final UserRepository userRepository;
    private final SedeRepository sedeRepository;

    @Override
    @Transactional
    public EncuestaDTO createEncuesta(EncuestaCreateDTO dto, Long creadorId) {
        // Obtener el usuario creador
        User creador = userRepository.findById(creadorId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Crear la encuesta
        Encuesta encuesta = EncuestaMapper.toEntity(dto, creador);

        // Asignar sede, coordinador o usuario según el tipo de asignación
        switch (dto.getTipoAsignacion()) {
            case SEDE:
                if (dto.getSedeId() == null) {
                    throw new MidasExceptions(MidasErrorMessage.SEDE_ID_REQUIRED);
                }
                Sede sede = sedeRepository.findById(dto.getSedeId())
                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SEDE_NOT_FOUND));
                encuesta.setSede(sede);
                break;
            case COORDINACION:
                if (dto.getCoordinadorId() == null) {
                    throw new MidasExceptions(MidasErrorMessage.COORDINADOR_ID_REQUIRED);
                }
                User coordinador = userRepository.findById(dto.getCoordinadorId())
                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.COORDINADOR_NOT_FOUND));
                encuesta.setCoordinador(coordinador);
                break;
            case PERSONAL:
                if (dto.getUsuarioId() == null) {
                    throw new MidasExceptions(MidasErrorMessage.USUARIO_ID_REQUIRED);
                }
                User usuario = userRepository.findById(dto.getUsuarioId())
                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
                encuesta.setUsuario(usuario);
                break;
            default:
                // Para tipo TODOS no se requiere asignación específica
                break;
        }

        // Guardar la encuesta
        encuesta = encuestaRepository.save(encuesta);

        // Crear preguntas si se proporcionaron
        if (dto.getPreguntas() != null && !dto.getPreguntas().isEmpty()) {
            for (int i = 0; i < dto.getPreguntas().size(); i++) {
                PreguntaEncuestaCreateDTO preguntaDTO = dto.getPreguntas().get(i);

                // Asignar orden si no se proporcionó
                if (preguntaDTO.getOrden() == null) {
                    preguntaDTO.setOrden(i + 1);
                }

                // Asignar ID de encuesta
                preguntaDTO.setEncuestaId(encuesta.getId());

                // Crear la pregunta
                PreguntaEncuesta pregunta = PreguntaEncuestaMapper.toEntity(preguntaDTO, encuesta);
                pregunta = preguntaEncuestaRepository.save(pregunta);

                // Crear opciones si se proporcionaron y el tipo de pregunta lo requiere
                if (preguntaDTO.getOpciones() != null && !preguntaDTO.getOpciones().isEmpty() &&
                        (pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.OPCION_MULTIPLE ||
                                pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.SELECCION_MULTIPLE ||
                                pregunta.getTipo() == PreguntaEncuesta.TipoPregunta.ESCALA_LIKERT)) {

                    for (int j = 0; j < preguntaDTO.getOpciones().size(); j++) {
                        OpcionRespuestaEncuestaCreateDTO opcionDTO = preguntaDTO.getOpciones().get(j);

                        // Asignar orden si no se proporcionó
                        if (opcionDTO.getOrden() == null) {
                            opcionDTO.setOrden(j + 1);
                        }

                        // Asignar ID de pregunta
                        opcionDTO.setPreguntaId(pregunta.getId());

                        // Crear la opción
                        opcionRespuestaEncuestaRepository.save(
                                OpcionRespuestaEncuestaMapper.toEntity(opcionDTO, pregunta));
                    }
                }
            }
        }

        // Retornar la encuesta creada con todas sus preguntas y opciones
        return getEncuestaCompleta(encuesta.getId());
    }

    @Override
    @Transactional(readOnly = true)
    public EncuestaDTO getEncuestaById(Long id) {
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        return EncuestaMapper.toDTO(encuesta);
    }

    @Override
    @Transactional(readOnly = true)
    public EncuestaDTO getEncuestaCompleta(Long id) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Obtener las preguntas
        List<PreguntaEncuesta> preguntas = preguntaEncuestaRepository.findByEncuestaOrderByOrdenAsc(encuesta);

        // Convertir preguntas a DTOs con sus opciones
        List<PreguntaEncuestaDTO> preguntasDTO = preguntas.stream()
                .map(pregunta -> {
                    // Obtener opciones para cada pregunta
                    var opciones = opcionRespuestaEncuestaRepository.findByPreguntaOrderByOrdenAsc(pregunta)
                            .stream()
                            .map(OpcionRespuestaEncuestaMapper::toDTO)
                            .collect(Collectors.toList());

                    return PreguntaEncuestaMapper.toDTOWithOpciones(pregunta, opciones);
                })
                .collect(Collectors.toList());

        // Obtener estadísticas básicas
        Long totalRespuestas = respuestaEncuestaUsuarioRepository.countByEncuestaId(id);
        Long respuestasCompletadas = respuestaEncuestaUsuarioRepository.countCompletadasByEncuestaId(id);

        // Crear DTO completo
        EncuestaDTO dto = EncuestaMapper.toDTOWithPreguntas(encuesta, preguntasDTO);
        dto.setTotalRespuestas(totalRespuestas);
        dto.setRespuestasCompletadas(respuestasCompletadas);

        return dto;
    }

    @Override
    @Transactional
    public EncuestaDTO updateEncuesta(Long id, EncuestaUpdateDTO dto) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Actualizar campos básicos
        EncuestaMapper.updateEntityFromDTO(encuesta, dto);

        // Actualizar asignaciones según el tipo
        if (dto.getTipoAsignacion() != null) {
            encuesta.setTipoAsignacion(dto.getTipoAsignacion());

            // Limpiar asignaciones previas
            encuesta.setSede(null);
            encuesta.setCoordinador(null);
            encuesta.setUsuario(null);

            // Asignar según el nuevo tipo
            switch (dto.getTipoAsignacion()) {
                case SEDE:
                    if (dto.getSedeId() != null) {
                        Sede sede = sedeRepository.findById(dto.getSedeId())
                                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SEDE_NOT_FOUND));
                        encuesta.setSede(sede);
                    }
                    break;
                case COORDINACION:
                    if (dto.getCoordinadorId() != null) {
                        User coordinador = userRepository.findById(dto.getCoordinadorId())
                                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.COORDINADOR_NOT_FOUND));
                        encuesta.setCoordinador(coordinador);
                    }
                    break;
                case PERSONAL:
                    if (dto.getUsuarioId() != null) {
                        User usuario = userRepository.findById(dto.getUsuarioId())
                                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));
                        encuesta.setUsuario(usuario);
                    }
                    break;
                default:
                    // Para tipo TODOS no se requiere asignación específica
                    break;
            }
        }

        // Guardar cambios
        encuesta = encuestaRepository.save(encuesta);

        return EncuestaMapper.toDTO(encuesta);
    }

    @Override
    @Transactional
    public void deleteEncuesta(Long id) {
        // Obtener la encuesta
        Encuesta encuesta = encuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND));

        // Cambiar estado a inactivo
        encuesta.setEstado("I");
        encuestaRepository.save(encuesta);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getAllEncuestas() {
        return encuestaRepository.findAll().stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getAllEncuestasActivas() {
        return encuestaRepository.findAllActive().stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getEncuestasDisponiblesParaUsuario(Long usuarioId) {
        // Obtener el usuario
        User usuario = userRepository.findById(usuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Obtener sede y coordinador del usuario
        Long sedeId = usuario.getSede() != null ? usuario.getSede().getId() : null;
        Long coordinadorId = usuario.getCoordinador() != null ? usuario.getCoordinador().getId() : null;

        // Obtener encuestas disponibles
        return encuestaRepository.findEncuestasDisponiblesParaUsuario(sedeId, coordinadorId, usuarioId).stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<EncuestaDTO> getEncuestasByCreador(Long creadorId) {
        // Obtener el usuario creador
        User creador = userRepository.findById(creadorId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        return encuestaRepository.findByCreador(creador).stream()
                .map(EncuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EncuestaDTO> getEncuestasPageable(String search, Pageable pageable) {
        return encuestaRepository.findBySearchTerm(search, pageable)
                .map(EncuestaMapper::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getEstadisticasEncuesta(Long encuestaId) {
        // Verificar que la encuesta existe
        if (!encuestaRepository.existsById(encuestaId)) {
            throw new MidasExceptions(MidasErrorMessage.ENCUESTA_NOT_FOUND);
        }

        // Obtener estadísticas básicas
        Long totalRespuestas = respuestaEncuestaUsuarioRepository.countByEncuestaId(encuestaId);
        Long respuestasCompletadas = respuestaEncuestaUsuarioRepository.countCompletadasByEncuestaId(encuestaId);

        // Crear mapa de estadísticas
        Map<String, Object> estadisticas = new HashMap<>();
        estadisticas.put("totalRespuestas", totalRespuestas);
        estadisticas.put("respuestasCompletadas", respuestasCompletadas);

        return estadisticas;
    }
}
