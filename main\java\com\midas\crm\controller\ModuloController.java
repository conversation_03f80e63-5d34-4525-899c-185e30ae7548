package com.midas.crm.controller;

import com.midas.crm.entity.DTO.modulo.ModuloCreateDTO;
import com.midas.crm.entity.DTO.modulo.ModuloDTO;
import com.midas.crm.entity.DTO.modulo.ModuloUpdateDTO;
import com.midas.crm.service.ModuloService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.modulo}")
@RequiredArgsConstructor
public class ModuloController {

    private final ModuloService moduloService;

    /**
     * Crea un nuevo módulo
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<ModuloDTO>> createModulo(@Valid @RequestBody ModuloCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(moduloService::createModulo)
            .map(modulo -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Módulo creado exitosamente", modulo)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Lista todos los módulos
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<ModuloDTO>>> listModulos() {
        return Optional.of(moduloService.listModulos())
            .map(modulos -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de módulos", modulos)
            ))
            .orElse(ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "No se encontraron módulos", Collections.emptyList())
            ));
    }

    /**
     * Lista módulos por curso
     * Implementado con programación funcional
     */
    @GetMapping("/curso/{cursoId}")
    public ResponseEntity<GenericResponse<List<ModuloDTO>>> listModulosByCursoId(@PathVariable Long cursoId) {
        return Optional.ofNullable(cursoId)
            .map(moduloService::listModulosByCursoId)
            .map(modulos -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de módulos por curso", modulos)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene un módulo por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<ModuloDTO>> getModulo(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(moduloService::getModuloById)
            .map(modulo -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Módulo encontrado", modulo)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Actualiza un módulo existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<ModuloDTO>> updateModulo(@PathVariable Long id, @Valid @RequestBody ModuloUpdateDTO dto) {
        return Optional.ofNullable(dto)
            .map(updateDto -> moduloService.updateModulo(id, updateDto))
            .map(modulo -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Módulo actualizado exitosamente", modulo)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina un módulo por su ID
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Object>> deleteModulo(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().build();
        }

        moduloService.deleteModulo(id);
        return ResponseEntity.ok(
            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Módulo eliminado exitosamente", null)
        );
    }
}
