package com.midas.crm.controller;

import com.midas.crm.entity.DTO.cuestionario.CuestionarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioUpdateDTO;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.service.CuestionarioService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.cuestionario}")
@RequiredArgsConstructor
@Slf4j
public class CuestionarioController {

    private final CuestionarioService cuestionarioService;

    /**
     * Crea un nuevo cuestionario
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<CuestionarioDTO>> createCuestionario(@Valid @RequestBody CuestionarioCreateDTO dto) {
        return Optional.ofNullable(dto)
                .map(cuestionarioService::createCuestionario)
                .map(cuestionario -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cuestionario creado exitosamente", cuestionario)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todos los cuestionarios
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<CuestionarioDTO>>> listCuestionarios() {
        List<CuestionarioDTO> cuestionarios = cuestionarioService.listCuestionarios();
        return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de cuestionarios", cuestionarios)
        );
    }

    /**
     * Obtiene un cuestionario por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<CuestionarioDTO>> getCuestionario(@PathVariable Long id) {
        return Optional.ofNullable(id)
                .map(cuestionarioService::getCuestionarioById)
                .map(cuestionario -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cuestionario encontrado", cuestionario)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene un cuestionario por el ID de la lección
     * Implementado con manejo explícito de excepciones para mejor respuesta de error
     */
    @GetMapping("/leccion/{leccionId}")
    public ResponseEntity<GenericResponse<CuestionarioDTO>> getCuestionarioByLeccionId(@PathVariable Long leccionId) {
        try {
            CuestionarioDTO cuestionario = cuestionarioService.getCuestionarioByLeccionId(leccionId);
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cuestionario encontrado", cuestionario)
            );
        } catch (MidasExceptions ex) {
            // Devolver una respuesta con el código de error y mensaje adecuados
            return ResponseEntity.ok(
                    new GenericResponse<>(GenericResponseConstants.ERROR, ex.getMessage(), null)
            );
        } catch (Exception ex) {
            // Para cualquier otra excepción no esperada
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Error interno del servidor", null));
        }
    }

    /**
     * Actualiza un cuestionario existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<CuestionarioDTO>> updateCuestionario(@PathVariable Long id, @Valid @RequestBody CuestionarioUpdateDTO dto) {
        return Optional.ofNullable(dto)
                .map(updateDto -> cuestionarioService.updateCuestionario(id, updateDto))
                .map(cuestionario -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cuestionario actualizado exitosamente", cuestionario)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina un cuestionario
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Object>> deleteCuestionario(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().build();
        }

        cuestionarioService.deleteCuestionario(id);
        return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cuestionario eliminado exitosamente", null)
        );
    }
}
