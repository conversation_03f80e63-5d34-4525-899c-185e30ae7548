<section class="max-w-7xl mx-auto px-2 sm:px-4 py-4 sm:py-6">
  <div class="w-full">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div class="p-3 sm:p-6">
        <div class="flex flex-col mb-3 sm:mb-4">
          <h2 class="text-xl sm:text-2xl font-medium text-blue-900 dark:text-blue-300 m-0">Hola {{ (user$ | async)?.nombre }}</h2>
          <span class="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">¿Qué acción quieres tomar hoy?</span>
        </div>

        <div class="flex flex-wrap gap-2 sm:gap-4 justify-center sm:justify-between items-center py-2 sm:py-4">
          <ng-container *ngIf="isAuthorized$ | async">
            <!-- Nuevo Registro - visible para todos -->
            <div class="flex-1 min-w-[140px] max-w-[300px] flex items-center px-3 sm:px-5 py-2 sm:py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer h-[50px] sm:h-[60px] mb-2 sm:mb-0" routerLink="/">
              <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-sm mr-2 sm:mr-3">
                <svg width="20" height="20" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 sm:w-6 sm:h-6">
                  <g clip-path="url(#clip0_175_1428)">
                    <path d="M36 47.25V42.75C36 40.3631 35.0518 38.0739 33.364 36.386C31.6761 34.6982 29.3869 33.75 27 33.75H11.25C8.86305 33.75 6.57387 34.6982 4.88604 36.386C3.19821 38.0739 2.25 40.3631 2.25 42.75V47.25" stroke="#4CAF50" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M19.125 24.75C24.0956 24.75 28.125 20.7206 28.125 15.75C28.125 10.7794 24.0956 6.75 19.125 6.75C14.1544 6.75 10.125 10.7794 10.125 15.75C10.125 20.7206 14.1544 24.75 19.125 24.75Z" stroke="#4CAF50" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M38.25 24.75L42.75 29.25L51.75 20.25" stroke="#4CAF50" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_175_1428">
                      <rect width="54" height="54" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <span class="text-xs sm:text-sm font-medium whitespace-nowrap">Nueva Tipificación</span>
            </div>

            <!-- Listado - visible solo para usuarios con permiso -->
            <div *ngIf="(user$ | async)?.role !== 'ASESOR'" class="flex-1 min-w-[140px] max-w-[300px] flex items-center px-3 sm:px-5 py-2 sm:py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer h-[50px] sm:h-[60px] mb-2 sm:mb-0" (click)="checkPermissionAndNavigate('/clienteresidencial/listar', 'LISTADO')">
              <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-sm mr-2 sm:mr-3">
                <svg width="20" height="20" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 sm:w-6 sm:h-6">
                  <g clip-path="url(#clip0_175_1439)">
                    <path d="M36 47.25V42.75C36 40.3631 35.0518 38.0739 33.364 36.386C31.6761 34.6982 29.3869 33.75 27 33.75H11.25C8.86305 33.75 6.57387 34.6982 4.88604 36.386C3.19821 38.0739 2.25 40.3631 2.25 42.75V47.25" stroke="#2196F3" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M19.125 24.75C24.0956 24.75 28.125 20.7206 28.125 15.75C28.125 10.7794 24.0956 6.75 19.125 6.75C14.1544 6.75 10.125 10.7794 10.125 15.75C10.125 20.7206 14.1544 24.75 19.125 24.75Z" stroke="#2196F3" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M38.25 24.75L42.75 29.25L51.75 20.25" stroke="#2196F3" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_175_1439">
                      <rect width="54" height="54" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <span class="text-xs sm:text-sm font-medium whitespace-nowrap">Listado de Tipificaciones</span>
            </div>

            <!-- Registrar Venta (SOLO ASESOR) -->
            <div *ngIf="(user$ | async)?.role === 'ASESOR'" class="flex-1 min-w-[140px] max-w-[300px] flex items-center px-3 sm:px-5 py-2 sm:py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer h-[50px] sm:h-[60px] mb-2 sm:mb-0" (click)="checkPermissionAndNavigate('/ventas/crear', 'REGISTRAR_VENTA')">
              <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-sm mr-2 sm:mr-3">
                <svg width="20" height="20" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 sm:w-6 sm:h-6">
                  <circle cx="27" cy="27" r="25" stroke="#2196F3" stroke-width="4"/>
                  <path d="M27 16V38" stroke="#2196F3" stroke-width="4" stroke-linecap="round"/>
                  <path d="M16 27H38" stroke="#2196F3" stroke-width="4" stroke-linecap="round"/>
                </svg>
              </div>
              <span class="text-xs sm:text-sm font-medium whitespace-nowrap">Nueva Venta</span>
            </div>

            <!-- SMS - visible solo para usuarios con permiso -->
            <div class="flex-1 min-w-[140px] max-w-[300px] flex items-center px-3 sm:px-5 py-2 sm:py-3 bg-gradient-to-r from-blue-800 to-blue-900 hover:from-blue-900 hover:to-blue-950 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 cursor-pointer h-[50px] sm:h-[60px] mb-2 sm:mb-0" (click)="checkPermissionAndOpenModal()">
              <div class="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-sm mr-2 sm:mr-3">
                <svg width="20" height="20" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 sm:w-6 sm:h-6">
                  <path d="M13.5 40.5L8.325 45.675C7.6125 46.3875 6.7965 46.5473 5.877 46.1543C4.9575 45.7612 4.4985 45.0578 4.5 44.0438V9C4.5 7.7625 4.941 6.7035 5.823 5.823C6.705 4.9425 7.764 4.5015 9 4.5H45C46.2375 4.5 47.2973 4.941 48.1793 5.823C49.0613 6.705 49.5015 7.764 49.5 9V36C49.5 37.2375 49.0598 38.2972 48.1793 39.1792C47.2988 40.0612 46.239 40.5015 45 40.5H13.5Z" stroke="#0D47A1" stroke-width="4" fill="none"/>
                </svg>
              </div>
              <span class="text-xs sm:text-sm font-medium whitespace-nowrap">Envío de SMS</span>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Modal de Envío de SMS -->
<div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 transition-opacity" [ngClass]="{'hidden': !isModalOpen, 'flex': isModalOpen}">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-2 sm:mx-4 overflow-hidden">
    <!-- Header del modal -->
    <div class="flex justify-between items-center px-3 sm:px-6 py-3 sm:py-4 border-b border-gray-200 dark:border-gray-700">
      <h2 class="text-lg sm:text-xl font-medium text-gray-800 dark:text-white">Envío de SMS</h2>
      <button class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none" (click)="closeModal()">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </div>

    <!-- Cuerpo del modal -->
    <div class="px-3 sm:px-6 py-3 sm:py-4">
      <!-- Nombre del cliente -->
      <div class="mb-4 sm:mb-5 flex flex-col sm:flex-row sm:items-center">
        <label for="nombre-cliente" class="mb-1 sm:mb-0 sm:w-36 text-left sm:text-right sm:mr-4 text-xs sm:text-sm text-gray-700 dark:text-gray-300">Nombre del cliente</label>
        <input type="text" id="nombre-cliente" placeholder="Cliente" class="flex-1 px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
      </div>

      <!-- Número de celular -->
      <div class="mb-4 sm:mb-5 flex flex-col sm:flex-row sm:items-center">
        <label for="nro-celular" class="mb-1 sm:mb-0 sm:w-36 text-left sm:text-right sm:mr-4 text-xs sm:text-sm text-gray-700 dark:text-gray-300">Nro de celular</label>
        <input type="text" id="nro-celular" placeholder="Número de celular" class="flex-1 px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
      </div>

      <!-- Campaña -->
      <div class="mb-4 sm:mb-5 flex flex-col sm:flex-row sm:items-center">
        <label for="campania" class="mb-1 sm:mb-0 sm:w-36 text-left sm:text-right sm:mr-4 text-xs sm:text-sm text-gray-700 dark:text-gray-300">Campaña</label>
        <div class="flex-1 relative">
          <select id="campania" class="w-full px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md text-xs sm:text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
            <option value="">Campaña 01</option>
            <option value="">Campaña 02</option>
            <option value="">Campaña 03</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none text-gray-500 dark:text-gray-400">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- Tipo de SMS -->
      <div class="mb-4 sm:mb-5 flex flex-col sm:flex-row sm:items-center">
        <label for="tipo-sms" class="mb-1 sm:mb-0 sm:w-36 text-left sm:text-right sm:mr-4 text-xs sm:text-sm text-gray-700 dark:text-gray-300">Tipo de SMS</label>
        <div class="flex-1 flex flex-col sm:flex-row">
          <div class="flex-1 relative mb-2 sm:mb-0 sm:mr-2">
            <select id="tipo-sms" class="w-full px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md text-xs sm:text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
              <option value="">Acta de consentimiento</option>
              <option value="">Información general</option>
              <option value="">Recordatorio</option>
            </select>
            <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none text-gray-500 dark:text-gray-400">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
          <button class="px-3 py-1.5 sm:py-2 bg-green-500 hover:bg-green-600 text-white text-xs font-medium rounded-md transition-colors">Ver plantilla</button>
        </div>
      </div>

      <!-- Mensaje -->
      <div class="mb-4 sm:mb-5 flex flex-col sm:flex-row sm:items-start">
        <label for="mensaje" class="mb-1 sm:mb-0 sm:w-36 text-left sm:text-right sm:mr-4 text-xs sm:text-sm text-gray-700 dark:text-gray-300 sm:pt-2">Mensaje</label>
        <textarea id="mensaje" rows="4" class="flex-1 px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 dark:border-gray-600 rounded-md text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none dark:bg-gray-700 dark:text-white"></textarea>
      </div>
    </div>

    <!-- Footer del modal -->
    <div class="px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex justify-end space-x-2 sm:space-x-3">
      <button class="px-3 sm:px-4 py-1.5 sm:py-2 bg-red-500 hover:bg-red-600 text-white text-xs sm:text-sm font-semibold rounded-md transition-colors" (click)="closeModal()">Cerrar X</button>
      <button class="px-3 sm:px-4 py-1.5 sm:py-2 bg-blue-500 hover:bg-blue-600 text-white text-xs sm:text-sm font-semibold rounded-md transition-colors">Enviar ></button>
    </div>
  </div>
</div>

<!-- Sección de anuncios recientes -->
<section class="max-w-7xl mx-auto px-2 sm:px-4 mt-4 sm:mt-6 mb-8 sm:mb-12">
  <div class="w-full min-h-[400px] sm:min-h-[500px] rounded-xl overflow-hidden shadow-md" [ngClass]="{'bg-white/50 dark:bg-transparent': true}">
    <app-anuncios-recientes></app-anuncios-recientes>
  </div>
</section>
