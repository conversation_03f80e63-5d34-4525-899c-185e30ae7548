import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ClienteResidencialListComponent } from './clienteResidencial-list.component';
import { ClienteResidencialListPageRoutingModule } from './clienteResidencial-list-routing.module';
import { StoreModule } from '@ngrx/store';
import { clienteReducer } from '../../store/save';
import { ClienteEffects } from '../../store/save';
import { EffectsModule } from '@ngrx/effects';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { MatIconModule } from '@angular/material/icon';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import {
  MatPaginatorModule,
  MatPaginatorIntl,
} from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ExportByDateDialogComponent } from './export-by-date-dialog.component';
import { ExportByDateRangeDialogComponent } from './export-by-date-range-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { SpinnerModule } from '../../../../shared/indicators/spinner/spinner.module';
import { DataTableModule } from '../../../../shared/components/data-table/data-table.module';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { getEsPaginatorIntl } from '../../../../shared/i18n/es-paginator-intl';

// Módulo compartido
import { SharedComponentsModule } from '../../components/shared/shared-components.module';

@NgModule({
  declarations: [
    ClienteResidencialListComponent,
    ExportByDateDialogComponent,
    ExportByDateRangeDialogComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgbPaginationModule,
    MatIconModule,
    ClienteResidencialListPageRoutingModule,
    StoreModule.forFeature('cliente', clienteReducer),
    EffectsModule.forFeature([ClienteEffects]),
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FlexLayoutModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatDividerModule,
    MatTooltipModule,
    MatDialogModule,
    MatDatepickerModule,
    MatNativeDateModule,
    SpinnerModule,
    DataTableModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatSelectModule,
    MatCheckboxModule,
    SharedComponentsModule,
  ],
  providers: [
    DatePipe,
    { provide: MatPaginatorIntl, useFactory: getEsPaginatorIntl },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ClienteResidencialListModule {}
