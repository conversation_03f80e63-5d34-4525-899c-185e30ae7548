package com.midas.crm.entity.DTO.manual;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManualDTO {
    @NotBlank(message = "El nombre es obligatorio")
    @Size(min = 2, max = 255, message = "El nombre debe tener entre 2 y 255 caracteres")
    private String nombre;

    @NotBlank(message = "El tipo es obligatorio")
    @Pattern(regexp = "^[SBMROT]$", message = "El tipo debe ser S, B, M, R, O o T")
    private String tipo;

    private String archivo; // URL del archivo que llegará desde el frontend (Firebase)

    private String horario; // Horario del manual, "00000" indica inactivo

    private Long userAuthId; // ID del usuario autenticado

    private Boolean isActive; // Estado del manual (activo/inactivo)

    private Boolean is_active; // Campo para compatibilidad con el frontend

    // Método para validar el DTO
    public boolean isValid() {
        return nombre != null && !nombre.trim().isEmpty() &&
                tipo != null && !tipo.trim().isEmpty() &&
                tipo.matches("^[SBMROT]$");
    }

    // Getter y setter personalizados para mantener sincronizados isActive y
    // is_active
    public Boolean getIsActive() {
        // Si is_active está establecido pero isActive no, usar is_active
        if (isActive == null && is_active != null) {
            isActive = is_active;
        }
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
        // Mantener sincronizado is_active
        this.is_active = isActive;
    }

    public Boolean getIs_active() {
        // Si isActive está establecido pero is_active no, usar isActive
        if (is_active == null && isActive != null) {
            is_active = isActive;
        }
        return is_active;
    }

    public void setIs_active(Boolean is_active) {
        this.is_active = is_active;
        // Mantener sincronizado isActive
        this.isActive = is_active;
    }
}
