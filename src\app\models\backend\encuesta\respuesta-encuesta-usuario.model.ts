import { DetalleRespuestaEncuestaUsuario } from './detalle-respuesta-encuesta-usuario.model';
import { User } from '../user';

/**
 * Modelo que representa una respuesta de un usuario a una encuesta completa
 */
export interface RespuestaEncuestaUsuario {
  id: number;
  usuario?: User; // Puede ser null si la encuesta es anónima
  usuarioId?: number;
  usuarioNombre?: string;
  encuestaId: number;
  encuestaTitulo?: string;
  fechaInicio: string;
  fechaFin?: string;
  completada: boolean;
  detallesRespuestas?: DetalleRespuestaEncuestaUsuario[];
  fechaCreacion: string;
  fechaActualizacion: string;
}

/**
 * Modelo para iniciar una nueva respuesta de usuario a una encuesta
 */
export interface RespuestaEncuestaUsuarioCreateRequest {
  usuarioId?: number; // Puede ser null si la encuesta es anónima
  encuestaId: number;
}
