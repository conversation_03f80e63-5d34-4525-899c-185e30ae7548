package com.midas.crm.service;

import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaDTO;

import java.util.List;
import java.util.Map;

/**
 * Servicio para gestionar preguntas de encuestas
 */
public interface PreguntaEncuestaService {

    /**
     * Crea una nueva pregunta para una encuesta
     */
    PreguntaEncuestaDTO createPregunta(PreguntaEncuestaCreateDTO dto);

    /**
     * Obtiene una pregunta por su ID
     */
    PreguntaEncuestaDTO getPreguntaById(Long id);

    /**
     * Obtiene una pregunta por su ID con todas sus opciones
     */
    PreguntaEncuestaDTO getPreguntaCompleta(Long id);

    /**
     * Actualiza una pregunta existente
     */
    PreguntaEncuestaDTO updatePregunta(Long id, PreguntaEncuestaCreateDTO dto);

    /**
     * Elimina una pregunta (cambio de estado a inactivo)
     */
    void deletePregunta(Long id);

    /**
     * Obtiene todas las preguntas de una encuesta
     */
    List<PreguntaEncuestaDTO> getPreguntasByEncuestaId(Long encuestaId);

    /**
     * Obtiene estadísticas de respuestas para una pregunta específica
     */
    Map<String, Object> getEstadisticasPregunta(Long preguntaId);

    /**
     * Reordena las preguntas de una encuesta
     */
    List<PreguntaEncuestaDTO> reordenarPreguntas(Long encuestaId, List<Long> nuevosOrdenes);
}
