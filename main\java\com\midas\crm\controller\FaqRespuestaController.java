package com.midas.crm.controller;

import com.midas.crm.entity.DTO.faq.FaqRespuestaDTO;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.FaqService;
import com.midas.crm.utils.GenericResponse;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("${api.route.faq-respuestas}")
public class FaqRespuestaController {

    @Autowired
    private FaqService faqService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @PostMapping
    public ResponseEntity<GenericResponse<FaqRespuestaDTO>> createRespuesta(@Valid @RequestBody FaqRespuestaDTO respuestaDTO, @RequestParam Long faqId, @RequestParam Long usuarioId) {
        FaqRespuestaDTO savedRespuesta = faqService.agregarRespuesta(faqId, respuestaDTO, usuarioId);

        GenericResponse<FaqRespuestaDTO> response = new GenericResponse<>(1, "Respuesta creada exitosamente", savedRespuesta);

        // Notificar a todos los clientes sobre la nueva respuesta
        messagingTemplate.convertAndSend("/topic/faq-respuestas/" + faqId, faqService.getRespuestasByFaqId(faqId));

        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<FaqRespuestaDTO>> updateRespuesta(@Valid @RequestBody FaqRespuestaDTO respuestaDTO, @PathVariable Long id) {
        FaqRespuestaDTO updatedRespuesta = faqService.actualizarRespuesta(id, respuestaDTO);

        GenericResponse<FaqRespuestaDTO> response = new GenericResponse<>(1, "Respuesta actualizada exitosamente", updatedRespuesta);

        // Notificar a todos los clientes sobre la actualización
        if (updatedRespuesta.getFaqId() != null) {
            messagingTemplate.convertAndSend("/topic/faq-respuestas/" + updatedRespuesta.getFaqId(),
                    faqService.getRespuestasByFaqId(updatedRespuesta.getFaqId()));
        }

        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Void>> deleteRespuesta(@PathVariable Long id, @RequestParam Long faqId) {
        faqService.eliminarRespuesta(id);

        GenericResponse<Void> response = new GenericResponse<>(1, "Respuesta eliminada exitosamente", null);

        // Notificar a todos los clientes sobre la eliminación
        messagingTemplate.convertAndSend("/topic/faq-respuestas/" + faqId, faqService.getRespuestasByFaqId(faqId));

        return ResponseEntity.ok(response);
    }

    @GetMapping("/faq/{faqId}")
    public ResponseEntity<GenericResponse<List<FaqRespuestaDTO>>> getRespuestasByFaqId(@PathVariable Long faqId) {
        List<FaqRespuestaDTO> respuestas = faqService.getRespuestasByFaqId(faqId);

        GenericResponse<List<FaqRespuestaDTO>> response = new GenericResponse<>(1, "Respuestas obtenidas exitosamente", respuestas);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<List<FaqRespuestaDTO>>> getRespuestasByUsuarioId(@PathVariable Long usuarioId) {
        List<FaqRespuestaDTO> respuestas = faqService.getRespuestasByUsuarioId(usuarioId);

        GenericResponse<List<FaqRespuestaDTO>> response = new GenericResponse<>(1, "Respuestas obtenidas exitosamente", respuestas);

        return ResponseEntity.ok(response);
    }

    // Endpoints WebSocket para respuestas
    @MessageMapping("/faq-respuestas.list")
    @SendTo("/topic/faq-respuestas")
    public List<FaqRespuestaDTO> processRespuestasList(@Payload Map<String, Object> payload) {
        Long faqId = Long.valueOf(payload.get("faqId").toString());
        return faqService.getRespuestasByFaqId(faqId);
    }

    @MessageMapping("/faq-respuestas.create")
    @SendTo("/topic/faq-respuestas")
    public List<FaqRespuestaDTO> createRespuestaWs(@Payload Map<String, Object> payload, SimpMessageHeaderAccessor headerAccessor) {
        // Extraer datos del payload
        Long faqId = Long.valueOf(payload.get("faqId").toString());
        String contenido = payload.get("contenido").toString();

        // Crear el DTO de respuesta
        FaqRespuestaDTO respuestaDTO = new FaqRespuestaDTO();
        respuestaDTO.setContenido(contenido);
        respuestaDTO.setFaqId(faqId);

        // Obtener el usuario autenticado si está disponible
        Long usuarioId = null;
        if (headerAccessor.getUser() instanceof Authentication authentication &&
                authentication.getPrincipal() instanceof UserPrincipal userPrincipal) {
            usuarioId = userPrincipal.getId();
        }

        // Si tenemos un usuario, agregar la respuesta
        if (usuarioId != null) {
            faqService.agregarRespuesta(faqId, respuestaDTO, usuarioId);
        }

        // Devolver la lista actualizada
        return faqService.getRespuestasByFaqId(faqId);
    }
}
