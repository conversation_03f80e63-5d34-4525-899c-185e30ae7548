package com.midas.crm.entity.DTO.faq;

import com.midas.crm.entity.EstadoFaq;
import com.midas.crm.entity.DTO.user.UserDTO;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class FaqDTO {
    private Long id;
    private String pregunta;
    private String respuesta;
    private String categoria;
    private Integer views;

    // Usuario que crea la pregunta
    private Long usuarioPreguntaId;
    private String usuarioPreguntaNombre;
    private UserDTO usuarioPregunta;

    // Usuario que responde la pregunta
    private Long usuarioRespuestaId;
    private String usuarioRespuestaNombre;
    private UserDTO usuarioRespuesta;

    // Indica si la pregunta es pública o privada
    private Boolean esPublica = true;

    // Indica si la pregunta ha sido respondida
    private Boolean respondida = false;

    // Estado de la pregunta (ABIERTA, CERRADA)
    private EstadoFaq estado = EstadoFaq.ABIERTA;

    // Archivos adjuntos a la pregunta
    private List<FileFaqDTO> archivos = new ArrayList<>();

    // Respuestas a esta pregunta
    private List<FaqRespuestaDTO> respuestas = new ArrayList<>();

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}