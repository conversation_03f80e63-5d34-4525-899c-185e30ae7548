<div class="anuncios-recientes bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
  <ng-container *ngIf="!loading; else loadingTemplate">
    <ng-container *ngIf="anunciosFiltrados$ | async as anuncios">
      <!-- Solo mostrar el header si hay anuncios disponibles -->
      <div *ngIf="anuncios.length > 0" class="header-container">
        <h3 class="anuncios-title">
          Anuncios recientes
          <ng-container *ngIf="!isAdmin; else adminBadge">
            <span *ngIf="userSedeId" class="sede-badge">
              <i class="fas fa-map-marker-alt"></i> {{ userSedeName ? userSedeName : 'tu sede' }}
            </span>
          </ng-container>
          <ng-template #adminBadge>
            <span class="sede-badge admin-badge">
              <i class="fas fa-globe"></i> Todas las sedes
            </span>
          </ng-template>
        </h3>

        <div class="search-container">
          <input type="text" [(ngModel)]="searchTerm" placeholder="Buscar" class="search-input" (input)="searchTermSubject.next(searchTerm)">
          <button class="search-button clear-button" *ngIf="searchTerm" (click)="clearSearch()">
            <i class="fas fa-times"></i>
          </button>
          <button class="search-button" (click)="searchTermSubject.next(searchTerm)">
            <i class="fas fa-search"></i>
          </button>
        </div>
      </div>

      <!-- Mensaje cuando no hay resultados de búsqueda -->
      <div *ngIf="anuncios.length === 0 && searchTerm" class="no-results-message">
        <i class="fas fa-search"></i>
        <p>No se encontraron anuncios que coincidan con "<strong>{{searchTerm}}</strong>"</p>
        <button class="clear-search-btn" (click)="clearSearch()">Limpiar búsqueda</button>
      </div>

      <!-- Mostrar grid adaptativo solo si hay anuncios disponibles -->
      <div *ngIf="anuncios.length > 0" class="anuncios-grid" [ngClass]="{'single-item': anuncios.length === 1, 'two-items': anuncios.length === 2, 'multi-items': anuncios.length > 2}">


        <!-- Caso de 1 anuncio (centrado con tamaño normal) -->
        <ng-container *ngIf="anuncios.length === 1">
          <!-- Contenedor centrado para un solo anuncio -->
          <div class="single-anuncio-container">
            <!-- Único anuncio con tamaño normal (no destacado) -->
            <div class="anuncio-card normal">
              <div class="card-info-container">
                <div *ngIf="anuncios[0].categoria" class="card-badge">{{ anuncios[0].categoria }}</div>
                <h4 class="card-title">{{ anuncios[0].titulo }}</h4>
                <p class="card-description">{{ anuncios[0].descripcion }}</p>
              </div>
              <div class="card-image-container">
                <div class="image-placeholder"></div>
                <img [src]="anuncios[0].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[0].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[0].imagenUrl || pictureDefault] = true">
              </div>
              <div class="card-overlay">
                <div class="card-meta">
                  <span class="time-ago">{{ formatTimeDifference(anuncios[0].fechaPublicacion) }}</span>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <!-- Caso de 2 anuncios -->
        <ng-container *ngIf="anuncios.length === 2">
          <!-- Primer anuncio -->
          <div class="feature-column">
            <div class="anuncio-card feature">
              <div class="card-info-container">
                <div *ngIf="anuncios[0].categoria" class="card-badge">{{ anuncios[0].categoria }}</div>
                <h4 class="card-title">{{ anuncios[0].titulo }}</h4>
                <p class="card-description">{{ anuncios[0].descripcion }}</p>
              </div>
              <div class="card-image-container">
                <div class="image-placeholder"></div>
                <img [src]="anuncios[0].imagenUrl || pictureDefault" alt="Anuncio destacado" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[0].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[0].imagenUrl || pictureDefault] = true">
              </div>
              <div class="card-overlay">
                <div class="card-meta">
                  <span class="time-ago">{{ formatTimeDifference(anuncios[0].fechaPublicacion) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Segundo anuncio -->
          <div class="feature-column">
            <div class="anuncio-card feature"> <!-- Añadimos la clase feature para igualar con el primer anuncio -->
              <div class="card-info-container">
                <div *ngIf="anuncios[1].categoria" class="card-badge">{{ anuncios[1].categoria }}</div>
                <h4 class="card-title">{{ anuncios[1].titulo }}</h4>
                <p class="card-description">{{ anuncios[1].descripcion }}</p>
              </div>
              <div class="card-image-container">
                <div class="image-placeholder"></div>
                <img [src]="anuncios[1].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[1].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[1].imagenUrl || pictureDefault] = true">
              </div>
              <div class="card-overlay">
                <div class="card-meta">
                  <span class="time-ago">{{ formatTimeDifference(anuncios[1].fechaPublicacion) }}</span>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <!-- Si hay exactamente 3 anuncios, mostrar en formato especial -->
        <ng-container *ngIf="anuncios.length === 3">
          <div class="grid-column">
            <!-- Primera fila con 2 anuncios -->
            <div class="grid-row">
              <!-- Anuncio 1 -->
              <div class="anuncio-card">
                <div class="card-info-container">
                  <div *ngIf="anuncios[0].categoria" class="card-badge">{{ anuncios[0].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[0].titulo }}</h4>
                  <p class="card-description">{{ anuncios[0].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[0].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[0].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[0].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[0].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>

              <!-- Anuncio 2 -->
              <div class="anuncio-card">
                <div class="card-info-container">
                  <div *ngIf="anuncios[1].categoria" class="card-badge">{{ anuncios[1].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[1].titulo }}</h4>
                  <p class="card-description">{{ anuncios[1].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[1].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[1].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[1].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[1].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Anuncio 3 centrado -->
            <div class="grid-row third-anuncio-container" style="margin-top: 24px;">
              <!-- Anuncio 3 centrado -->
              <div class="anuncio-card feature third-anuncio">
                <div class="card-info-container">
                  <div *ngIf="anuncios[2].categoria" class="card-badge">{{ anuncios[2].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[2].titulo }}</h4>
                  <p class="card-description">{{ anuncios[2].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[2].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[2].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[2].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[2].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>

        <!-- Si hay 4 o más anuncios, mostrar en formato de rejilla -->
        <ng-container *ngIf="anuncios.length >= 4">
          <div class="grid-column">
            <!-- Primera fila con 2 anuncios -->
            <div class="grid-row">
              <!-- Anuncio 1 -->
              <div class="anuncio-card">
                <div class="card-info-container">
                  <div *ngIf="anuncios[0].categoria" class="card-badge">{{ anuncios[0].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[0].titulo }}</h4>
                  <p class="card-description">{{ anuncios[0].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[0].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[0].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[0].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[0].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>

              <!-- Anuncio 2 -->
              <div class="anuncio-card">
                <div class="card-info-container">
                  <div *ngIf="anuncios[1].categoria" class="card-badge">{{ anuncios[1].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[1].titulo }}</h4>
                  <p class="card-description">{{ anuncios[1].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[1].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[1].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[1].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[1].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Tercera fila con anuncio 3 centrado cuando hay exactamente 3 anuncios -->
            <div class="grid-row-centered third-anuncio" *ngIf="anuncios.length === 3">
              <!-- Anuncio 3 centrado -->
              <div class="centered-card anuncio-card feature">
                <div class="card-info-container">
                  <div *ngIf="anuncios[2].categoria" class="card-badge">{{ anuncios[2].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[2].titulo }}</h4>
                  <p class="card-description">{{ anuncios[2].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[2].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[2].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[2].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[2].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Segunda fila con 2 anuncios cuando hay 4 o más anuncios -->
            <div class="grid-row" *ngIf="anuncios.length >= 4">
              <!-- Anuncio 3 -->
              <div class="anuncio-card">
                <div class="card-info-container">
                  <div *ngIf="anuncios[2].categoria" class="card-badge">{{ anuncios[2].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[2].titulo }}</h4>
                  <p class="card-description">{{ anuncios[2].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[2].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[2].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[2].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[2].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>

              <!-- Anuncio 4 -->
              <div class="anuncio-card">
                <div class="card-info-container">
                  <div *ngIf="anuncios[3].categoria" class="card-badge">{{ anuncios[3].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[3].titulo }}</h4>
                  <p class="card-description">{{ anuncios[3].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[3].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[3].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[3].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[3].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Tercera fila con anuncio 5 centrado cuando hay exactamente 5 anuncios -->
            <div class="grid-row fifth-anuncio-container" *ngIf="anuncios.length === 5" style="margin-top: 24px;">
              <!-- Anuncio 5 centrado -->
              <div class="anuncio-card feature fifth-anuncio">
                <div class="card-info-container">
                  <div *ngIf="anuncios[4].categoria" class="card-badge">{{ anuncios[4].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[4].titulo }}</h4>
                  <p class="card-description">{{ anuncios[4].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[4].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[4].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[4].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[4].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Tercera fila normal para 6 anuncios -->
            <div class="grid-row" *ngIf="anuncios.length >= 6">
              <!-- Anuncio 5 si hay 6 anuncios -->
              <div class="anuncio-card">
                <div class="card-info-container">
                  <div *ngIf="anuncios[4].categoria" class="card-badge">{{ anuncios[4].categoria }}</div>
                  <h4 class="card-title">{{ anuncios[4].titulo }}</h4>
                  <p class="card-description">{{ anuncios[4].descripcion }}</p>
                </div>
                <div class="card-image-container">
                  <div class="image-placeholder"></div>
                  <img [src]="anuncios[4].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[4].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[4].imagenUrl || pictureDefault] = true">
                </div>
                <div class="card-overlay">
                  <div class="card-meta">
                    <span class="time-ago">{{ formatTimeDifference(anuncios[4].fechaPublicacion) }}</span>
                  </div>
                </div>
              </div>

              <!-- Anuncio 6 si existe -->
              <ng-container *ngIf="anuncios.length >= 6">
                <div class="anuncio-card">
                  <div class="card-info-container">
                    <div *ngIf="anuncios[5].categoria" class="card-badge">{{ anuncios[5].categoria }}</div>
                    <h4 class="card-title">{{ anuncios[5].titulo }}</h4>
                    <p class="card-description">{{ anuncios[5].descripcion }}</p>
                  </div>
                  <div class="card-image-container">
                    <div class="image-placeholder"></div>
                    <img [src]="anuncios[5].imagenUrl || pictureDefault" alt="Anuncio" class="card-image" loading="lazy" [ngClass]="{'loaded': loadedImages[anuncios[5].imagenUrl] || loadedImages[pictureDefault]}" (load)="loadedImages[anuncios[5].imagenUrl || pictureDefault] = true">
                  </div>
                  <div class="card-overlay">
                    <div class="card-meta">
                      <span class="time-ago">{{ formatTimeDifference(anuncios[5].fechaPublicacion) }}</span>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </div>

      <!-- Mensaje solo si no hay anuncios disponibles pero no mostrar nada más-->
      <div *ngIf="anuncios.length === 0" class="no-anuncios">
        <p>No hay anuncios disponibles en este momento.</p>
      </div>

      <!-- Paginación profesional para administradores -->
      <div *ngIf="anuncios.length > 0 && isAdmin" class="pagination-container">
        <mat-paginator
          [ngClass]="{'dark-theme-paginator': isDarkTheme}"
          [length]="totalElements > 0 ? totalElements : 10"
          [pageSize]="maxAnuncios"
          [pageIndex]="currentPage"
          [hidePageSize]="false"
          [pageSizeOptions]="[6, 12, 24, 48]"
          [showFirstLastButtons]="true"
          (page)="onPageChange($event)"
          aria-label="Seleccionar página"
          class="custom-paginator">
        </mat-paginator>
      </div>

      <!-- Paginación para usuarios no admin - solo si hay suficientes elementos -->
      <div *ngIf="anuncios.length > 0 && !isAdmin && (totalElements > maxAnuncios || totalPages > 1)" class="pagination-container">
        <mat-paginator
          [ngClass]="{'dark-theme-paginator': isDarkTheme}"
          [length]="totalElements"
          [pageSize]="maxAnuncios"
          [pageIndex]="currentPage"
          [hidePageSize]="true"
          [pageSizeOptions]="[6]"
          [showFirstLastButtons]="true"
          (page)="onPageChange($event)"
          aria-label="Seleccionar página"
          class="custom-paginator">
        </mat-paginator>
      </div>
    </ng-container>
  </ng-container>

  <!-- Template para estado de carga - minimalista -->
  <ng-template #loadingTemplate>
    <div class="loading-container">
      <div class="header-skeleton"></div>
      <div class="anuncios-grid-skeleton">
        <div class="feature-skeleton"></div>
        <div class="grid-skeleton">
          <div class="grid-row-skeleton">
            <div class="card-skeleton"></div>
            <div class="card-skeleton"></div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</div>