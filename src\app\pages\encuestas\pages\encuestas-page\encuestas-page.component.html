<div class="container mx-auto p-4">
  <div class="flex flex-col md:flex-row justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
      Encuestas de Psicología
    </h1>

    <div class="flex flex-col md:flex-row gap-4">
      <!-- Buscador -->
      <div class="relative">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (keyup.enter)="applyFilter()"
          placeholder="Buscar encuestas..."
          class="px-4 py-2 pr-10 rounded-lg border border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white w-full md:w-64"
        />
        <button
          (click)="applyFilter()"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400"
        >
          <mat-icon>search</mat-icon>
        </button>
      </div>

      <!-- Botón para crear nueva encuesta (solo para admin y psicólogos) -->
      <button
        *ngIf="isAdmin || isPsicologo"
        (click)="openCreateForm()"
        class="h-10 rounded bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white flex items-center justify-center px-3"
      >
        <mat-icon class="text-white">add</mat-icon>
        <span class="hidden sm:inline-block ml-1 text-sm">Nueva Encuesta</span>
      </button>
    </div>
  </div>

  <!-- Tabs para diferentes vistas -->
  <mat-tab-group class="mb-6">
    <mat-tab label="Todas las Encuestas" *ngIf="isAdmin || isPsicologo">
      <!-- Tabla de encuestas -->
      <div class="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow">
        <table mat-table [dataSource]="encuestas" class="w-full">
          <!-- Título -->
          <ng-container matColumnDef="titulo">
            <th mat-header-cell *matHeaderCellDef class="dark:text-gray-300">
              Título
            </th>
            <td mat-cell *matCellDef="let encuesta" class="dark:text-white">
              {{ encuesta.titulo }}
            </td>
          </ng-container>

          <!-- Descripción -->
          <ng-container matColumnDef="descripcion">
            <th mat-header-cell *matHeaderCellDef class="dark:text-gray-300">
              Descripción
            </th>
            <td mat-cell *matCellDef="let encuesta" class="dark:text-white">
              {{
                encuesta.descripcion?.length > 50
                  ? (encuesta.descripcion | slice : 0 : 50) + "..."
                  : encuesta.descripcion
              }}
            </td>
          </ng-container>

          <!-- Tipo de Asignación -->
          <ng-container matColumnDef="tipoAsignacion">
            <th mat-header-cell *matHeaderCellDef class="dark:text-gray-300">
              Asignación
            </th>
            <td mat-cell *matCellDef="let encuesta" class="dark:text-white">
              {{ getTipoAsignacionText(encuesta.tipoAsignacion) }}
            </td>
          </ng-container>

          <!-- Estado -->
          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef class="dark:text-gray-300">
              Estado
            </th>
            <td mat-cell *matCellDef="let encuesta">
              <span
                [ngClass]="
                  encuesta.estado === 'A'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                "
                class="px-2 py-1 rounded-full text-xs font-medium"
              >
                {{ encuesta.estado === "A" ? "Activa" : "Inactiva" }}
              </span>
            </td>
          </ng-container>

          <!-- Acciones -->
          <ng-container matColumnDef="acciones">
            <th mat-header-cell *matHeaderCellDef class="dark:text-gray-300">
              Acciones
            </th>
            <td mat-cell *matCellDef="let encuesta">
              <div class="flex gap-2">
                <!-- Ver detalle -->
                <button
                  mat-icon-button
                  color="primary"
                  [routerLink]="['/encuestas/detalle', encuesta.id]"
                  matTooltip="Ver detalle"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                >
                  <mat-icon>visibility</mat-icon>
                </button>

                <!-- Editar -->
                <button
                  mat-icon-button
                  color="accent"
                  (click)="openEditForm(encuesta)"
                  matTooltip="Editar"
                  class="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 transition-colors"
                >
                  <mat-icon>edit</mat-icon>
                </button>

                <!-- Eliminar -->
                <button
                  mat-icon-button
                  color="warn"
                  (click)="deleteEncuesta(encuesta.id)"
                  matTooltip="Eliminar"
                  class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                >
                  <mat-icon>delete</mat-icon>
                </button>

                <!-- Ver resultados -->
                <button
                  mat-icon-button
                  color="primary"
                  [routerLink]="['/encuestas/resultados', encuesta.id]"
                  matTooltip="Ver resultados"
                  class="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 transition-colors"
                >
                  <mat-icon>bar_chart</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr
            mat-header-row
            *matHeaderRowDef="[
              'titulo',
              'descripcion',
              'tipoAsignacion',
              'estado',
              'acciones'
            ]"
          ></tr>
          <tr
            mat-row
            *matRowDef="
              let row;
              columns: [
                'titulo',
                'descripcion',
                'tipoAsignacion',
                'estado',
                'acciones'
              ]
            "
          ></tr>
        </table>

        <!-- Mensaje cuando no hay datos -->
        <div
          *ngIf="encuestas.length === 0 && !loading"
          class="p-6 text-center text-gray-500 dark:text-gray-400"
        >
          No se encontraron encuestas
        </div>

        <!-- Spinner de carga -->
        <div *ngIf="loading" class="p-6 flex justify-center">
          <mat-spinner diameter="40"></mat-spinner>
        </div>

        <!-- Paginador -->
        <mat-paginator
          [length]="totalItems"
          [pageSize]="pageSize"
          [pageSizeOptions]="pageSizeOptions"
          [pageIndex]="currentPage"
          (page)="onPageChange($event)"
          class="dark:bg-gray-800 dark:text-white"
        ></mat-paginator>
      </div>
    </mat-tab>

    <mat-tab label="Encuestas Disponibles">
      <!-- Tarjetas de encuestas disponibles -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-4"
      >
        <mat-card
          *ngFor="let encuesta of encuestasDisponibles"
          class="dark:bg-gray-800"
        >
          <mat-card-header>
            <mat-card-title class="dark:text-white">{{
              encuesta.titulo
            }}</mat-card-title>
            <mat-card-subtitle class="dark:text-gray-300">
              {{ getTipoAsignacionText(encuesta.tipoAsignacion) }}
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content class="dark:text-gray-300 mt-4">
            <p>{{ encuesta.descripcion }}</p>

            <div class="mt-4 flex flex-col gap-2">
              <div *ngIf="encuesta.fechaInicio" class="flex items-center gap-2">
                <mat-icon class="text-gray-500 dark:text-gray-400"
                  >event</mat-icon
                >
                <span
                  >Inicia:
                  {{ encuesta.fechaInicio | date : "dd/MM/yyyy" }}</span
                >
              </div>

              <div *ngIf="encuesta.fechaFin" class="flex items-center gap-2">
                <mat-icon class="text-gray-500 dark:text-gray-400"
                  >event_busy</mat-icon
                >
                <span
                  >Finaliza: {{ encuesta.fechaFin | date : "dd/MM/yyyy" }}</span
                >
              </div>

              <div
                *ngIf="encuesta.tiempoLimite"
                class="flex items-center gap-2"
              >
                <mat-icon class="text-gray-500 dark:text-gray-400"
                  >timer</mat-icon
                >
                <span>Tiempo límite: {{ encuesta.tiempoLimite }} minutos</span>
              </div>
            </div>
          </mat-card-content>

          <mat-card-actions align="end" class="flex justify-end gap-2 p-2">
            <button
              mat-button
              color="primary"
              [routerLink]="['/encuestas/responder', encuesta.id]"
              class="bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 px-4 py-1 rounded-md transition-colors"
            >
              RESPONDER
            </button>

            <button
              mat-button
              *ngIf="isAdmin || isPsicologo"
              [routerLink]="['/encuestas/detalle', encuesta.id]"
              class="bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 px-4 py-1 rounded-md transition-colors"
            >
              VER DETALLE
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <!-- Mensaje cuando no hay encuestas disponibles -->
      <div
        *ngIf="encuestasDisponibles.length === 0 && !loading"
        class="p-6 text-center text-gray-500 dark:text-gray-400"
      >
        No hay encuestas disponibles para ti en este momento
      </div>

      <!-- Spinner de carga -->
      <div *ngIf="loading" class="p-6 flex justify-center">
        <mat-spinner diameter="40"></mat-spinner>
      </div>
    </mat-tab>

    <mat-tab label="Mis Encuestas Creadas" *ngIf="isAdmin || isPsicologo">
      <!-- Tarjetas de encuestas creadas por el usuario -->
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-4"
      >
        <mat-card
          *ngFor="let encuesta of encuestasCreadas"
          class="dark:bg-gray-800"
        >
          <mat-card-header>
            <mat-card-title class="dark:text-white">{{
              encuesta.titulo
            }}</mat-card-title>
            <mat-card-subtitle class="dark:text-gray-300">
              {{ getTipoAsignacionText(encuesta.tipoAsignacion) }}
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content class="dark:text-gray-300 mt-4">
            <p>{{ encuesta.descripcion }}</p>

            <div class="mt-4 flex flex-col gap-2">
              <div class="flex items-center gap-2">
                <mat-icon class="text-gray-500 dark:text-gray-400"
                  >question_answer</mat-icon
                >
                <span>Respuestas: {{ encuesta.totalRespuestas || 0 }}</span>
              </div>

              <div class="flex items-center gap-2">
                <mat-icon class="text-gray-500 dark:text-gray-400"
                  >check_circle</mat-icon
                >
                <span
                  >Completadas: {{ encuesta.respuestasCompletadas || 0 }}</span
                >
              </div>
            </div>
          </mat-card-content>

          <mat-card-actions align="end" class="flex justify-end gap-2 p-2">
            <button
              mat-button
              color="accent"
              (click)="openEditForm(encuesta)"
              class="bg-amber-100 text-amber-700 hover:bg-amber-200 dark:bg-amber-900 dark:text-amber-300 dark:hover:bg-amber-800 px-4 py-1 rounded-md transition-colors"
            >
              EDITAR
            </button>

            <button
              mat-button
              color="primary"
              [routerLink]="['/encuestas/resultados', encuesta.id]"
              class="bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900 dark:text-purple-300 dark:hover:bg-purple-800 px-4 py-1 rounded-md transition-colors"
            >
              RESULTADOS
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <!-- Mensaje cuando no hay encuestas creadas -->
      <div
        *ngIf="encuestasCreadas.length === 0 && !loading"
        class="p-6 text-center text-gray-500 dark:text-gray-400"
      >
        No has creado ninguna encuesta todavía
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
