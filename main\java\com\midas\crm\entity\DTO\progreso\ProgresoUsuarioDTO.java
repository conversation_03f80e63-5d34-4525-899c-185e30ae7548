package com.midas.crm.entity.DTO.progreso;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgresoUsuarioDTO {
    private Long id;
    private Long usuarioId;
    private String usuarioNombre;
    private Long leccionId;
    private String leccionTitulo;
    private Boolean completado;
    private Integer segundosVistos;
    private Integer porcentajeCompletado;
    private Integer ultimaPosicion;
    private LocalDateTime fechaCreacion;
    private LocalDateTime ultimaVisualizacion;
}
