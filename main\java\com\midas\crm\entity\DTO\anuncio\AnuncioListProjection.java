package com.midas.crm.entity.DTO.anuncio;

public interface AnuncioListProjection {
    Long getId();
    String getTitulo();
    String getDescripcion();
    String getImagenUrl();
    String getCategoria();
    String getFechaPublicacion(); // se puede convertir con CAST o DATE_FORMAT
    String getFechaInicio();      // se puede convertir con CAST o DATE_FORMAT
    String getFechaFin();         // se puede convertir con CAST o DATE_FORMAT
    Integer getOrden();
    String getEstado();
    String getNombreUsuario();    // CONCAT(nombre, ' ', apellido)
    Long getSedeId();             // ID de la sede
    String getNombreSede();       // Nombre de la sede
}
