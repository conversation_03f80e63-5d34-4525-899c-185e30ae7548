package com.midas.crm.repository;

import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /* ---------- BÚSQUEDAS BÁSICAS ---------- */
    Optional<User> findByUsername(String username);

    Optional<User> findByEmail(String email);

    Optional<User> findByDni(String dni);

    boolean existsByUsername(String username);

    boolean existsByDni(String dni);

    /* ---------- PAGINADAS / FILTRADAS ---------- */
    Page<User> findAllBySede_Id(Long sedeId, Pageable pageable);

    Page<User> findAllByRole(Role role, Pageable pageable);

    Optional<User> findByIdAndRole(Long id, Role role);

    /* ---------- CONSULTAS OPTIMIZADAS ---------- */
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede LEFT JOIN FETCH u.coordinador WHERE u.id IN :ids")
    List<User> findAllWithSedeAndCoordinadorByIds(@Param("ids") List<Long> ids);

    @Query("SELECT u FROM User u LEFT JOIN FETCH u.sede LEFT JOIN FETCH u.coordinador WHERE u.id IN :ids AND u.sede.id = :sedeId")
    List<User> findAllWithSedeAndCoordinadorByIdsAndSedeId(@Param("ids") List<Long> ids, @Param("sedeId") Long sedeId);

    @Query(value = "SELECT u FROM User u", countQuery = "SELECT COUNT(u) FROM User u")
    Page<User> findAllPaginated(Pageable pageable);

    @Query(value = "SELECT u FROM User u WHERE u.sede.id = :sedeId", countQuery = "SELECT COUNT(u) FROM User u WHERE u.sede.id = :sedeId")
    Page<User> findAllBySede_IdPaginated(@Param("sedeId") Long sedeId, Pageable pageable);

    /**
     * Consulta optimizada para obtener usuarios ordenados alfabéticamente por
     * nombre y apellido
     */
    @Query(value = "SELECT u FROM User u ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u")
    Page<User> findAllOrderedByNombreApellido(Pageable pageable);

    /**
     * Consulta optimizada para obtener usuarios de una sede específica ordenados
     * alfabéticamente por nombre y apellido
     */
    @Query(value = "SELECT u FROM User u WHERE u.sede.id = :sedeId ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u WHERE u.sede.id = :sedeId")
    Page<User> findAllBySede_IdOrderedByNombreApellido(@Param("sedeId") Long sedeId, Pageable pageable);

    /* ---------- ROL & COORDINADOR ---------- */
    @Modifying
    @Query("UPDATE User u SET u.role = :role WHERE u.username = :username")
    void updateUserRole(@Param("username") String username, @Param("role") Role role);

    @Modifying
    @Query("UPDATE User u SET u.role = :role WHERE u.id = :userId")
    void updateUserRoleById(@Param("userId") Long userId, @Param("role") Role role);

    List<User> findByRole(Role role);

    /**
     * Consulta para obtener usuarios por rol ordenados alfabéticamente por nombre y
     * apellido
     */
    @Query("SELECT u FROM User u WHERE u.role = :role ORDER BY u.nombre ASC, u.apellido ASC")
    List<User> findByRoleOrderByNombreAscApellidoAsc(@Param("role") Role role);

    /**
     * Consulta paginada para obtener usuarios por rol ordenados alfabéticamente por
     * nombre y apellido
     */
    @Query(value = "SELECT u FROM User u WHERE u.role = :role ORDER BY u.nombre ASC, u.apellido ASC", countQuery = "SELECT COUNT(u) FROM User u WHERE u.role = :role")
    Page<User> findAllByRoleOrderedByNombreApellido(@Param("role") Role role, Pageable pageable);

    List<User> findByCoordinadorIdAndRole(Long coordinadorId, Role role);

    List<User> findByCoordinadorId(Long coordinadorId);

    @Query("SELECT CAST(u.role AS string) FROM User u WHERE u.id = :userId")
    String findRoleById(@Param("userId") Long userId);

    @Query("SELECT u FROM User u WHERE u.role = :role AND u.coordinador IS NULL")
    List<User> findByRoleAndCoordinadorIsNull(@Param("role") Role role);

    /**
     * Obtiene usuarios por sede y rol
     */
    @Query("SELECT u FROM User u WHERE u.sede.id = :sedeId AND u.role = :role ORDER BY u.nombre ASC, u.apellido ASC")
    List<User> findBySedeIdAndRole(@Param("sedeId") Long sedeId, @Param("role") Role role);

    /* ---------- BÚSQUEDA FULL-TEXT LIGERA ---------- */
    @Query("""
                SELECT u FROM User u
                WHERE (LOWER(u.username)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.nombre)      LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.apellido)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.email)       LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.telefono)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.dni)         LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.sedeNombre)  LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :q, '%')))
            """)
    Page<User> searchAllFields(@Param("q") String query, Pageable pageable);

    @Query("""
                SELECT u FROM User u
                WHERE (LOWER(u.username)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.nombre)      LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.apellido)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.email)       LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.telefono)    LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.dni)         LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(u.sedeNombre)  LIKE LOWER(CONCAT('%', :q, '%'))
                    OR LOWER(CONCAT(u.nombre, ' ', u.apellido)) LIKE LOWER(CONCAT('%', :q, '%')))
                  AND u.sede.id = :sedeId
            """)
    Page<User> searchAllFieldsBySede(@Param("q") String query,
                                     @Param("sedeId") Long sedeId,
                                     Pageable pageable);
}
