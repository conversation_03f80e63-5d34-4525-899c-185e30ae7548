package com.midas.crm.repository;

import com.midas.crm.entity.Anuncio;
import com.midas.crm.entity.DTO.anuncio.AnuncioListProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AnuncioRepository extends JpaRepository<Anuncio, Long> {
    Page<Anuncio> findAll(Pageable pageable);
    Page<Anuncio> findAllByOrderByFechaPublicacionDescIdDesc(Pageable pageable);

    // Consulta para obtener anuncios activos y vigentes
    Page<Anuncio> findByEstadoAndFechaFinGreaterThanEqualOrFechaFinIsNullOrderByOrdenAsc(String estado, LocalDateTime fechaActual, Pageable pageable);

    // Consulta para obtener anuncios ordenados por el campo orden
    Page<Anuncio> findAllByOrderByOrdenAsc(Pageable pageable);

    // Consulta para obtener anuncios expirados que aún están activos
    List<Anuncio> findByEstadoAndFechaFinLessThan(String estado, LocalDateTime fechaActual);

    /**
     * Consulta optimizada para obtener anuncios recientes activos y vigentes
     * No utiliza caché y siempre consulta directamente la base de datos
     */
    @Query("""
        SELECT a.id AS id, a.titulo AS titulo, a.descripcion AS descripcion,
               a.imagenUrl AS imagenUrl, a.categoria AS categoria,
               CAST(a.fechaPublicacion AS string) AS fechaPublicacion,
               CAST(a.fechaInicio AS string) AS fechaInicio,
               CAST(a.fechaFin AS string) AS fechaFin,
               a.orden AS orden,
               a.estado AS estado,
               CONCAT(u.nombre, ' ', u.apellido) AS nombreUsuario,
               a.sede.id AS sedeId,
               a.sede.nombre AS nombreSede
        FROM Anuncio a
        JOIN a.usuario u
        LEFT JOIN a.sede s
        WHERE a.estado = 'ACTIVO'
        AND (a.fechaFin IS NULL OR a.fechaFin >= CURRENT_TIMESTAMP)
        ORDER BY a.orden ASC, a.fechaPublicacion DESC
    """)
    Page<AnuncioListProjection> listarAnunciosRecientes(Pageable pageable);

    /**
     * Consulta optimizada para obtener anuncios recientes activos y vigentes filtrados por sede
     * No utiliza caché y siempre consulta directamente la base de datos
     */
    @Query("""
        SELECT a.id AS id, a.titulo AS titulo, a.descripcion AS descripcion,
               a.imagenUrl AS imagenUrl, a.categoria AS categoria,
               CAST(a.fechaPublicacion AS string) AS fechaPublicacion,
               CAST(a.fechaInicio AS string) AS fechaInicio,
               CAST(a.fechaFin AS string) AS fechaFin,
               a.orden AS orden,
               a.estado AS estado,
               CONCAT(u.nombre, ' ', u.apellido) AS nombreUsuario,
               a.sede.id AS sedeId,
               a.sede.nombre AS nombreSede
        FROM Anuncio a
        JOIN a.usuario u
        LEFT JOIN a.sede s
        WHERE a.estado = 'ACTIVO'
        AND (a.fechaFin IS NULL OR a.fechaFin >= CURRENT_TIMESTAMP)
        AND (a.sede.id = :sedeId OR a.sede IS NULL)
        ORDER BY a.orden ASC, a.fechaPublicacion DESC
    """)
    Page<AnuncioListProjection> listarAnunciosRecientesPorSede(Long sedeId, Pageable pageable);

    /**
     * Consulta optimizada para obtener todos los anuncios sin filtrar
     * No utiliza caché y siempre consulta directamente la base de datos
     */
    @Query("""
        SELECT a.id AS id, a.titulo AS titulo, a.descripcion AS descripcion,
               a.imagenUrl AS imagenUrl, a.categoria AS categoria,
               CAST(a.fechaPublicacion AS string) AS fechaPublicacion,
               CAST(a.fechaInicio AS string) AS fechaInicio,
               CAST(a.fechaFin AS string) AS fechaFin,
               a.orden AS orden,
               a.estado AS estado,
               CONCAT(u.nombre, ' ', u.apellido) AS nombreUsuario,
               a.sede.id AS sedeId,
               a.sede.nombre AS nombreSede
        FROM Anuncio a
        JOIN a.usuario u
        LEFT JOIN a.sede s
        ORDER BY a.orden ASC, a.fechaPublicacion DESC
    """)
    Page<AnuncioListProjection> listarTodosLosAnuncios(Pageable pageable);

    /**
     * Consulta optimizada para obtener todos los anuncios filtrados por sede
     * No utiliza caché y siempre consulta directamente la base de datos
     */
    @Query("""
        SELECT a.id AS id, a.titulo AS titulo, a.descripcion AS descripcion,
               a.imagenUrl AS imagenUrl, a.categoria AS categoria,
               CAST(a.fechaPublicacion AS string) AS fechaPublicacion,
               CAST(a.fechaInicio AS string) AS fechaInicio,
               CAST(a.fechaFin AS string) AS fechaFin,
               a.orden AS orden,
               a.estado AS estado,
               CONCAT(u.nombre, ' ', u.apellido) AS nombreUsuario,
               a.sede.id AS sedeId,
               a.sede.nombre AS nombreSede
        FROM Anuncio a
        JOIN a.usuario u
        LEFT JOIN a.sede s
        WHERE (a.sede.id = :sedeId OR a.sede IS NULL)
        ORDER BY a.orden ASC, a.fechaPublicacion DESC
    """)
    Page<AnuncioListProjection> listarTodosLosAnunciosPorSede(Long sedeId, Pageable pageable);
}
