package com.midas.crm.controller;

import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.EstadisticaSedePaginadaResponse;
import com.midas.crm.service.EstadisticasSedeService;
import com.midas.crm.utils.GenericResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Controlador para manejar las estadísticas por sede
 */
@RestController
@RequestMapping("/api/estadisticas-sede")
@CrossOrigin(origins = "*")
public class EstadisticasSedeController {

    @Autowired
    private EstadisticasSedeService estadisticasSedeService;

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor
     *
     * @param sedeId ID de la sede (opcional)
     * @param fecha  Fecha para filtrar (opcional, por defecto hoy)
     * @return Lista de estadísticas
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<EstadisticaSedeDTO>>> obtenerEstadisticasPorSede(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {

        try {
            // Si no se proporciona fecha, usar la fecha actual
            if (fecha == null) {
                fecha = LocalDate.now();
            }

            List<EstadisticaSedeDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasPorSede(sedeId, fecha);

            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas resumidas por sede
     *
     * @param fecha Fecha para filtrar
     * @return Estadísticas resumidas
     */
    @GetMapping("/resumen")
    public ResponseEntity<GenericResponse<List<EstadisticaSedeDTO>>> obtenerResumenPorSede(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {

        try {
            if (fecha == null) {
                fecha = LocalDate.now();
            }

            List<EstadisticaSedeDTO> resumen = estadisticasSedeService.obtenerResumenPorSede(fecha);

            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Resumen obtenido correctamente");
            response.setData(resumen);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener resumen: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas por rango de fechas
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @return Estadísticas por rango
     */
    @GetMapping("/rango")
    public ResponseEntity<GenericResponse<List<EstadisticaSedeDTO>>> obtenerEstadisticasPorRango(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId) {

        try {
            List<EstadisticaSedeDTO> estadisticas = estadisticasSedeService.obtenerEstadisticasPorRango(fechaInicio,
                    fechaFin, sedeId);

            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas por rango obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<EstadisticaSedeDTO>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas por rango: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    // ===== ENDPOINTS PAGINADOS =====

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor con paginación
     *
     * @param sedeId ID de la sede (opcional)
     * @param fecha  Fecha para filtrar (opcional, por defecto hoy)
     * @param page   Número de página (por defecto 0)
     * @param size   Tamaño de página (por defecto 10)
     * @param sort   Campo de ordenamiento (por defecto 'sede')
     * @return Estadísticas paginadas
     */
    @GetMapping("/paginado")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorSedePaginado(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Si no se proporciona fecha, usar la fecha actual
            if (fecha == null) {
                fecha = LocalDate.now();
            }

            // Crear objeto Pageable sin ordenamiento específico
            Pageable pageable = PageRequest.of(page, size);

            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorSedePaginado(sedeId, supervisorId, fecha, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas paginadas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas paginadas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas agrupadas por sede, supervisor y vendedor para una fecha
     * específica con paginación y filtro de búsqueda por nombre de vendedor
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fecha            Fecha para filtrar (opcional, por defecto hoy)
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param page             Número de página (por defecto 0)
     * @param size             Tamaño de página (por defecto 10)
     * @return Estadísticas paginadas con filtro de búsqueda
     */
    @GetMapping("/paginado/busqueda")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorSedePaginadoConBusqueda(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) String busquedaVendedor,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            System.out.println("=== CONTROLLER: obtenerEstadisticasPorSedePaginadoConBusqueda ===");
            System.out.println("SedeId recibido: " + sedeId);
            System.out.println("SupervisorId recibido: " + supervisorId);
            System.out.println("Fecha recibida: " + fecha);
            System.out.println("BusquedaVendedor recibida: " + busquedaVendedor);
            System.out.println("Page: " + page + ", Size: " + size);

            // Si no se proporciona fecha, usar la fecha actual
            if (fecha == null) {
                fecha = LocalDate.now();
                System.out.println("Fecha establecida por defecto: " + fecha);
            }

            // Crear objeto Pageable sin ordenamiento específico
            Pageable pageable = PageRequest.of(page, size);

            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorSedePaginadoConBusqueda(sedeId, supervisorId, fecha, busquedaVendedor,
                            pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas paginadas con búsqueda obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println("Error en obtenerEstadisticasPorSedePaginadoConBusqueda: " + e.getMessage());
            e.printStackTrace();

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas paginadas con búsqueda: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación
     *
     * @param fechaInicio  Fecha de inicio del rango
     * @param fechaFin     Fecha de fin del rango
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param page         Número de página (por defecto 0)
     * @param size         Tamaño de página (por defecto 10)
     * @return Estadísticas acumuladas paginadas por rango
     */
    @GetMapping("/rango-fechas/paginado")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorRangoFechasPaginado(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            System.out.println("=== OBTENER ESTADÍSTICAS POR RANGO DE FECHAS ===");
            System.out.println("Fecha Inicio: " + fechaInicio);
            System.out.println("Fecha Fin: " + fechaFin);
            System.out.println("SedeId: " + sedeId);
            System.out.println("SupervisorId: " + supervisorId);

            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener estadísticas acumuladas por rango de fechas
            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorRangoFechas(sedeId, supervisorId, fechaInicio, fechaFin, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas acumuladas por rango de fechas obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas por rango de fechas con paginación
     *
     * @param fechaInicio Fecha de inicio
     * @param fechaFin    Fecha de fin
     * @param sedeId      ID de la sede (opcional)
     * @param page        Número de página (por defecto 0)
     * @param size        Tamaño de página (por defecto 10)
     * @param sort        Campo de ordenamiento (por defecto 'sede')
     * @return Estadísticas paginadas por rango
     */
    @GetMapping("/rango/paginado")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorRangoPaginado(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Crear objeto Pageable sin ordenamiento específico
            Pageable pageable = PageRequest.of(page, size);

            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorRangoPaginado(fechaInicio, fechaFin, sedeId, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas paginadas por rango obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas paginadas por rango: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene estadísticas acumuladas por rango de fechas con paginación y búsqueda
     * por vendedor
     *
     * @param fechaInicio      Fecha de inicio del rango
     * @param fechaFin         Fecha de fin del rango
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param busquedaVendedor Término de búsqueda para el nombre del vendedor
     *                         (opcional)
     * @param page             Número de página (por defecto 0)
     * @param size             Tamaño de página (por defecto 10)
     * @return Estadísticas acumuladas paginadas con búsqueda
     */
    @GetMapping("/rango-fechas/paginado/busqueda")
    public ResponseEntity<GenericResponse<EstadisticaSedePaginadaResponse>> obtenerEstadisticasPorRangoFechasPaginadoConBusqueda(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) String busquedaVendedor,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener estadísticas acumuladas por rango de fechas con búsqueda
            EstadisticaSedePaginadaResponse estadisticas = estadisticasSedeService
                    .obtenerEstadisticasPorRangoFechasConBusqueda(sedeId, supervisorId, fechaInicio, fechaFin,
                            busquedaVendedor, pageable);

            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Estadísticas acumuladas por rango de fechas con búsqueda obtenidas correctamente");
            response.setData(estadisticas);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<EstadisticaSedePaginadaResponse> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener estadísticas por rango de fechas con búsqueda: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene leads específicos de un asesor para una fecha determinada
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fecha        Fecha para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param page         Número de página (por defecto 0)
     * @param size         Tamaño de página (por defecto 10)
     * @return Leads del asesor paginados
     */
    @GetMapping("/leads-asesor")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsPorAsesorYFecha(
            @RequestParam String nombreAsesor,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Validar parámetros de entrada
            if (nombreAsesor == null || nombreAsesor.trim().isEmpty()) {
                GenericResponse<Map<String, Object>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("El nombre del asesor es requerido");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener leads del asesor
            Map<String, Object> leadsData = estadisticasSedeService.obtenerLeadsPorAsesorYFecha(
                    nombreAsesor.trim(), fecha, numeroMovil, pageable);

            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Leads del asesor obtenidos correctamente");
            response.setData(leadsData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener leads del asesor: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene leads específicos de un asesor para un rango de fechas
     *
     * @param nombreAsesor Nombre completo del asesor
     * @param fechaInicio  Fecha de inicio para filtrar
     * @param fechaFin     Fecha de fin para filtrar
     * @param numeroMovil  Número móvil para filtrar (opcional)
     * @param page         Número de página (por defecto 0)
     * @param size         Tamaño de página (por defecto 10)
     * @return Leads del asesor paginados
     */
    @GetMapping("/leads-asesor-rango")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerLeadsPorAsesorYRangoFechas(
            @RequestParam String nombreAsesor,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        try {
            // Validar parámetros de entrada
            if (nombreAsesor == null || nombreAsesor.trim().isEmpty()) {
                GenericResponse<Map<String, Object>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("El nombre del asesor es requerido");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<Map<String, Object>> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Crear objeto Pageable
            Pageable pageable = PageRequest.of(page, size);

            // Obtener leads del asesor por rango de fechas
            Map<String, Object> leadsData = estadisticasSedeService.obtenerLeadsPorAsesorYRangoFechas(
                    nombreAsesor.trim(), fechaInicio, fechaFin, numeroMovil, pageable);

            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Leads del asesor por rango de fechas obtenidos correctamente");
            response.setData(leadsData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<Map<String, Object>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener leads del asesor por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Obtiene supervisores/coordinadores por sede
     *
     * @param sedeId ID de la sede
     * @return Lista de supervisores de la sede
     */
    @GetMapping("/supervisores-por-sede")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> obtenerSupervisoresPorSede(
            @RequestParam Long sedeId) {

        try {
            List<Map<String, Object>> supervisores = estadisticasSedeService.obtenerSupervisoresPorSede(sedeId);

            GenericResponse<List<Map<String, Object>>> response = new GenericResponse<>();
            response.setRpta(1);
            response.setMsg("Supervisores obtenidos correctamente");
            response.setData(supervisores);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            GenericResponse<List<Map<String, Object>>> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al obtener supervisores: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y fecha
     * específica
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fecha        Fecha para filtrar (formato yyyy-MM-dd)
     * @return Archivo Excel con los leads filtrados
     */
    @GetMapping("/exportar-leads-por-fecha")
    public ResponseEntity<?> exportarLeadsPorRango(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha) {

        try {
            System.out.println("=== EXPORTAR LEADS POR RANGO ===");
            System.out.println("SedeId: " + sedeId);
            System.out.println("SupervisorId: " + supervisorId);
            System.out.println("Fecha: " + fecha);

            // Generar el Excel con los leads filtrados
            byte[] excelData = estadisticasSedeService.exportarLeadsPorRango(sedeId, supervisorId, fecha);

            if (excelData == null || excelData.length == 0) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("No se encontraron leads para los filtros especificados");
                response.setData(null);
                return ResponseEntity.ok(response);
            }

            // Crear nombre del archivo basado en los filtros
            String nombreArchivo = "leads_estadisticas_" + fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
            if (sedeId != null) {
                nombreArchivo += "_sede_" + sedeId;
            }
            if (supervisorId != null) {
                nombreArchivo += "_supervisor_" + supervisorId;
            }
            nombreArchivo += ".xlsx";

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            System.err.println("Error al exportar leads por rango: " + e.getMessage());
            e.printStackTrace();

            GenericResponse<String> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al generar el archivo Excel: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Exporta a Excel todos los leads filtrados por sede, supervisor y rango de
     * fechas
     *
     * @param sedeId       ID de la sede (opcional)
     * @param supervisorId ID del supervisor (opcional)
     * @param fechaInicio  Fecha de inicio del rango (formato yyyy-MM-dd)
     * @param fechaFin     Fecha de fin del rango (formato yyyy-MM-dd)
     * @return Archivo Excel con los leads filtrados
     */
    @GetMapping("/exportar-leads-por-rango-fechas")
    public ResponseEntity<?> exportarLeadsPorRangoFechas(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin) {

        try {
            System.out.println("=== EXPORTAR LEADS POR RANGO DE FECHAS ===");
            System.out.println("SedeId: " + sedeId);
            System.out.println("SupervisorId: " + supervisorId);
            System.out.println("Fecha Inicio: " + fechaInicio);
            System.out.println("Fecha Fin: " + fechaFin);

            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Generar el Excel con los leads filtrados por rango de fechas
            byte[] excelData = estadisticasSedeService.exportarLeadsPorRangoFechas(sedeId, supervisorId, fechaInicio,
                    fechaFin);

            if (excelData == null || excelData.length == 0) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("No se encontraron leads para el rango de fechas especificado");
                response.setData(null);
                return ResponseEntity.ok(response);
            }

            // Crear nombre del archivo basado en los filtros
            String nombreArchivo = "leads_estadisticas_" + fechaInicio.format(DateTimeFormatter.ISO_LOCAL_DATE)
                    + "_a_" + fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE);
            if (sedeId != null) {
                nombreArchivo += "_sede_" + sedeId;
            }
            if (supervisorId != null) {
                nombreArchivo += "_supervisor_" + supervisorId;
            }
            nombreArchivo += ".xlsx";

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            System.err.println("Error al exportar leads por rango de fechas: " + e.getMessage());
            e.printStackTrace();

            GenericResponse<String> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al generar el archivo Excel por rango de fechas: " + e.getMessage());
            response.setData(null);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Exporta a Excel todos los leads filtrados incluyendo búsqueda por vendedor
     * Este endpoint respeta TODOS los filtros aplicados en el frontend
     *
     * @param sedeId           ID de la sede (opcional)
     * @param supervisorId     ID del supervisor (opcional)
     * @param fecha            Fecha específica (opcional, para exportar por fecha)
     * @param fechaInicio      Fecha de inicio del rango (opcional, para exportar
     *                         por rango)
     * @param fechaFin         Fecha de fin del rango (opcional, para exportar por
     *                         rango)
     * @param busquedaVendedor Término de búsqueda para filtrar por vendedor
     *                         (opcional)
     * @return Archivo Excel con los leads filtrados
     */
    @GetMapping("/exportar-leads-filtrados")
    public ResponseEntity<?> exportarLeadsFiltrados(
            @RequestParam(required = false) Long sedeId,
            @RequestParam(required = false) Long supervisorId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fecha,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaInicio,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate fechaFin,
            @RequestParam(required = false) String busquedaVendedor) {

        try {
            System.out.println("=== EXPORTAR LEADS FILTRADOS ===");
            System.out.println("SedeId: " + sedeId);
            System.out.println("SupervisorId: " + supervisorId);
            System.out.println("Fecha: " + fecha);
            System.out.println("Fecha Inicio: " + fechaInicio);
            System.out.println("Fecha Fin: " + fechaFin);
            System.out.println("Búsqueda Vendedor: " + busquedaVendedor);

            // Validar que se proporcione al menos una fecha
            if (fecha == null && (fechaInicio == null || fechaFin == null)) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("Debe proporcionar una fecha específica o un rango de fechas");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Validar rango de fechas si se proporciona
            if (fechaInicio != null && fechaFin != null && fechaInicio.isAfter(fechaFin)) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("La fecha de inicio no puede ser posterior a la fecha de fin");
                response.setData(null);
                return ResponseEntity.badRequest().body(response);
            }

            // Generar el Excel con todos los filtros aplicados
            byte[] excelData = estadisticasSedeService.exportarLeadsFiltrados(
                    sedeId, supervisorId, fecha, fechaInicio, fechaFin, busquedaVendedor);

            if (excelData == null || excelData.length == 0) {
                GenericResponse<String> response = new GenericResponse<>();
                response.setRpta(0);
                response.setMsg("No se encontraron leads para los filtros especificados");
                response.setData(null);
                return ResponseEntity.ok(response);
            }

            // Crear nombre del archivo basado en los filtros
            String nombreArchivo = "leads_filtrados_";
            if (fecha != null) {
                nombreArchivo += fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
            } else {
                nombreArchivo += fechaInicio.format(DateTimeFormatter.ISO_LOCAL_DATE)
                        + "_a_" + fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE);
            }
            if (sedeId != null) {
                nombreArchivo += "_sede_" + sedeId;
            }
            if (supervisorId != null) {
                nombreArchivo += "_supervisor_" + supervisorId;
            }
            if (busquedaVendedor != null && !busquedaVendedor.trim().isEmpty()) {
                nombreArchivo += "_vendedor_" + busquedaVendedor.replaceAll("[^a-zA-Z0-9]", "_");
            }
            nombreArchivo += ".xlsx";

            // Configurar headers para descarga
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);

        } catch (Exception e) {
            System.err.println("Error al exportar leads filtrados: " + e.getMessage());
            e.printStackTrace();

            GenericResponse<String> response = new GenericResponse<>();
            response.setRpta(0);
            response.setMsg("Error al generar el archivo Excel filtrado: " + e.getMessage());
            response.setData(null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
