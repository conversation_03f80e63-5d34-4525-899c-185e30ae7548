<div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden max-h-[85vh] overflow-y-auto">
  <!-- Header -->
  <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">{{ isEditMode ? 'Editar' : 'Crear' }} Cuestionario</h2>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex flex-col items-center justify-center p-8 space-y-4">
    <mat-spinner diameter="40" class="!text-blue-500"></mat-spinner>
    <p class="text-gray-600 dark:text-gray-300">{{ isEditMode ? 'Cargando cuestionario...' : 'Creando cuestionario...' }}</p>
  </div>

  <!-- Form Content -->
  <form [formGroup]="form" (ngSubmit)="onSubmit()" *ngIf="!loading" class="px-6 py-4">
    <mat-dialog-content>
      <!-- Información General Section -->
      <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-200 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
          Información General
        </h3>

        <!-- Título -->
        <div class="mb-4">
          <mat-form-field appearance="outline" class="w-full">
            <mat-label>Título</mat-label>
            <input matInput formControlName="titulo" placeholder="Título del cuestionario"
                  class="dark:bg-gray-700 dark:text-white">
            <mat-error *ngIf="form.get('titulo')?.hasError('required')">
              El título es obligatorio
            </mat-error>
            <mat-error *ngIf="form.get('titulo')?.hasError('maxlength')">
              El título no puede tener más de 100 caracteres
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Descripción -->
        <div class="mb-4">
          <mat-form-field appearance="outline" class="w-full">
            <mat-label>Descripción</mat-label>
            <textarea matInput formControlName="descripcion" placeholder="Descripción del cuestionario" rows="3"
                     class="dark:bg-gray-700 dark:text-white"></textarea>
            <mat-error *ngIf="form.get('descripcion')?.hasError('maxlength')">
              La descripción no puede tener más de 500 caracteres
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Configuración en grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <!-- Tiempo límite -->
          <mat-form-field appearance="outline">
            <mat-label>Tiempo límite (min)</mat-label>
            <input matInput type="number" formControlName="tiempoLimite" min="1"
                  class="dark:bg-gray-700 dark:text-white">
            <mat-error *ngIf="form.get('tiempoLimite')?.hasError('required')">
              El tiempo límite es obligatorio
            </mat-error>
            <mat-error *ngIf="form.get('tiempoLimite')?.hasError('min')">
              Mínimo 1 minuto
            </mat-error>
          </mat-form-field>

          <!-- Puntaje de aprobación -->
          <mat-form-field appearance="outline">
            <mat-label>Puntaje aprobación (%)</mat-label>
            <input matInput type="number" formControlName="puntajeAprobacion" min="1" max="100"
                  class="dark:bg-gray-700 dark:text-white">
            <mat-error *ngIf="form.get('puntajeAprobacion')?.hasError('required')">
              El puntaje es obligatorio
            </mat-error>
            <mat-error *ngIf="form.get('puntajeAprobacion')?.hasError('min') || form.get('puntajeAprobacion')?.hasError('max')">
              Entre 1 y 100
            </mat-error>
          </mat-form-field>

          <!-- Intentos máximos -->
          <mat-form-field appearance="outline">
            <mat-label>Intentos máximos</mat-label>
            <input matInput type="number" formControlName="intentosMaximos" min="1"
                  class="dark:bg-gray-700 dark:text-white">
            <mat-error *ngIf="form.get('intentosMaximos')?.hasError('required')">
              Los intentos son obligatorios
            </mat-error>
            <mat-error *ngIf="form.get('intentosMaximos')?.hasError('min')">
              Mínimo 1 intento
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Checkboxes -->
        <div class="flex flex-wrap gap-6 mb-2">
          <mat-checkbox formControlName="mostrarRespuestas" class="text-gray-700 dark:text-gray-200">
            Mostrar respuestas correctas al finalizar
          </mat-checkbox>

          <mat-checkbox formControlName="aleatorizarPreguntas" class="text-gray-700 dark:text-gray-200">
            Aleatorizar orden de preguntas
          </mat-checkbox>
        </div>
      </div>

      <!-- Preguntas Section -->
      <div class="mb-6">
        <div class="flex justify-between items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-200">Preguntas</h3>
          <button type="button" mat-mini-fab color="primary" (click)="addPregunta()"
                 class="!bg-blue-600 hover:!bg-blue-700 !w-10 !h-10 !min-h-0" aria-label="Añadir pregunta">
            <mat-icon class="!text-white">add</mat-icon>
          </button>
        </div>

        <!-- Lista de preguntas -->
        <div formArrayName="preguntas" class="space-y-6">
          <div *ngFor="let preguntaGroup of preguntasForm.controls; let i = index"
               class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm"
               [formGroupName]="i">

            <!-- Cabecera de pregunta -->
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-base font-medium text-gray-700 dark:text-gray-200">Pregunta {{ i + 1 }}</h4>
              <button type="button" mat-icon-button color="warn" (click)="removePregunta(i)"
                     class="!text-red-500 hover:!bg-red-50 dark:hover:!bg-red-900/20" aria-label="Eliminar pregunta">
                <mat-icon>delete</mat-icon>
              </button>
            </div>

            <!-- Enunciado -->
            <div class="mb-4">
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Enunciado</mat-label>
                <textarea matInput formControlName="enunciado" placeholder="Enunciado de la pregunta" rows="2"
                         class="dark:bg-gray-700 dark:text-white"></textarea>
                <mat-error *ngIf="preguntaGroup.get('enunciado')?.hasError('required')">
                  El enunciado es obligatorio
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Configuración de pregunta -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <!-- Tipo de pregunta -->
              <mat-form-field appearance="outline">
                <mat-label>Tipo de pregunta</mat-label>
                <mat-select formControlName="tipo" class="dark:bg-gray-700 dark:text-white">
                  <mat-option [value]="TipoPregunta.OPCION_MULTIPLE">Opción múltiple (una respuesta)</mat-option>
                  <mat-option [value]="TipoPregunta.SELECCION_MULTIPLE">Selección múltiple (varias respuestas)</mat-option>
                  <mat-option [value]="TipoPregunta.VERDADERO_FALSO">Verdadero / Falso</mat-option>
                  <mat-option [value]="TipoPregunta.TEXTO_LIBRE">Texto libre</mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Puntaje -->
              <mat-form-field appearance="outline">
                <mat-label>Puntaje</mat-label>
                <input matInput type="number" formControlName="puntaje" min="1"
                      class="dark:bg-gray-700 dark:text-white">
                <mat-error *ngIf="preguntaGroup.get('puntaje')?.hasError('required')">
                  El puntaje es obligatorio
                </mat-error>
                <mat-error *ngIf="preguntaGroup.get('puntaje')?.hasError('min')">
                  Mínimo 1 punto
                </mat-error>
              </mat-form-field>

              <!-- Orden -->
              <mat-form-field appearance="outline">
                <mat-label>Orden</mat-label>
                <input matInput type="number" formControlName="orden" min="1"
                      class="dark:bg-gray-700 dark:text-white">
              </mat-form-field>
            </div>

            <!-- Explicación -->
            <div class="mb-4">
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Explicación (opcional)</mat-label>
                <textarea matInput formControlName="explicacion"
                         placeholder="Explicación que se mostrará después de responder" rows="2"
                         class="dark:bg-gray-700 dark:text-white"></textarea>
              </mat-form-field>
            </div>

            <!-- Respuestas (excepto para texto libre) -->
            <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600"
                *ngIf="preguntaGroup.get('tipo')?.value !== TipoPregunta.TEXTO_LIBRE">

              <div class="flex justify-between items-center mb-3">
                <h5 class="text-sm font-medium text-gray-600 dark:text-gray-300">Respuestas</h5>
                <button type="button" mat-mini-fab color="accent" (click)="addRespuesta(i)"
                       class="!bg-indigo-500 hover:!bg-indigo-600 !w-8 !h-8 !min-h-0" aria-label="Añadir respuesta">
                  <mat-icon class="!text-white !text-base">add</mat-icon>
                </button>
              </div>

              <!-- Lista de respuestas -->
              <div formArrayName="respuestas" class="space-y-3">
                <div *ngFor="let respuestaGroup of getRespuestas(i).controls; let j = index"
                     class="flex items-center gap-2 bg-white dark:bg-gray-800 p-2 rounded border border-gray-100 dark:border-gray-700"
                     [formGroupName]="j">

                  <!-- Texto de respuesta -->
                  <mat-form-field appearance="outline" class="flex-1 !m-0">
                    <mat-label>Texto de la respuesta</mat-label>
                    <input matInput formControlName="texto" placeholder="Texto de la respuesta"
                          class="dark:bg-gray-700 dark:text-white">
                    <mat-error *ngIf="respuestaGroup.get('texto')?.hasError('required')">
                      El texto es obligatorio
                    </mat-error>
                  </mat-form-field>

                  <!-- Checkbox de respuesta correcta -->
                  <mat-checkbox formControlName="esCorrecta" class="mx-2 text-gray-700 dark:text-gray-200">
                    Correcta
                  </mat-checkbox>

                  <!-- Botón eliminar respuesta -->
                  <button type="button" mat-icon-button color="warn" (click)="removeRespuesta(i, j)"
                         class="!text-red-500 hover:!bg-red-50 dark:hover:!bg-red-900/20" aria-label="Eliminar respuesta">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>

              <!-- Advertencia de respuestas mínimas -->
              <div *ngIf="getRespuestas(i).length < 2"
                   class="flex items-center mt-2 text-sm text-amber-600 dark:text-amber-400">
                <mat-icon class="!text-amber-500 !text-base mr-1">warning</mat-icon>
                <span>Debe añadir al menos 2 respuestas</span>
              </div>
            </div>

            <!-- Información para preguntas de texto libre -->
            <div *ngIf="preguntaGroup.get('tipo')?.value === TipoPregunta.TEXTO_LIBRE"
                 class="flex items-start p-3 mt-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-md">
              <mat-icon class="!text-blue-500 mr-2 !text-base">info</mat-icon>
              <p class="text-sm">Las preguntas de texto libre no tienen respuestas predefinidas. El usuario podrá ingresar cualquier texto como respuesta.</p>
            </div>
          </div>
        </div>

        <!-- Estado sin preguntas -->
        <div *ngIf="preguntasForm.length === 0"
             class="flex flex-col items-center justify-center p-8 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <mat-icon class="!text-gray-400 dark:!text-gray-500 !text-4xl mb-2">help</mat-icon>
          <p class="text-gray-500 dark:text-gray-400">No hay preguntas. Haga clic en el botón "+" para añadir una pregunta.</p>
        </div>
      </div>

      <!-- Mensaje de error -->
      <div *ngIf="error"
           class="flex items-center p-3 mb-4 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md">
        <mat-icon class="!text-red-500 mr-2">error</mat-icon>
        <span>{{ error }}</span>
      </div>
    </mat-dialog-content>

    <!-- Acciones del formulario -->
    <mat-dialog-actions align="end" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
      <button type="button" mat-button (click)="onCancel()"
             class="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 px-4 py-2 rounded">
        Cancelar
      </button>
      <button type="submit" mat-raised-button color="primary" [disabled]="form.invalid || loading"
             class="!bg-blue-600 hover:!bg-blue-700 !text-white px-4 py-2 rounded ml-2">
        {{ isEditMode ? 'Actualizar' : 'Guardar' }}
      </button>
    </mat-dialog-actions>
  </form>
</div>
