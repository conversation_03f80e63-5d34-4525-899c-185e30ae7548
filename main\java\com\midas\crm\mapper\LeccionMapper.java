package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.leccion.LeccionCreateDTO;
import com.midas.crm.entity.DTO.leccion.LeccionDTO;
import com.midas.crm.entity.DTO.leccion.LeccionUpdateDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.Seccion;

public final class LeccionMapper {

    private LeccionMapper() {}

    public static Leccion toEntity(LeccionCreateDTO dto, Seccion seccion) {
        Leccion leccion = new Leccion();
        leccion.setTitulo(dto.getTitulo());
        leccion.setDescripcion(dto.getDescripcion());
        leccion.setOrden(dto.getOrden());
        leccion.setTipoLeccion(dto.getTipoLeccion());
        leccion.setVideoUrl(dto.getVideoUrl());
        leccion.setSubtitlesUrl(dto.getSubtitlesUrl());
        leccion.setDuracion(dto.getDuracion());
        leccion.setThumbnailUrl(dto.getThumbnailUrl());
        leccion.setPdfUrl(dto.getPdfUrl());
        leccion.setSeccion(seccion);
        leccion.setEstado("A");
        return leccion;
    }

    public static LeccionDTO toDTO(Leccion leccion) {
        if (leccion == null) return null;

        return new LeccionDTO(
                leccion.getId(),
                leccion.getTitulo(),
                leccion.getDescripcion(),
                leccion.getOrden(),
                leccion.getTipoLeccion(),
                leccion.getVideoUrl(),
                leccion.getSubtitlesUrl(),
                leccion.getDuracion(),
                leccion.getThumbnailUrl(),
                leccion.getPdfUrl(),
                leccion.getSeccion() != null ? leccion.getSeccion().getId() : null,
                leccion.getSeccion() != null ? leccion.getSeccion().getTitulo() : null,
                leccion.getEstado(),
                leccion.getFechaCreacion()
        );
    }

    public static void updateEntityFromDTO(Leccion leccion, LeccionUpdateDTO dto) {
        if (dto.getTitulo() != null) {
            leccion.setTitulo(dto.getTitulo());
        }
        if (dto.getDescripcion() != null) {
            leccion.setDescripcion(dto.getDescripcion());
        }
        if (dto.getOrden() != null) {
            leccion.setOrden(dto.getOrden());
        }
        if (dto.getTipoLeccion() != null) {
            leccion.setTipoLeccion(dto.getTipoLeccion());
        }
        if (dto.getVideoUrl() != null) {
            leccion.setVideoUrl(dto.getVideoUrl());
        }
        if (dto.getSubtitlesUrl() != null) {
            leccion.setSubtitlesUrl(dto.getSubtitlesUrl());
        }
        if (dto.getDuracion() != null) {
            leccion.setDuracion(dto.getDuracion());
        }
        if (dto.getThumbnailUrl() != null) {
            leccion.setThumbnailUrl(dto.getThumbnailUrl());
        }
        if (dto.getPdfUrl() != null) {
            leccion.setPdfUrl(dto.getPdfUrl());
        }
        if (dto.getEstado() != null) {
            leccion.setEstado(dto.getEstado());
        }
    }
}
