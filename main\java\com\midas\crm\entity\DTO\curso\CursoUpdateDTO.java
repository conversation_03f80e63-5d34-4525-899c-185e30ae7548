package com.midas.crm.entity.DTO.curso;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CursoUpdateDTO {
    private String nombre;
    private String descripcion;
    private String videoUrl;
    private LocalDateTime fechaInicio;
    private LocalDateTime fechaFin;
    private String estado;
    private Boolean completado;
}
