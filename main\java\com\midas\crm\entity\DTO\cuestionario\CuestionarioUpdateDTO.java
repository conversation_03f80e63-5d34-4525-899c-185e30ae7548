package com.midas.crm.entity.DTO.cuestionario;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CuestionarioUpdateDTO {
    private String titulo;
    private String descripcion;
    
    @Min(value = 1, message = "El tiempo límite debe ser al menos 1 minuto")
    @Max(value = 180, message = "El tiempo límite no puede exceder 180 minutos")
    private Integer tiempoLimite;
    
    @Min(value = 0, message = "El puntaje de aprobación debe ser al menos 0")
    @Max(value = 100, message = "El puntaje de aprobación no puede exceder 100")
    private Integer puntajeAprobacion;
    
    @Min(value = 1, message = "Los intentos máximos deben ser al menos 1")
    private Integer intentosMaximos;
    
    private Boolean mostrarRespuestas;
    private Boolean aleatorizarPreguntas;
    private String estado;
}
