package com.midas.crm.mapper;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.DTO.cuestionario.PreguntaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaDTO;
import com.midas.crm.entity.DTO.cuestionario.PreguntaUpdateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaDTO;
import com.midas.crm.entity.Pregunta;

import java.util.List;
import java.util.stream.Collectors;

public final class PreguntaMapper {

    private PreguntaMapper() {}

    public static Pregunta toEntity(PreguntaCreateDTO dto, Cuestionario cuestionario) {
        Pregunta pregunta = new Pregunta();
        pregunta.setEnunciado(dto.getEnunciado());
        pregunta.setExplicacion(dto.getExplicacion());
        pregunta.setPuntaje(dto.getPuntaje());
        pregunta.setOrden(dto.getOrden());
        pregunta.setTipo(dto.getTipo());
        pregunta.setCuestionario(cuestionario);
        pregunta.setEstado("A");
        return pregunta;
    }

    public static PreguntaDTO toDTO(Pregunta pregunta) {
        if (pregunta == null) return null;

        List<RespuestaDTO> respuestasDTO = null;
        if (pregunta.getRespuestas() != null && !pregunta.getRespuestas().isEmpty()) {
            respuestasDTO = pregunta.getRespuestas().stream()
                    .map(RespuestaMapper::toDTO)
                    .collect(Collectors.toList());
        }

        return new PreguntaDTO(
                pregunta.getId(),
                pregunta.getEnunciado(),
                pregunta.getExplicacion(),
                pregunta.getPuntaje(),
                pregunta.getOrden(),
                pregunta.getTipo(),
                pregunta.getCuestionario() != null ? pregunta.getCuestionario().getId() : null,
                pregunta.getEstado(),
                pregunta.getFechaCreacion(),
                pregunta.getFechaActualizacion(),
                respuestasDTO
        );
    }

    public static void updateEntity(Pregunta pregunta, PreguntaUpdateDTO dto) {
        if (dto.getEnunciado() != null) pregunta.setEnunciado(dto.getEnunciado());
        if (dto.getExplicacion() != null) pregunta.setExplicacion(dto.getExplicacion());
        if (dto.getPuntaje() != null) pregunta.setPuntaje(dto.getPuntaje());
        if (dto.getOrden() != null) pregunta.setOrden(dto.getOrden());
        if (dto.getTipo() != null) pregunta.setTipo(dto.getTipo());
        if (dto.getEstado() != null) pregunta.setEstado(dto.getEstado());
    }
}
