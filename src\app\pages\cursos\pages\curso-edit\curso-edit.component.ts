import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CursoService } from '@app/services/curso.service';
import { Curso, CursoUpdateRequest } from '@app/models/backend/curso/curso.model';

import { FilesUploadComponent } from '@app/shared/popups/files-upload/files-upload.component';
import { Store, select } from '@ngrx/store';
import * as fromRoot from '@app/store';
import * as fromUser from '@app/store/user';
import { User } from '@app/models/backend/user';
import { NotificationService } from '@app/services/notification/notification.service';
import { Leccion } from '@app/models/backend/curso/leccion.model';
import { <PERSON><PERSON><PERSON> } from '@app/models/backend/curso/modulo.model';
import { Seccion } from '@app/models/backend/curso/seccion.model';

@Component({
  selector: 'app-curso-edit',
  templateUrl: './curso-edit.component.html'
})
export class CursoEditComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  loading: boolean = false;
  error: string | null = null;
  videoFile: File | null = null;
  videoPreviewUrl: string | null = null;
  user: User | null = null;
  curso: Curso | null = null;
  cursoId: number = 0;

  // Pestañas para gestionar módulos y lecciones
  activeTab: number = 0;

  // Gestión de módulos, secciones y lecciones
  selectedModulo: Modulo | null = null;
  selectedSeccion: Seccion | null = null;
  showModuloForm: boolean = false;
  showSeccionList: boolean = false;
  showSeccionForm: boolean = false;
  showLeccionList: boolean = false;
  showLeccionForm: boolean = false;
  selectedLeccion: Leccion | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private cursoService: CursoService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private notification: NotificationService,
    private store: Store<fromRoot.State>
  ) { }

  ngOnInit(): void {
    this.initForm();
    // Ya no necesitamos checkDarkTheme, usamos clases de Tailwind para el modo oscuro
    this.getCurrentUser();
    this.getCursoId();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm(): void {
    this.form = this.fb.group({
      nombre: ['', [Validators.required, Validators.maxLength(100)]],
      descripcion: ['', [Validators.maxLength(500)]],
      fechaInicio: [null],
      fechaFin: [null],
      videoUrl: [''],
      estado: ['A']
    });
  }

  // Ya no necesitamos el método checkDarkTheme, usamos clases de Tailwind para el modo oscuro

  private getCurrentUser(): void {
    this.store.pipe(
      select(fromUser.getUser),
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.user = user;
    });
  }

  private getCursoId(): void {
    this.route.params
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        if (params['id']) {
          this.cursoId = +params['id'];
          this.loadCurso();
        } else {
          this.error = 'ID de curso no válido';
          this.notification.error(this.error);
          this.router.navigate(['/cursos']);
        }
      });
  }

  private loadCurso(): void {
    this.loading = true;
    this.error = null;

    this.cursoService.getCursoById(this.cursoId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1 && response.data) {
            this.curso = response.data;
            this.populateForm();
          } else {
            this.error = response.msg || 'Error al cargar el curso';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al cargar el curso. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al cargar curso:', error);
        }
      });
  }

  private populateForm(): void {
    if (!this.curso) return;

    this.form.patchValue({
      nombre: this.curso.nombre,
      descripcion: this.curso.descripcion,
      fechaInicio: this.curso.fechaInicio ? new Date(this.curso.fechaInicio) : null,
      fechaFin: this.curso.fechaFin ? new Date(this.curso.fechaFin) : null,
      videoUrl: this.curso.videoUrl || '',
      estado: this.curso.estado || 'A'
    });
  }

  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    this.loading = true;
    this.error = null;

    const cursoData: CursoUpdateRequest = {
      nombre: this.form.value.nombre,
      descripcion: this.form.value.descripcion,
      videoUrl: this.form.value.videoUrl,
      estado: this.form.value.estado
    };

    // Añadir fechas solo si están presentes
    if (this.form.value.fechaInicio) {
      cursoData.fechaInicio = this.formatDate(this.form.value.fechaInicio);
    }

    if (this.form.value.fechaFin) {
      cursoData.fechaFin = this.formatDate(this.form.value.fechaFin);
    }

    this.cursoService.updateCurso(this.cursoId, cursoData, this.videoFile || undefined)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success('Curso actualizado exitosamente');
            this.router.navigate(['/cursos']);
          } else {
            this.error = response.msg || 'Error al actualizar el curso';
            this.notification.error(this.error);
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = 'Error al actualizar el curso. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error);
          console.error('Error al actualizar curso:', error);
        }
      });
  }

  onCancel(): void {
    this.router.navigate(['/cursos']);
  }

  openFilesUpload(): void {
    const dialogRef = this.dialog.open(FilesUploadComponent, {
      width: '600px',
      height: '400px',
      panelClass: 'files-upload-dialog',
      data: {
        multiple: false,
        crop: false
      }
    });

    dialogRef.afterClosed().subscribe({
      next: (url) => {
        if (url) {
          this.form.patchValue({
            videoUrl: url
          });
        }
      }
    });
  }

  /**
   * Método para seleccionar un archivo de video local (comentado porque ahora solo usamos Firebase)
   */
  /*
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Verificar que sea un archivo de video
      if (!file.type.startsWith('video/')) {
        this.notification.error('Por favor, seleccione un archivo de video válido');
        return;
      }

      this.videoFile = file;

      // Crear una URL para previsualizar el video
      this.videoPreviewUrl = URL.createObjectURL(file);

      // Limpiar la URL del video existente
      this.form.patchValue({
        videoUrl: ''
      });
    }
  }
  */

  removeSelectedVideo(): void {
    this.videoFile = null;
    this.videoPreviewUrl = null;
  }

  changeTab(tabIndex: number): void {
    this.activeTab = tabIndex;
  }

  // Métodos para gestionar módulos
  onEditModulo(modulo: Modulo | null): void {
    this.selectedModulo = modulo;
    this.showModuloForm = true;
  }

  onViewLecciones(modulo: Modulo): void {
    this.selectedModulo = modulo;
    this.showSeccionList = true;
    this.activeTab = 2; // Cambiar a la pestaña de secciones
  }

  onModuloSaved(): void {
    this.showModuloForm = false;
    this.selectedModulo = null;
    this.notification.success('Módulo guardado exitosamente');
  }

  onModuloCancelled(): void {
    this.showModuloForm = false;
    this.selectedModulo = null;
  }

  // Métodos para gestionar secciones
  onSeccionesBack(): void {
    this.showSeccionList = false;
    this.selectedModulo = null;
  }

  onSeccionSelected(seccion: Seccion): void {
    this.selectedSeccion = seccion;
    this.showSeccionList = false;
    this.showLeccionList = true;
  }

  onEditSeccion(seccion: Seccion | null): void {
    this.selectedSeccion = seccion;
    this.showSeccionList = false;
    this.showSeccionForm = true;
  }

  onSeccionSaved(): void {
    this.showSeccionForm = false;
    this.showSeccionList = true;
    this.selectedSeccion = null;
    this.notification.success('Sección guardada exitosamente');
  }

  onSeccionCancelled(): void {
    this.showSeccionForm = false;
    this.showSeccionList = true;
    this.selectedSeccion = null;
  }

  // Métodos para gestionar lecciones
  onLeccionesBack(): void {
    this.showLeccionList = false;
    this.showSeccionList = true;
  }

  onEditLeccion(leccion: Leccion | null): void {
    this.selectedLeccion = leccion;
    this.showLeccionForm = true;
    this.showLeccionList = false;
  }

  onLeccionSaved(): void {
    this.showLeccionForm = false;
    this.showLeccionList = true;
    this.selectedLeccion = null;
    this.notification.success('Lección guardada exitosamente');
  }

  onLeccionCancelled(): void {
    this.showLeccionForm = false;
    this.showLeccionList = true;
    this.selectedLeccion = null;
  }

  private formatDate(date: string | Date): string {
    try {
      // Si date ya es un string (formato YYYY-MM-DD), añadimos la parte de tiempo
      if (typeof date === 'string') {
        // Añadir la parte de tiempo para que sea un LocalDateTime válido
        return `${date}T00:00:00`;
      }

      // Si date es un objeto Date, lo convertimos a ISO string con formato completo
      if (date instanceof Date) {
        // Asegurarnos de que la fecha sea válida
        if (isNaN(date.getTime())) {
          throw new Error('Fecha inválida');
        }

        // Formatear como YYYY-MM-DDT00:00:00 (formato que espera LocalDateTime en el backend)
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}T00:00:00`;
      }

      throw new Error('Tipo de fecha no soportado');
    } catch (error) {
      console.error('Error al formatear fecha:', error, date);
      return '';
    }
  }
}
