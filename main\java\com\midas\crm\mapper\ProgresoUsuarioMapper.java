package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.progreso.ProgresoCreateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUpdateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUsuarioDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.ProgresoUsuario;
import com.midas.crm.entity.User;

public final class ProgresoUsuarioMapper {

    private ProgresoUsuarioMapper() {}

    public static ProgresoUsuario toEntity(ProgresoCreateDTO dto, User usuario, Leccion leccion) {
        ProgresoUsuario progreso = new ProgresoUsuario();
        progreso.setUsuario(usuario);
        progreso.setLeccion(leccion);
        progreso.setCompletado(dto.getCompletado() != null ? dto.getCompletado() : false);
        progreso.setSegundosVistos(dto.getSegundosVistos() != null ? dto.getSegundosVistos() : 0);
        progreso.setPorcentajeCompletado(dto.getPorcentajeCompletado() != null ? dto.getPorcentajeCompletado() : 0);
        progreso.setUltimaPosicion(dto.getUltimaPosicion() != null ? dto.getUltimaPosicion() : 0);
        return progreso;
    }

    public static ProgresoUsuarioDTO toDTO(ProgresoUsuario progreso) {
        if (progreso == null) return null;

        return new ProgresoUsuarioDTO(
                progreso.getId(),
                progreso.getUsuario() != null ? progreso.getUsuario().getId() : null,
                progreso.getUsuario() != null ? progreso.getUsuario().getNombre() + " " + progreso.getUsuario().getApellido() : null,
                progreso.getLeccion() != null ? progreso.getLeccion().getId() : null,
                progreso.getLeccion() != null ? progreso.getLeccion().getTitulo() : null,
                progreso.getCompletado(),
                progreso.getSegundosVistos(),
                progreso.getPorcentajeCompletado(),
                progreso.getUltimaPosicion(),
                progreso.getFechaCreacion(),
                progreso.getUltimaVisualizacion()
        );
    }

    public static void updateEntity(ProgresoUsuario progreso, ProgresoUpdateDTO dto) {
        if (dto.getCompletado() != null) progreso.setCompletado(dto.getCompletado());
        if (dto.getSegundosVistos() != null) progreso.setSegundosVistos(dto.getSegundosVistos());
        if (dto.getPorcentajeCompletado() != null) progreso.setPorcentajeCompletado(dto.getPorcentajeCompletado());
        if (dto.getUltimaPosicion() != null) progreso.setUltimaPosicion(dto.getUltimaPosicion());
    }
}
