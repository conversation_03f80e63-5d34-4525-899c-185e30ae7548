package com.midas.crm.controller;

import com.midas.crm.entity.DTO.video.VideoInfoCreateDTO;
import com.midas.crm.entity.DTO.video.VideoInfoDTO;
import com.midas.crm.service.VideoInfoService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("${api.route.video-info}")
@RequiredArgsConstructor
public class VideoInfoController {

    private final VideoInfoService videoInfoService;

    /**
     * Crea información de video
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<VideoInfoDTO>> createVideoInfo(@Valid @RequestBody VideoInfoCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(videoInfoService::createVideoInfo)
            .map(videoInfo -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Información de video registrada exitosamente", videoInfo)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene información de video por ID de lección
     * Implementado con programación funcional
     */
    @GetMapping("/leccion/{leccionId}")
    public ResponseEntity<GenericResponse<VideoInfoDTO>> getVideoInfoByLeccionId(@PathVariable Long leccionId) {
        return Optional.ofNullable(leccionId)
            .map(videoInfoService::getVideoInfoByLeccionId)
            .map(videoInfo -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Información de video encontrada", videoInfo)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina información de video por ID
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Object>> deleteVideoInfo(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().build();
        }

        videoInfoService.deleteVideoInfo(id);
        return ResponseEntity.ok(
            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Información de video eliminada exitosamente", null)
        );
    }
}
