<div class="p-2">
  <div class="flex justify-between items-center mb-2">
    <h3 class="text-sm font-medium text-indigo-600 dark:text-indigo-400 flex items-center">
      <mat-icon class="mr-1 text-sm">view_module</mat-icon>
      Mó<PERSON>los del Curso
    </h3>
    <button class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="editModulo.emit(null)">
      <mat-icon class="mr-1 text-xs">add</mat-icon> Nuevo
    </button>
  </div>

  <div class="relative min-h-[50px]">
    <app-spinner *ngIf="loading"></app-spinner>

    <div *ngIf="error" class="flex items-center text-red-600 dark:text-red-400 text-xs mb-2">
      <mat-icon class="mr-1 text-xs">error</mat-icon>
      <span>{{ error }}</span>
    </div>

    <div *ngIf="!loading && !error && modulos.length === 0" class="flex flex-col items-center py-3 text-center">
      <mat-icon class="text-indigo-500 dark:text-indigo-400 mb-1">school</mat-icon>
      <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">No hay módulos disponibles</p>
      <button class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-2 py-1 rounded flex items-center" (click)="editModulo.emit(null)">
        <mat-icon class="mr-1 text-xs">add</mat-icon> Crear Módulo
      </button>
    </div>

    <div cdkDropList (cdkDropListDropped)="onDrop($event)" class="space-y-2" *ngIf="modulos.length > 0">
      <div *ngFor="let modulo of modulos" class="bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 shadow-sm relative p-2 cursor-move" cdkDrag>
        <div class="absolute top-1 right-1 text-gray-400 dark:text-gray-500" cdkDragHandle>
          <mat-icon class="text-sm">drag_indicator</mat-icon>
        </div>

        <div class="flex items-center mb-1">
          <h4 class="text-sm font-medium text-gray-800 dark:text-white mr-2">{{ modulo.titulo || modulo.nombre }}</h4>
          <span class="text-xs text-gray-500 dark:text-gray-400">Orden: {{ modulo.orden }}</span>
          <div class="ml-auto text-xs px-1.5 py-0.5 rounded-full"
               [ngClass]="{'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': modulo.estado === 'A',
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': modulo.estado === 'I'}">
            {{ modulo.estado === 'A' ? 'Activo' : 'Inactivo' }}
          </div>
        </div>

        <p class="text-xs text-gray-600 dark:text-gray-400 mb-1 line-clamp-1">{{ modulo.descripcion }}</p>

        <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-2">
          <span *ngIf="modulo.secciones?.length" class="flex items-center mr-3">
            <mat-icon class="mr-0.5 text-xs">folder</mat-icon> {{ modulo.secciones?.length || 0 }} secciones
          </span>
          <span *ngIf="getTotalLecciones(modulo) > 0" class="flex items-center">
            <mat-icon class="mr-0.5 text-xs">video_library</mat-icon> {{ getTotalLecciones(modulo) }} lecciones
          </span>
        </div>

        <div class="flex justify-end space-x-1 border-t border-gray-100 dark:border-gray-700 pt-1">
          <button class="text-xs px-1.5 py-0.5 text-indigo-600 hover:bg-indigo-50 dark:text-indigo-400 dark:hover:bg-indigo-900/20 rounded flex items-center" (click)="onViewLecciones(modulo)">
            <mat-icon class="mr-0.5 text-xs">folder</mat-icon> Secciones
          </button>
          <button class="text-xs px-1.5 py-0.5 text-amber-600 hover:bg-amber-50 dark:text-amber-400 dark:hover:bg-amber-900/20 rounded flex items-center" (click)="onEditModulo(modulo)">
            <mat-icon class="mr-0.5 text-xs">edit</mat-icon> Editar
          </button>
          <button class="text-xs px-1.5 py-0.5 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded flex items-center" (click)="onDeleteModulo(modulo.id)">
            <mat-icon class="mr-0.5 text-xs">delete</mat-icon> Eliminar
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
