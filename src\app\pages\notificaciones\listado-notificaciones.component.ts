import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { NotificacionesWsService } from '@app/services/notificaciones/notificaciones-ws.service';
import { Notificacion, TipoNotificacion } from '@app/models/notificacion.model';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { EnviarNotificacionComponent } from './enviar-notificacion.component';

@Component({
  selector: 'app-listado-notificaciones',
  templateUrl: './listado-notificaciones.component.html',
})
export class ListadoNotificacionesComponent implements OnInit, OnDestroy {
  notificaciones: Notificacion[] = [];
  notificacionesFiltradas: Notificacion[] = [];
  contadorNoLeidas = 0;
  cargando = false;
  filtroActual: string = 'todas';

  private subscripciones: Subscription[] = [];

  constructor(
    private notificacionesService: NotificacionesWsService,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    // Inicializar el servicio de notificaciones
    this.notificacionesService.inicializar();

    // Suscribirse a las notificaciones
    this.subscripciones.push(
      this.notificacionesService
        .getNotificaciones()
        .subscribe((notificaciones) => {
          this.notificaciones = notificaciones;
          this.aplicarFiltro(this.filtroActual);
        })
    );

    // Suscribirse al contador de no leídas
    this.subscripciones.push(
      this.notificacionesService.getContadorNoLeidas().subscribe((contador) => {
        this.contadorNoLeidas = contador;
      })
    );

    // Suscribirse al estado de carga
    this.subscripciones.push(
      this.notificacionesService.getCargando().subscribe((cargando) => {
        this.cargando = cargando;
      })
    );

    // Cargar notificaciones iniciales
    this.cargarNotificaciones();
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones
    this.subscripciones.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Carga las notificaciones desde el servidor
   */
  cargarNotificaciones(): void {
    this.notificacionesService.cargarNotificaciones();
  }

  /**
   * Marca una notificación como leída
   */
  marcarComoLeida(notificacion: Notificacion, event?: MouseEvent): void {
    if (event) {
      event.stopPropagation();
    }

    this.notificacionesService.marcarComoLeida(notificacion.id);
  }

  /**
   * Marca todas las notificaciones como leídas
   */
  marcarTodasComoLeidas(): void {
    this.notificacionesService.marcarTodasComoLeidas();
  }

  /**
   * Maneja el clic en una notificación
   */
  manejarClicNotificacion(notificacion: Notificacion): void {
    // Marcar como leída
    this.marcarComoLeida(notificacion);

    // Navegar al enlace si existe
    if (notificacion.enlace) {
      this.router.navigateByUrl(notificacion.enlace);
    }
  }

  /**
   * Aplica un filtro a las notificaciones
   */
  aplicarFiltro(filtro: string): void {
    this.filtroActual = filtro;

    switch (filtro) {
      case 'no-leidas':
        this.notificacionesFiltradas = this.notificaciones.filter(
          (n) => !n.leida
        );
        break;
      case 'leidas':
        this.notificacionesFiltradas = this.notificaciones.filter(
          (n) => n.leida
        );
        break;
      case 'info':
        this.notificacionesFiltradas = this.notificaciones.filter(
          (n) => n.tipo === TipoNotificacion.INFO
        );
        break;
      case 'exito':
        this.notificacionesFiltradas = this.notificaciones.filter(
          (n) => n.tipo === TipoNotificacion.EXITO
        );
        break;
      case 'advertencia':
        this.notificacionesFiltradas = this.notificaciones.filter(
          (n) => n.tipo === TipoNotificacion.ADVERTENCIA
        );
        break;
      case 'error':
        this.notificacionesFiltradas = this.notificaciones.filter(
          (n) => n.tipo === TipoNotificacion.ERROR
        );
        break;
      case 'sistema':
        this.notificacionesFiltradas = this.notificaciones.filter(
          (n) => n.tipo === TipoNotificacion.SISTEMA
        );
        break;
      default:
        this.notificacionesFiltradas = [...this.notificaciones];
    }
  }

  /**
   * Abre el diálogo para enviar una nueva notificación
   */
  abrirDialogoEnviarNotificacion(): void {
    const dialogRef = this.dialog.open(EnviarNotificacionComponent, {
      width: '500px',
      data: {},
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Recargar notificaciones después de enviar una nueva
        this.cargarNotificaciones();
      }
    });
  }

  /**
   * Obtiene la clase CSS según el tipo de notificación
   */
  obtenerClaseNotificacion(tipo: TipoNotificacion): string {
    switch (tipo) {
      case TipoNotificacion.INFO:
        return 'notificacion-info';
      case TipoNotificacion.EXITO:
        return 'notificacion-exito';
      case TipoNotificacion.ADVERTENCIA:
        return 'notificacion-advertencia';
      case TipoNotificacion.ERROR:
        return 'notificacion-error';
      case TipoNotificacion.SISTEMA:
        return 'notificacion-sistema';
      default:
        return '';
    }
  }

  /**
   * Obtiene el icono según el tipo de notificación
   */
  obtenerIconoNotificacion(tipo: TipoNotificacion): string {
    switch (tipo) {
      case TipoNotificacion.INFO:
        return 'info';
      case TipoNotificacion.EXITO:
        return 'check_circle';
      case TipoNotificacion.ADVERTENCIA:
        return 'warning';
      case TipoNotificacion.ERROR:
        return 'error';
      case TipoNotificacion.SISTEMA:
        return 'notifications';
      default:
        return 'notifications';
    }
  }

  /**
   * Formatea la fecha para mostrarla de forma amigable
   */
  formatearFecha(fecha: string): string {
    if (!fecha) {
      return '';
    }

    const fechaObj = new Date(fecha);
    const ahora = new Date();
    const diferenciaMilisegundos = ahora.getTime() - fechaObj.getTime();
    const diferenciaMinutos = Math.floor(diferenciaMilisegundos / (1000 * 60));
    const diferenciaHoras = Math.floor(diferenciaMinutos / 60);
    const diferenciaDias = Math.floor(diferenciaHoras / 24);

    if (diferenciaMinutos < 1) {
      return 'Ahora mismo';
    } else if (diferenciaMinutos < 60) {
      return `Hace ${diferenciaMinutos} ${
        diferenciaMinutos === 1 ? 'minuto' : 'minutos'
      }`;
    } else if (diferenciaHoras < 24) {
      return `Hace ${diferenciaHoras} ${
        diferenciaHoras === 1 ? 'hora' : 'horas'
      }`;
    } else if (diferenciaDias < 7) {
      return `Hace ${diferenciaDias} ${diferenciaDias === 1 ? 'día' : 'días'}`;
    } else {
      return fechaObj.toLocaleDateString('es-ES');
    }
  }
}
