package com.midas.crm.entity.DTO.cliente;

import com.midas.crm.entity.DTO.user.UserWithCoordinadorDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClienteResidencialWithCoordinadorDTO {
    private Long id;
    private String campania;
    private String nombresApellidos;
    private String nifNie;
    private String nacionalidad;
    private LocalDate fechaNacimiento;
    private String genero;
    private String correoElectronico;
    private String cuentaBancaria;
    private String permanencia;
    private String direccion;
    private String tipoFibra;
    private String movilContacto;
    private String fijoCompania;
    private String planActual;
    private String codigoPostal;
    private String provincia;
    private String distrito;
    private String ciudad;
    private String tipoPlan;
    private String icc;
    private List<String> movilesAPortar;
    private UserWithCoordinadorDTO usuario;
    private Boolean autorizaSeguros;
    private Boolean autorizaEnergias;
    private Boolean ventaRealizada;
    private Boolean deseaPromocionesLowi;
    private LocalDateTime fechaCreacion;
    private String observacion;
    private String numeroAgente;
    private String estadoLlamada;
    private String titularDelServicio;
    private String tipoTecnologia;
    private String velocidad;
    private String futbol;
    private String numeroMoviles;
}
