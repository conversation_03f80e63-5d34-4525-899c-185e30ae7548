package com.midas.crm.validator;

import com.midas.crm.entity.DTO.calendar.CalendarDTO;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class CalendarValidator {

    public Map<String, String> validate(CalendarDTO dto) {
        Map<String, String> errors = new HashMap<>();

        if (dto.getTitulo() == null || dto.getTitulo().isBlank()) {
            errors.put("titulo", "El título es obligatorio");
        }

        if (dto.getFechaInicio() == null) {
            errors.put("fechaInicio", "La fecha de inicio es obligatoria");
        }

        if (dto.getHoraInicio() == null) {
            errors.put("horaInicio", "La hora de inicio es obligatoria");
        }

        if (dto.getFechaFinal() == null) {
            errors.put("fechaFinal", "La fecha final es obligatoria");
        }

        if (dto.getHoraFinal() == null) {
            errors.put("horaFinal", "La hora final es obligatoria");
        }

        return errors;
    }
}
