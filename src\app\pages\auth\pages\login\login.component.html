<section
  class="flex flex-col justify-center items-center w-full h-full bg-[url('/assets/bg-login.webp')] bg-no-repeat bg-center bg-cover relative"
>
  <div
    class="bg-white dark:bg-gray-900 p-6 shadow-lg rounded-lg w-full lg:w-6/12 max-w-[500px]"
  >
    <div class="text-center mb-8">
      <img
        src="assets/logovector-MIDAS.svg"
        alt="Midas Solutions Center"
        height="50"
        class="mb-4 w-[220px] block mx-auto"
      />
      <div class="text-gray-900 dark:text-white text-3xl font-medium mb-4">
        INTRANET CRM MIDAS
      </div>
    </div>

    <form #f="ngForm" (ngSubmit)="loginUsuario(f)">
      <div>
        <label
          for="username"
          class="block text-gray-900 dark:text-white font-medium mb-2"
          >Usuario</label
        >
        <div
          class="mb-4 flex items-center relative bg-gray-100 dark:bg-gray-800 rounded-md p-2.5"
        >
          <mat-icon class="mr-2.5 text-[#002f6c] dark:text-gray-400"
            >person</mat-icon
          >
          <input
            id="username"
            type="text"
            placeholder="Ingrese su usuario"
            name="username"
            ngModel
            required
            class="w-full p-2.5 border-none bg-transparent text-base outline-none text-gray-900 dark:text-white"
          />
        </div>

        <label
          for="password"
          class="block text-gray-900 dark:text-white font-medium mb-2"
          >Contraseña</label
        >
        <div
          class="mb-4 flex items-center relative bg-gray-100 dark:bg-gray-800 rounded-md p-2.5"
        >
          <mat-icon class="mr-2.5 text-[#002f6c] dark:text-gray-400"
            >lock</mat-icon
          >
          <input
            id="password"
            [type]="hidePassword ? 'password' : 'text'"
            placeholder="Ingrese su contraseña"
            name="password"
            ngModel
            required
            class="w-full p-2.5 border-none bg-transparent text-base outline-none text-gray-900 dark:text-white"
          />
          <mat-icon
            class="absolute right-2.5 cursor-pointer text-[#002f6c] dark:text-gray-400 hover:text-[#0148a4] dark:hover:text-gray-200"
            (click)="hidePassword = !hidePassword"
          >
            {{ hidePassword ? "visibility_off" : "visibility" }}
          </mat-icon>
        </div>

        <button
          type="submit"
          class="w-full py-3 bg-[#002f6c] text-white font-bold border-none rounded-md cursor-pointer text-base mt-2.5 hover:bg-[#0148a4]"
        >
          Ingresar
        </button>
      </div>
    </form>
  </div>

  <div class="mt-5 text-center">
    <p class="m-0 text-xs text-white text-shadow">
      © {{ currentYear }} Midas Solutions Center. Creado por Desarrolladores
      Midas
    </p>
  </div>
</section>
