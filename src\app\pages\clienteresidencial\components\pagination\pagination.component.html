<!-- Controles de paginación superiores -->
<div *ngIf="showInfo && totalElements > 0" class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
  <!-- Información de registros -->
  <div class="text-sm text-gray-600 dark:text-gray-400">
    {{ getPaginationInfo() }}
  </div>
  
  <!-- Selector de tamaño de página -->
  <div *ngIf="showPageSizeSelector" class="flex items-center gap-2">
    <label class="text-sm text-gray-600 dark:text-gray-400">Mostrar:</label>
    <select 
      [value]="pageSize" 
      (change)="onPageSizeChange(+$any($event.target).value)"
      class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
    >
      <option *ngFor="let size of pageSizeOptions" [value]="size">{{ size }}</option>
    </select>
    <span class="text-sm text-gray-600 dark:text-gray-400">por página</span>
  </div>
</div>

<!-- Controles de navegación -->
<div *ngIf="totalPages > 1" class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-6">
  <!-- Información de página -->
  <div class="text-sm text-gray-600 dark:text-gray-400">
    Página {{ currentPage + 1 }} de {{ totalPages }}
  </div>
  
  <!-- Botones de navegación -->
  <div class="flex items-center gap-2">
    <!-- Botón primera página -->
    <button
      (click)="onFirstPage()"
      [disabled]="!hasPrevious"
      class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white transition-colors"
      title="Primera página"
    >
      ⏮️
    </button>
    
    <!-- Botón página anterior -->
    <button
      (click)="onPreviousPage()"
      [disabled]="!hasPrevious"
      class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white transition-colors"
      title="Página anterior"
    >
      ◀️
    </button>
    
    <!-- Números de página -->
    <ng-container *ngFor="let page of getPageNumbers()">
      <button
        (click)="onPageChange(page)"
        [class]="page === currentPage 
          ? 'px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-gray-300 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white transition-colors'
          : 'px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white transition-colors'"
      >
        {{ page + 1 }}
      </button>
    </ng-container>
    
    <!-- Botón página siguiente -->
    <button
      (click)="onNextPage()"
      [disabled]="!hasNext"
      class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white transition-colors"
      title="Página siguiente"
    >
      ▶️
    </button>
    
    <!-- Botón última página -->
    <button
      (click)="onLastPage()"
      [disabled]="!hasNext"
      class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white transition-colors"
      title="Última página"
    >
      ⏭️
    </button>
  </div>
</div>
