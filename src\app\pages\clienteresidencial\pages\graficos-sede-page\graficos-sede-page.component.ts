import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store, select } from '@ngrx/store';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Observable, take } from 'rxjs';
import * as fromRoot from '@app/store';
import * as fromClienteActions from '../../store/save/save.actions';
import * as fromClienteSelectors from '../../store/save/save.selectors';
import {
  ClienteConUsuarioDTO,
  ClienteResidencial,
} from '@app/models/backend/clienteresidencial';
import * as fromUser from '@app/store/user';
import { HttpClient } from '@angular/common/http';
import { environment } from '@src/environments/environment';
import Swal from 'sweetalert2';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

@Component({
  selector: 'app-graficos-sede-page',
  templateUrl: './graficos-sede-page.component.html',
  styleUrls: ['./graficos-sede-page.component.scss'],
})
export class GraficosSedePageComponent implements OnInit {
  // Variables para el modal
  modalVisible = false;
  selectedCliente$!: Observable<ClienteResidencial | null>;
  loading$!: Observable<boolean>;
  error$!: Observable<string | null>;
  user$!: Observable<fromUser.UserResponse>;
  editForm!: FormGroup;
  editMode = false;
  exportLoading = false;
  selectedAdvisorName!: string;

  constructor(
    private router: Router,
    private store: Store<fromRoot.State>,
    private fb: FormBuilder,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    // Inicializar observables
    this.loading$ = this.store.pipe(select(fromClienteSelectors.getLoading));
    this.error$ = this.store.pipe(select(fromClienteSelectors.getError));
    this.selectedCliente$ = this.store.pipe(
      select(fromClienteSelectors.getSelectedCliente)
    );
    this.user$ = this.store.pipe(
      select(fromUser.getUser)
    ) as Observable<fromUser.UserResponse>;

    // Inicializar formulario
    this.editForm = this.fb.group({
      id: [''],
      campania: [''],
      nombresApellidos: [''],
      nifNie: [''],
      nacionalidad: [''],
      fechaNacimiento: [''],
      genero: [''],
      correoElectronico: [''],
      cuentaBancaria: [''],
      permanencia: [''],
      direccion: [''],
      tipoFibra: [''],
      movilContacto: [''],
      fijoCompania: [''],
      planActual: [''],
      codigoPostal: [''],
      provincia: [''],
      distrito: [''],
      ciudad: [''],
      tipoPlan: [''],
      icc: [''],
      autorizaSeguros: [false],
      autorizaEnergias: [false],
      ventaRealizada: [false],
      deseaPromocionesLowi: [false],
      observacionEstado: [''],
      numeroMoviles: [''],
      movilesAPortar: [[]],
      observacion: [''],
      numeroAgente: [''],
      estadoLlamada: [''],
      titularDelServicio: [''],
      futbol: [''],
      nombres: [''],
      fechaCreacion: [''],
      tipoTecnologia: [''],
    });
  }

  volverALeads(): void {
    this.router.navigate(['/clienteresidencial/listar']);
  }

  // Método para abrir detalles de un cliente
  openDetails(cliente: ClienteConUsuarioDTO): void {
    console.log('openDetails llamado con cliente:', cliente);

    // Formatear la fecha según el tipo de dato
    let fechaCreacion = '';

    // Si es un array, convertir a fecha ISO estándar con todos los componentes
    if (
      Array.isArray(cliente.fechaIngresado) &&
      cliente.fechaIngresado.length >= 3
    ) {
      // Extraer los componentes de fecha y hora del array
      const year = cliente.fechaIngresado[0];
      const month = String(cliente.fechaIngresado[1]).padStart(2, '0');
      const day = String(cliente.fechaIngresado[2]).padStart(2, '0');
      const hours =
        cliente.fechaIngresado.length >= 4
          ? String(cliente.fechaIngresado[3]).padStart(2, '0')
          : '00';
      const minutes =
        cliente.fechaIngresado.length >= 5
          ? String(cliente.fechaIngresado[4]).padStart(2, '0')
          : '00';
      const seconds =
        cliente.fechaIngresado.length >= 6
          ? String(cliente.fechaIngresado[5]).padStart(2, '0')
          : '00';
      // Formatear milisegundos correctamente
      let millis = '000';
      if (cliente.fechaIngresado.length >= 7) {
        // Convertir el valor de nanosegundos a milisegundos (dividir por 1,000,000)
        const nanos = cliente.fechaIngresado[6];
        millis = String(Math.floor(nanos / 1000000)).padStart(3, '0');
      }

      // Crear una fecha ISO completa
      fechaCreacion = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${millis}`;
    }
    // Si es un string, convertir a formato ISO completo
    else if (typeof cliente.fechaIngresado === 'string') {
      try {
        // Intentar convertir a fecha ISO completa
        const date = new Date(cliente.fechaIngresado);
        if (!isNaN(date.getTime())) {
          // Usar directamente toISOString para obtener el formato completo
          fechaCreacion = date.toISOString().replace('Z', '');
        } else {
          fechaCreacion = cliente.getFechaCreacionFormatted();
        }
      } catch (e) {
        fechaCreacion = cliente.getFechaCreacionFormatted();
      }
    }

    // Guardar el nombre del asesor para mostrarlo en el modal
    this.selectedAdvisorName = cliente.asesor;

    // Mostrar el modal
    this.modalVisible = true;

    // Verificar que la fecha sea válida antes de enviarla
    if (fechaCreacion) {
      this.store.dispatch(
        fromClienteActions.loadClienteDetalle({
          dni: cliente.dni,
          mobile: cliente.numeroMovil,
          fechaCreacion: fechaCreacion,
        })
      );
    } else {
      // Si la fecha no es válida, enviar una cadena vacía
      this.store.dispatch(
        fromClienteActions.loadClienteDetalle({
          dni: cliente.dni,
          mobile: cliente.numeroMovil,
          fechaCreacion: '',
        })
      );
    }
  }

  // Método para cerrar el modal
  closeModal(): void {
    this.modalVisible = false;
    // Limpiar el error al cerrar el modal
    this.store.dispatch(fromClienteActions.clearClienteError());
  }

  // Métodos auxiliares para formatear fechas
  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  formatDateArray(dateArray: any[]): string {
    if (!dateArray || dateArray.length < 3) {
      return '-';
    }

    const year = dateArray[0];
    const month = String(dateArray[1]).padStart(2, '0');
    const day = String(dateArray[2]).padStart(2, '0');

    return `${day}/${month}/${year}`;
  }

  formatStringDateToText(dateString: string): string {
    if (!dateString) {
      return '-';
    }

    try {
      // Intentar convertir a fecha
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return dateString; // Si no es una fecha válida, devolver el string original
      }

      // Extraer día, mes y año
      const day = date.getDate();
      const month = date.getMonth() + 1; // getMonth() devuelve 0-11
      const year = date.getFullYear();

      // Formatear en texto
      return `${day}/${month}/${year}`;
    } catch (e) {
      console.error('Error al formatear fecha:', e);
      return dateString;
    }
  }

  formatDateArrayWithTime(dateValue: any): string {
    // Si es un string, intentar usar el pipe date
    if (typeof dateValue === 'string') {
      try {
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return `${date.getDate().toString().padStart(2, '0')}/${(
            date.getMonth() + 1
          )
            .toString()
            .padStart(2, '0')}/${date.getFullYear()} ${date
            .getHours()
            .toString()
            .padStart(2, '0')}:${date
            .getMinutes()
            .toString()
            .padStart(2, '0')}`;
        }
      } catch (e) {
        // Si hay error, continuar con el siguiente bloque
      }
    }

    // Si es un array
    if (Array.isArray(dateValue) && dateValue.length >= 3) {
      const year = dateValue[0];
      const month = String(dateValue[1]).padStart(2, '0');
      const day = String(dateValue[2]).padStart(2, '0');

      // Agregar hora y minutos si están disponibles
      let timeStr = '';
      if (dateValue.length >= 5) {
        const hour = String(dateValue[3]).padStart(2, '0');
        const minute = String(dateValue[4]).padStart(2, '0');
        timeStr = ` ${hour}:${minute}`;
      }

      return `${day}/${month}/${year}${timeStr}`;
    }

    // Si no se pudo formatear
    return '-';
  }

  /**
   * Descarga o imprime el contenido del modal como PDF
   */
  downloadOrPrint(elementId: string): void {
    const element = document.getElementById(elementId);
    if (!element) return;

    // Mostrar indicador de carga
    this.exportLoading = true;

    // Cargar el logo primero
    const logoImg = new Image();
    logoImg.src = 'assets/logovector-MIDAS.svg';

    logoImg.onload = () => {
      // Convertir el SVG a PNG usando canvas
      const canvas = document.createElement('canvas');
      const logoWidth = 300; // Ancho del logo en el canvas (mayor resolución)
      const logoHeight = 100; // Altura del logo en el canvas
      canvas.width = logoWidth;
      canvas.height = logoHeight;

      const ctx = canvas.getContext('2d');
      if (ctx) {
        // Fondo transparente
        ctx.clearRect(0, 0, logoWidth, logoHeight);
        // Dibujar el logo en el canvas
        ctx.drawImage(logoImg, 0, 0, logoWidth, logoHeight);
        // Convertir a formato de imagen
        const logoDataUrl = canvas.toDataURL('image/png');

        // Ahora proceder con la generación del PDF
        this.generatePDFWithLogo(element, logoDataUrl);
      } else {
        // Si no se puede obtener el contexto del canvas, generar PDF sin logo
        this.generatePDFWithoutLogo(element);
      }
    };

    logoImg.onerror = () => {
      console.error('Error al cargar el logo');
      // Continuar sin el logo en caso de error
      this.generatePDFWithoutLogo(element);
    };
  }

  // Método para generar PDF con logo
  private generatePDFWithLogo(element: HTMLElement, logoDataUrl: string): void {
    // Configuración para html2canvas
    const options = {
      scale: 2, // Mejor calidad
      useCORS: true,
      scrollY: -window.scrollY,
      windowHeight: document.documentElement.offsetHeight,
      allowTaint: true,
      backgroundColor: null,
    };

    html2canvas(element, options)
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();

        // Añadir el logo en la parte superior
        const logoWidth = 50; // Ancho del logo en mm en el PDF
        const logoHeight = 15; // Altura del logo en mm en el PDF
        const logoX = (pdfWidth - logoWidth) / 2; // Centrar horizontalmente
        const logoY = 10; // Margen superior para el logo

        // Añadir el logo al PDF como PNG
        pdf.addImage(logoDataUrl, 'PNG', logoX, logoY, logoWidth, logoHeight);

        // Calcular la altura proporcional manteniendo la relación de aspecto
        const contentHeight = (canvas.height * pdfWidth) / canvas.width;

        // Margen superior después del logo
        const marginTop = logoY + logoHeight + 5; // 5mm de espacio adicional después del logo

        // Si el contenido es más alto que una página, dividirlo en múltiples páginas
        if (contentHeight > pageHeight - marginTop) {
          let remainingHeight = contentHeight;
          let position = 0;

          // Primera página (con logo)
          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
          remainingHeight -= pageHeight - marginTop;
          position += pageHeight - marginTop;

          // Páginas adicionales si es necesario
          while (remainingHeight > 0) {
            pdf.addPage();

            // Añadir el logo en cada página nueva
            pdf.addImage(
              logoDataUrl,
              'PNG',
              logoX,
              logoY,
              logoWidth,
              logoHeight
            );

            pdf.addImage(
              imgData,
              'PNG',
              0,
              -(position - marginTop),
              pdfWidth,
              contentHeight,
              '',
              'FAST'
            );

            remainingHeight -= pageHeight;
            position += pageHeight;
          }
        } else {
          // Si cabe en una sola página
          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
        }

        this.selectedCliente$.pipe(take(1)).subscribe((cliente) => {
          pdf.save(`Cliente-${cliente?.movilContacto || 'detalle'}.pdf`);
          // Ocultar indicador de carga
          this.exportLoading = false;
        });
      })
      .catch((error) => {
        console.error('Error al generar PDF:', error);
        Swal.fire(
          'Error',
          'No se pudo generar el PDF. Intente nuevamente.',
          'error'
        );
        this.exportLoading = false;
      });
  }

  // Método auxiliar para generar PDF sin logo en caso de error al cargar el logo
  private generatePDFWithoutLogo(element: HTMLElement): void {
    const options = {
      scale: 2,
      useCORS: true,
      scrollY: -window.scrollY,
      windowHeight: document.documentElement.offsetHeight,
      allowTaint: true,
      backgroundColor: null,
    };

    html2canvas(element, options)
      .then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const contentHeight = (canvas.height * pdfWidth) / canvas.width;
        const pageHeight = pdf.internal.pageSize.getHeight();
        const marginTop = 10; // Margen superior sin logo

        if (contentHeight > pageHeight - marginTop) {
          let remainingHeight = contentHeight;
          let position = 0;

          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
          remainingHeight -= pageHeight - marginTop;
          position += pageHeight - marginTop;

          while (remainingHeight > 0) {
            pdf.addPage();
            pdf.addImage(
              imgData,
              'PNG',
              0,
              -(position - marginTop),
              pdfWidth,
              contentHeight,
              '',
              'FAST'
            );

            remainingHeight -= pageHeight;
            position += pageHeight;
          }
        } else {
          pdf.addImage(
            imgData,
            'PNG',
            0,
            marginTop,
            pdfWidth,
            contentHeight,
            '',
            'FAST'
          );
        }

        this.selectedCliente$.pipe(take(1)).subscribe((cliente) => {
          pdf.save(`Cliente-${cliente?.movilContacto || 'detalle'}.pdf`);
          this.exportLoading = false;
        });
      })
      .catch((error) => {
        console.error('Error al generar PDF:', error);
        Swal.fire(
          'Error',
          'No se pudo generar el PDF. Intente nuevamente.',
          'error'
        );
        this.exportLoading = false;
      });
  }

  /**
   * Descarga el Excel del cliente seleccionado
   */
  downloadModalExcel(): void {
    this.exportLoading = true;

    // Usar el cliente seleccionado
    this.selectedCliente$.pipe(take(1)).subscribe((selectedCliente) => {
      if (selectedCliente && selectedCliente.movilContacto) {
        this.downloadExcelFromBackend(selectedCliente.movilContacto);
      } else {
        Swal.fire(
          'Error',
          'No se pudo identificar el número móvil del cliente',
          'error'
        );
        this.exportLoading = false;
      }
    });
  }

  /**
   * Descarga el Excel desde el backend
   */
  private downloadExcelFromBackend(numeroMovil: string): void {
    const url = `${environment.url}api/clientes/exportar-excel-individual/${numeroMovil}`;

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (blob) => {
        // Verificar si el blob es un JSON de error
        if (blob.type === 'application/json') {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              console.error('Error del servidor:', errorJson);
              Swal.fire(
                'Error',
                errorJson.detail || 'Error al generar el Excel',
                'error'
              );
            } catch (e) {
              Swal.fire(
                'Error',
                'No se pudo procesar la respuesta del servidor',
                'error'
              );
            }
            this.exportLoading = false;
          };
          reader.readAsText(blob);
          return;
        }

        // Descargar el archivo
        this.downloadFile(blob, `Cliente-${numeroMovil}.xlsx`);
        this.exportLoading = false;
      },
      error: (error) => {
        console.error('Error al descargar Excel:', error);

        // Intentar leer el cuerpo del error para mostrar un mensaje más específico
        if (error.error instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const errorJson = JSON.parse(reader.result as string);
              Swal.fire(
                'Error',
                errorJson.detail || 'No se pudo descargar el Excel',
                'error'
              );
            } catch (e) {
              Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
            }
            this.exportLoading = false;
          };
          reader.readAsText(error.error);
        } else {
          Swal.fire('Error', 'No se pudo descargar el Excel', 'error');
          this.exportLoading = false;
        }
      },
    });
  }

  /**
   * Descarga un archivo
   */
  private downloadFile(blob: Blob, fileName: string): void {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
}
