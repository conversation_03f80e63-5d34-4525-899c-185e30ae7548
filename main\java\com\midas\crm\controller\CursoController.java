package com.midas.crm.controller;

import com.midas.crm.entity.DTO.curso.CursoCreateDTO;
import com.midas.crm.entity.DTO.curso.CursoDTO;
import com.midas.crm.entity.DTO.curso.CursoUpdateDTO;
import com.midas.crm.service.CursoService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.curso}")
@RequiredArgsConstructor
public class CursoController {

    private final CursoService cursoService;

    /**
     * Crea un nuevo curso
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<CursoDTO>> createCurso(@RequestBody CursoCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(cursoService::createCurso)
            .map(curso -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Curso creado exitosamente", curso)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Lista todos los cursos
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<CursoDTO>>> listCursos() {
        return Optional.of(cursoService.listCursos())
            .map(cursos -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de cursos", cursos)
            ))
            .orElse(ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "No se encontraron cursos", Collections.emptyList())
            ));
    }

    /**
     * Obtiene un curso por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<CursoDTO>> getCurso(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(cursoService::getCursoById)
            .map(curso -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Curso encontrado", curso)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene cursos por lista de IDs
     * Implementado con programación funcional
     */
    @PostMapping("/byIds")
    public ResponseEntity<GenericResponse<List<CursoDTO>>> getCursosByIds(@RequestBody Map<String, List<Long>> request) {
        List<Long> ids = request.get("ids");
        if (ids == null || ids.isEmpty()) {
            return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "No se encontraron cursos", new ArrayList<>())
            );
        }

        try {
            List<CursoDTO> cursosDTO = cursoService.getCursosByIds(ids);
            return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cursos encontrados", cursosDTO)
            );
        } catch (Exception e) {
            return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.ERROR, "Error al obtener cursos: " + e.getMessage(), new ArrayList<>())
            );
        }
    }

    /**
     * Actualiza un curso existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<CursoDTO>> updateCurso(@PathVariable Long id, @RequestBody CursoUpdateDTO dto) {
        return Optional.ofNullable(dto)
            .map(updateDto -> cursoService.updateCurso(id, updateDto))
            .map(curso -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Curso actualizado exitosamente", curso)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina un curso por su ID
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Object>> deleteCurso(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().build();
        }

        cursoService.deleteCurso(id);
        return ResponseEntity.ok(
            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Curso eliminado exitosamente", null)
        );
    }
}
