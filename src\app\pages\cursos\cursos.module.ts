import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule, MatOptionModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MatSortModule } from '@angular/material/sort';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatListModule } from '@angular/material/list';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCheckboxModule } from '@angular/material/checkbox';


// Pipes personalizados
import { PipesModule } from '@app/shared/pipes/pipes.module';

import { CursosRoutingModule } from './cursos-routing.module';
import { CursosComponent } from './cursos.component';
import { CursoListComponent } from './pages/curso-list/curso-list.component';
import { CursoCreateComponent } from './pages/curso-create/curso-create.component';
import { CursoEditComponent } from './pages/curso-edit/curso-edit.component';
import { MisCursosComponent } from './pages/mis-cursos/mis-cursos.component';
import { CursoDetalleComponent } from './pages/curso-detalle/curso-detalle.component';
import { CursoVideoPlayerComponent } from './components/curso-video-player/curso-video-player.component';
import { ModuloListComponent } from './components/modulo-list/modulo-list.component';
import { ModuloFormComponent } from './components/modulo-form/modulo-form.component';
import { LeccionListComponent } from './components/leccion-list/leccion-list.component';
import { LeccionFormComponent } from './components/leccion-form/leccion-form.component';
import { ProgresoUsuarioComponent } from './components/progreso-usuario/progreso-usuario.component';
import { CursoAsignarUsuariosComponent } from './components/curso-asignar-usuarios/curso-asignar-usuarios.component';
import { CursoVerAlumnosComponent } from './components/curso-ver-alumnos/curso-ver-alumnos.component';
import { CuestionarioPlayerComponent } from './components/cuestionario-player/cuestionario-player.component';
import { CuestionarioFormComponent } from './components/cuestionario-form/cuestionario-form.component';
import { SeccionListComponent } from './components/seccion-list/seccion-list.component';
import { SeccionFormComponent } from './components/seccion-form/seccion-form.component';
import { SeccionFormInlineComponent } from './components/seccion-form-inline/seccion-form-inline.component';
import { SpinnerModule } from '@app/shared/indicators';
import { FilesUploadModule } from '@app/shared/popups/files-upload/files-upload.module';
import { NotificationModule } from '@app/services';

@NgModule({
  declarations: [
    CursosComponent,
    CursoListComponent,
    CursoCreateComponent,
    CursoEditComponent,
    MisCursosComponent,
    CursoDetalleComponent,
    CursoVideoPlayerComponent,
    ModuloListComponent,
    ModuloFormComponent,
    LeccionListComponent,
    LeccionFormComponent,
    ProgresoUsuarioComponent,
    CursoAsignarUsuariosComponent,
    CursoVerAlumnosComponent,
    CuestionarioPlayerComponent,
    CuestionarioFormComponent,
    SeccionListComponent,
    SeccionFormComponent,
    SeccionFormInlineComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    FlexLayoutModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatDialogModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatOptionModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatTableModule,
    MatSortModule,
    MatTooltipModule,
    MatTabsModule,
    MatListModule,
    MatExpansionModule,
    MatProgressBarModule,
    MatRadioModule,
    MatButtonToggleModule,
    MatCheckboxModule,
    DragDropModule,
    CursosRoutingModule,
    SpinnerModule,
    FilesUploadModule,
    NotificationModule.forRoot(),
    PipesModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CursosModule { }
