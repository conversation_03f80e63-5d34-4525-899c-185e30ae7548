import { Component, OnInit, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { NavigationEnd, Router } from '@angular/router';
import { NotificationService, ThemeService } from '@app/services';
import { select, Store } from '@ngrx/store';
import { Observable, Subscription } from 'rxjs';
import * as fromRoot from './store';
import * as fromUser from './store/user';
import { filter, map } from 'rxjs/operators';
import { MatSidenav } from '@angular/material/sidenav';
import { RouterOutlet } from '@angular/router';
import {
  trigger,
  transition,
  style,
  animate,
  query,
} from '@angular/animations';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { SidenavStateService } from './shared/services/sidenav-state.service';
import { WebSocketService } from './services/websocket/WebSocketService';
import { UserStatusService } from './services/user-status.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  animations: [
    trigger('routeAnimations', [
      // Transición para "cualquier ruta" => "cualquier ruta"
      transition('* <=> *', [
        // Ocultamos la nueva vista al inicio
        query(':enter', [style({ opacity: 0 })], { optional: true }),

        // Animamos la nueva vista a opacidad 1
        query(':enter', [animate('300ms ease-in-out', style({ opacity: 1 }))], {
          optional: true,
        }),
      ]),
    ]),
  ],
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild('sidenav') sidenav!: MatSidenav;

  showSpinner = false;
  title = 'client-inmueble-app';
  user$!: Observable<fromUser.UserResponse>;
  isAuthorized$!: Observable<boolean>;
  isAdmin: boolean = false;
  isLoginRoute$!: Observable<boolean>;
  showMenu$!: Observable<boolean>;
  isMobile = false;
  private subscriptions: Subscription[] = [];

  constructor(
    private fs: AngularFirestore,
    private notification: NotificationService,
    private store: Store<fromRoot.State>,
    public router: Router,
    private breakpointObserver: BreakpointObserver,
    private themeService: ThemeService,
    public sidenavStateService: SidenavStateService,
    private webSocketService: WebSocketService,
    private userStatusService: UserStatusService
  ) {
    this.checkToken();
  }

  private checkToken(): void {
    const token = localStorage.getItem('token');
    if (!token && !this.router.url.includes('/auth/login')) {
      this.router.navigate(['/auth/login']);
      // Evitar mostrar el mensaje de error en la carga inicial
      setTimeout(() => {
        this.notification.error('No hay sesión activa');
      }, 100);
    }
  }

  ngOnInit() {
    try {
      // Suscribirse a la reconexión del WebSocket después de suspender el sistema
      const resumeSubscription = this.webSocketService
        .getMessagesByType('CONNECTION_RESUMED')
        .subscribe(() => {
          console.log(
            'AppComponent: Conexión WebSocket restaurada después de suspender el sistema'
          );

          // Mostrar notificación de conexión restaurada
          this.notification.success('Se restauró la conexión a internet', {
            duration: 5000,
            horizontalPosition: 'center',
            verticalPosition: 'top',
            panelClass: ['success-snackbar', 'connection-restored'],
          });

          // Solicitar actualización de datos si es necesario
          // Esto se manejará en cada componente que necesite actualizar sus datos
        });

      // Suscribirse a eventos de reconexión por reinicio de servidor
      const serverRestartSubscription = this.webSocketService
        .getMessagesByType('SERVER_RESTART_DETECTED')
        .subscribe(() => {
          console.log(
            'AppComponent: Reinicio de servidor detectado, intentando reconectar...'
          );

          // Mostrar notificación específica para reinicio de servidor
          this.notification.info(
            'Servidor reiniciando, reconectando automáticamente...',
            {
              duration: 8000,
              horizontalPosition: 'center',
              verticalPosition: 'top',
              panelClass: ['info-snackbar', 'server-restart'],
            }
          );
        });

      this.subscriptions.push(resumeSubscription);
      this.subscriptions.push(serverRestartSubscription);

      // Inicializar observables de usuario y autorización
      this.user$ = this.store.pipe(
        select(fromUser.getUser)
      ) as Observable<fromUser.UserResponse>;

      this.isAuthorized$ = this.store.pipe(
        select(fromUser.getIsAuthorized)
      ) as Observable<boolean>;

      // Inicializar la aplicación
      this.store.dispatch(new fromUser.Init());

      // Configurar observables para rutas
      this.isLoginRoute$ = this.router.events.pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.router.url.includes('/auth/login'))
      );

      this.showMenu$ = this.router.events.pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => !this.router.url.includes('/auth/login'))
      );

      // Guardar la URL actual cuando cambia la ruta (excepto login)
      this.router.events
        .pipe(filter((event) => event instanceof NavigationEnd))
        .subscribe((event: any) => {
          if (event.url && !event.url.includes('/auth/login')) {
            sessionStorage.setItem('lastUrl', event.url);
          }
        });

      // Restaurar la URL después de inicializar si hay una guardada
      this.isAuthorized$.subscribe((isAuthorized) => {
        if (isAuthorized) {
          const lastUrl = sessionStorage.getItem('lastUrl');
          if (lastUrl && this.router.url === '/home') {
            // Solo redirigir si estamos en la página de inicio
            this.router.navigateByUrl(lastUrl);
          }
        }
      });

      // Suscribirse al usuario para verificar si es admin
      this.user$.subscribe((user) => {
        if (user && user.role) {
          this.isAdmin = user.role.trim().toUpperCase() === 'ADMIN';

          // No iniciamos la conexión WebSocket aquí, ahora se hace en el login exitoso
          // Solo nos aseguramos de desconectar si estamos en la página de login
          if (this.router.url.includes('/auth/login')) {
            // Si estamos en la página de login, asegurarse de desconectar el WebSocket
            if (this.webSocketService.isConnected()) {
              console.log(
                'AppComponent: En página de login, desconectando WebSocket'
              );
              this.webSocketService.disconnect();

              // Resetear la bandera global al desconectar
              window.wsConnectionInitiated = false;
            }
          }
        } else {
          this.isAdmin = false;
        }
      });

      // Observar cambios en el tamaño de la pantalla
      this.breakpointObserver
        .observe([`(max-width: 768px)`])
        .subscribe((result) => {
          this.isMobile = result.matches;
          // Actualizar el estado del sidenav en el servicio
          if (this.isMobile) {
            this.sidenavStateService.setSidenavState(false);
          } else {
            this.sidenavStateService.setSidenavState(true);
          }
        });
    } catch (error) {
      console.error('Error al inicializar la aplicación:', error);
      this.notification.error('Error al inicializar la aplicación');
    }
  }

  prepareRoute(outlet: RouterOutlet) {
    // Si la ruta está activada, retornamos su "routeConfig.path" como un identificador
    return outlet?.activatedRouteData?.['animation'] || '';
  }

  // Variable para evitar múltiples cierres de sesión
  private isSigningOut = false;

  onSignOut(sidenav: MatSidenav): void {
    // Evitar múltiples cierres de sesión simultáneos
    if (this.isSigningOut) {
      console.log(
        'AppComponent: Ya hay un proceso de cierre de sesión en curso'
      );
      return;
    }

    // Marcar como en proceso de cierre de sesión
    this.isSigningOut = true;
    console.log('AppComponent: Iniciando proceso de cierre de sesión');

    try {
      if (sidenav) {
        sidenav.close();
        // Actualizar el estado del sidenav en el servicio
        this.sidenavStateService.setSidenavState(false);
      }

      // Desconectar el WebSocket antes de cerrar sesión
      // El método disconnect() en WebSocketService ya se encarga de enviar la señal de desconexión
      if (this.webSocketService.isConnected()) {
        console.log('AppComponent: Desconectando WebSocket');
        this.webSocketService.disconnect();
      }

      // Resetear la bandera global
      window.wsConnectionInitiated = false;

      // Limpiar localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('userStatus');
      localStorage.removeItem('coordinador');

      // Actualizar el store
      this.store.dispatch(new fromUser.SignOut());

      // Navegar a login
      this.router.navigate(['/auth/login']);
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
      this.notification.error('Error al cerrar sesión');

      // Resetear la bandera global incluso en caso de error
      window.wsConnectionInitiated = false;

      // Intentar navegar a login de todos modos
      this.router.navigate(['/auth/login']);
    } finally {
      // Restablecer el flag después de completar el cierre de sesión
      setTimeout(() => {
        this.isSigningOut = false;
      }, 1000);
    }
  }

  onToggleSpinner(): void {
    this.showSpinner = !this.showSpinner;
  }

  onFilesChanged(_urls: string | string[]): void {
    // Procesar los URLs de los archivos
  }

  onSuccess(): void {
    this.notification.success('El procedimiento fue exitoso');
  }

  onError(): void {
    this.notification.error('Se encontraron errores en el proceso');
  }

  /**
   * Limpia los recursos cuando se destruye el componente
   */
  ngOnDestroy(): void {
    // Limpiar suscripciones
    if (this.subscriptions && this.subscriptions.length > 0) {
      this.subscriptions.forEach((sub) => {
        if (sub) sub.unsubscribe();
      });
    }

    // Limpiar el WebSocketService
    if (this.webSocketService) {
      console.log('AppComponent: Limpiando recursos de WebSocketService');
      this.webSocketService.cleanup();
    }
  }
}
