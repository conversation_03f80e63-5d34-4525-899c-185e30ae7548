// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  name: 'dev',
  firebase: {
    config: {
      apiKey: 'AIzaSyCXlycEKPSxliqwfgHzUBYHQWMdTIWHdTM',
      authDomain: 'dotval-app.firebaseapp.com',
      projectId: 'dotval-app',
      storageBucket: 'dotval-app.appspot.com',
      messagingSenderId: '1036953496333',
      appId: '1:1036953496333:web:9de32d5210bb89dc80de44',
      measurementId: 'G-Z3BPHDP2NK',
    },
  },
  // Para desarrollo local
  //url: 'http://localhost:9039/',
  //wsUrl: 'ws://localhost:9039/ws',

  // Para producción (comentado)
  urlVentas: 'https://apisozarusac.com/ventas/api/',
  url: 'https://apisozarusac.com/BackendJava/',
  wsUrl: 'wss://apisozarusac.com/BackendJava/ws',

  // Configuración de mapas
  maps: {
    // 1 = Leaflet (Nominatim/Catastro), 0 = Mapbox
    mapaTipificacionLeaflet: 1, // Activar Leaflet
    mapaTipificacionMapbox: 1, // Activar Mapbox por defecto
    // Token de Mapbox (reemplazar con token real)
    mapboxToken:
      'pk.eyJ1Ijoic21vb3RoMjQ0NCIsImEiOiJjbWFyNWM3MW4wMXh0MnRxOGNqNmRxNjN2In0.iZrZvfUCqEWMo-SOzmHTxg',
  },
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
