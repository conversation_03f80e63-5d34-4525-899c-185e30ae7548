package com.midas.crm.controller;

import com.midas.crm.entity.DTO.SedeDTO;
import com.midas.crm.entity.DTO.SedePaginadoResponse;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.service.SedeService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/sedes")
public class SedeController {

        private final SedeService sedeService;

        @Autowired
        public SedeController(SedeService sedeService) {
                this.sedeService = sedeService;
        }

        @PostMapping
        public ResponseEntity<GenericResponse<SedeDTO>> crearSede(@Valid @RequestBody SedeDTO sedeDTO) {
                try {
                        // logger.info("Creando nueva sede: {}", sedeDTO.getNombre());
                        SedeDTO nuevaSede = sedeService.guardarSede(sedeDTO);
                        return ResponseEntity.status(HttpStatus.CREATED)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.SUCCESS,
                                                        "Sede creada exitosamente",
                                                        nuevaSede));
                } catch (MidasExceptions ex) {
                        // logger.error("Error al crear sede: {}", ex.getMessage());
                        return ResponseEntity.badRequest()
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        ex.getMessage(),
                                                        null));
                } catch (Exception e) {
                        // logger.error("Error inesperado al crear sede: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        "Error al crear la sede: " + e.getMessage(),
                                                        null));
                }
        }

        @GetMapping("/{id}")
        public ResponseEntity<GenericResponse<SedeDTO>> obtenerSedePorId(@PathVariable Long id) {
                try {
                        // logger.info("Obteniendo sede con ID: {}", id);
                        return sedeService.obtenerSedePorId(id)
                                        .map(sede -> ResponseEntity.ok(new GenericResponse<>(
                                                        GenericResponseConstants.SUCCESS,
                                                        GenericResponseConstants.OPERATION_SUCCESS,
                                                        sede)))
                                        .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SEDE_NOT_FOUND));
                } catch (MidasExceptions ex) {
                        // logger.error("Error al obtener sede: {}", ex.getMessage());
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        ex.getMessage(),
                                                        null));
                } catch (Exception e) {
                        // logger.error("Error inesperado al obtener sede: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        "Error al obtener la sede: " + e.getMessage(),
                                                        null));
                }
        }

        @GetMapping
        public ResponseEntity<GenericResponse<SedePaginadoResponse>> obtenerSedesPaginadas(
                        @RequestParam(required = false) String search,
                        @RequestParam(defaultValue = "0") int page,
                        @RequestParam(defaultValue = "10") int size,
                        @RequestParam(defaultValue = "id") String column,
                        @RequestParam(defaultValue = "asc") String order) {

                try {
                        // logger.info("Obteniendo sedes paginadas: page={}, size={}, search={}", page,
                        // size, search);
                        Sort.Direction direction = "desc".equalsIgnoreCase(order) ? Sort.Direction.DESC
                                        : Sort.Direction.ASC;
                        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, column));

                        SedePaginadoResponse response = sedeService.obtenerSedesPaginadas(search, pageable);
                        return ResponseEntity.ok(new GenericResponse<>(
                                        GenericResponseConstants.SUCCESS,
                                        GenericResponseConstants.OPERATION_SUCCESS,
                                        response));
                } catch (Exception e) {
                        // logger.error("Error al obtener sedes paginadas: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        "Error al obtener las sedes: " + e.getMessage(),
                                                        null));
                }
        }

        @GetMapping("/all")
        public ResponseEntity<GenericResponse<List<SedeDTO>>> obtenerTodasLasSedes() {
                try {
                        // logger.info("Obteniendo todas las sedes");
                        List<SedeDTO> sedes = sedeService.obtenerTodasLasSedes();
                        return ResponseEntity.ok(new GenericResponse<>(
                                        GenericResponseConstants.SUCCESS,
                                        GenericResponseConstants.OPERATION_SUCCESS,
                                        sedes));
                } catch (Exception e) {
                        // logger.error("Error al obtener todas las sedes: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        "Error al obtener todas las sedes: " + e.getMessage(),
                                                        null));
                }
        }

        @GetMapping("/activas")
        public ResponseEntity<GenericResponse<List<SedeDTO>>> obtenerSedesActivas() {
                try {
                        // logger.info("Obteniendo sedes activas");
                        List<SedeDTO> sedes = sedeService.obtenerSedesActivas();
                        return ResponseEntity.ok(new GenericResponse<>(
                                        GenericResponseConstants.SUCCESS,
                                        GenericResponseConstants.OPERATION_SUCCESS,
                                        sedes));
                } catch (Exception e) {
                        // logger.error("Error al obtener sedes activas: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        "Error al obtener las sedes activas: " + e.getMessage(),
                                                        null));
                }
        }

        @PutMapping("/{id}")
        public ResponseEntity<GenericResponse<SedeDTO>> actualizarSede(
                        @PathVariable Long id,
                        @Valid @RequestBody SedeDTO sedeDTO) {
                try {
                        // logger.info("Actualizando sede con ID: {}", id);
                        SedeDTO sedeActualizada = sedeService.actualizarSede(id, sedeDTO);
                        return ResponseEntity.ok(new GenericResponse<>(
                                        GenericResponseConstants.SUCCESS,
                                        "Sede actualizada exitosamente",
                                        sedeActualizada));
                } catch (MidasExceptions ex) {
                        // logger.error("Error al actualizar sede: {}", ex.getMessage());
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        ex.getMessage(),
                                                        null));
                } catch (Exception e) {
                        // logger.error("Error inesperado al actualizar sede: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        "Error al actualizar la sede: " + e.getMessage(),
                                                        null));
                }
        }

        @DeleteMapping("/{id}")
        public ResponseEntity<GenericResponse<Void>> eliminarSede(@PathVariable Long id) {
                try {
                        // logger.info("Eliminando sede con ID: {}", id);
                        boolean eliminado = sedeService.eliminarSede(id);
                        if (eliminado) {
                                return ResponseEntity.ok(new GenericResponse<>(
                                                GenericResponseConstants.SUCCESS,
                                                "Sede eliminada exitosamente",
                                                null));
                        } else {
                                throw new MidasExceptions(MidasErrorMessage.SEDE_NOT_FOUND);
                        }
                } catch (MidasExceptions ex) {
                        // logger.error("Error al eliminar sede: {}", ex.getMessage());
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        ex.getMessage(),
                                                        null));
                } catch (Exception e) {
                        // logger.error("Error inesperado al eliminar sede: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                        .body(new GenericResponse<>(
                                                        GenericResponseConstants.ERROR,
                                                        "Error al eliminar la sede: " + e.getMessage(),
                                                        null));
                }
        }
}
