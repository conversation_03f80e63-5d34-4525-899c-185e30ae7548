package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.faq.FaqDTO;
import com.midas.crm.entity.DTO.faq.FaqRespuestaDTO;
import com.midas.crm.entity.DTO.faq.FileFaqDTO;
import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.EstadoFaq;
import com.midas.crm.entity.Faq;
import com.midas.crm.entity.FileFaq;
import com.midas.crm.entity.User;
import com.midas.crm.repository.FaqRepository;
import com.midas.crm.repository.FaqRespuestaRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.FaqService;
import jakarta.persistence.EntityNotFoundException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FaqServiceImpl implements FaqService {

    @Autowired
    private FaqRepository faqRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private FaqRespuestaRepository faqRespuestaRepository;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private FaqRespuestaServiceImpl faqRespuestaService;

    @Override
    @Transactional
    public FaqDTO saveFaq(FaqDTO faqDTO) {
        // Usar composición de funciones para transformar, guardar y convertir
        return Optional.ofNullable(faqDTO)
                .map(this::convertToEntity)
                .map(faqRepository::save)
                .map(this::convertToDTO)
                .orElseThrow(() -> new IllegalArgumentException("No se puede guardar un FAQ nulo"));
    }

    @Override
    @Transactional
    public FaqDTO updateFaq(FaqDTO faqDTO, Long id) {
        return faqRepository.findById(id)
                .map(existingFaq -> {
                    // Actualizar campos básicos
                    existingFaq.setPregunta(faqDTO.getPregunta());
                    existingFaq.setRespuesta(faqDTO.getRespuesta());
                    existingFaq.setCategoria(faqDTO.getCategoria());

                    // Actualizar estado de respondida usando Optional
                    Optional.ofNullable(faqDTO.getRespondida())
                            .ifPresent(existingFaq::setRespondida);

                    // Actualizar usuario que responde si se proporciona
                    Optional.ofNullable(faqDTO.getUsuarioRespuestaId())
                            .flatMap(userRepository::findById)
                            .ifPresent(existingFaq::setUsuarioRespuesta);

                    // Actualizar visibilidad pública/privada
                    Optional.ofNullable(faqDTO.getEsPublica())
                            .ifPresent(existingFaq::setEsPublica);

                    // Actualizar archivos usando programación funcional
                    existingFaq.getArchivos().clear();
                    Optional.ofNullable(faqDTO.getArchivos())
                            .ifPresent(archivos ->
                                archivos.stream()
                                    .map(fileDTO -> {
                                        FileFaq fileFaq = new FileFaq();
                                        fileFaq.setName(fileDTO.getName());
                                        fileFaq.setType(fileDTO.getType());
                                        fileFaq.setSize(fileDTO.getSize());
                                        fileFaq.setUrl(fileDTO.getUrl());
                                        return fileFaq;
                                    })
                                    .forEach(existingFaq.getArchivos()::add)
                            );

                    return convertToDTO(faqRepository.save(existingFaq));
                })
                .orElseThrow(() -> new EntityNotFoundException("FAQ no encontrado con id: " + id));
    }

    @Override
    @Transactional
    public void deleteFaq(Long id) {
        // Verificar existencia y eliminar usando programación funcional
        if (faqRepository.existsById(id)) {
            faqRepository.deleteById(id);
        } else {
            throw new EntityNotFoundException("FAQ no encontrado con id: " + id);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public FaqDTO getFaqById(Long id) {
        // Usar programación funcional para buscar y convertir
        return faqRepository.findById(id)
                .map(this::convertToDTO)
                .orElseThrow(() -> new EntityNotFoundException("FAQ no encontrado con id: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqDTO> getAllFaqs() {
        // Ya está implementado con programación funcional
        return faqRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqDTO> getFaqsByCategoria(String categoria) {
        // Ya está implementado con programación funcional
        return faqRepository.findByCategoria(categoria).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public FaqDTO incrementViews(Long id) {
        // Usar programación funcional para todo el flujo
        return faqRepository.findById(id)
                .map(faq -> {
                    faq.setViews(faq.getViews() + 1);
                    return convertToDTO(faqRepository.save(faq));
                })
                .orElseThrow(() -> new EntityNotFoundException("FAQ no encontrado con id: " + id));
    }

    @Override
    @Transactional
    public FaqDTO crearPregunta(FaqDTO faqDTO, Long usuarioId) {
        // Buscar el usuario y crear la pregunta usando programación funcional
        return userRepository.findById(usuarioId)
                .map(usuario -> {
                    // Establecer el usuario que pregunta
                    faqDTO.setUsuarioPreguntaId(usuarioId);
                    faqDTO.setRespondida(false);

                    // Convertir a entidad, establecer usuario y guardar
                    Faq faq = convertToEntity(faqDTO);
                    faq.setUsuarioPregunta(usuario);
                    FaqDTO savedFaqDTO = convertToDTO(faqRepository.save(faq));

                    // Notificar a través de WebSocket
                    messagingTemplate.convertAndSend("/topic/faqs",
                        faqRepository.findAll().stream()
                            .map(this::convertToDTO)
                            .collect(Collectors.toList())
                    );

                    return savedFaqDTO;
                })
                .orElseThrow(() -> new EntityNotFoundException("Usuario no encontrado con id: " + usuarioId));
    }

    @Override
    @Transactional
    public FaqDTO responderPregunta(Long faqId, String respuesta, Long usuarioId) {
        // Usar programación funcional para buscar la pregunta y el usuario
        Faq faq = faqRepository.findById(faqId)
                .orElseThrow(() -> new EntityNotFoundException("FAQ no encontrado con id: " + faqId));

        User usuario = userRepository.findById(usuarioId)
                .orElseThrow(() -> new EntityNotFoundException("Usuario no encontrado con id: " + usuarioId));

        // Actualizar la respuesta usando un enfoque funcional
        return Optional.of(faq)
                .map(f -> {
                    // Actualizar campos
                    f.setRespuesta(respuesta);
                    f.setUsuarioRespuesta(usuario);
                    f.setRespondida(true);

                    // Guardar y convertir
                    FaqDTO updatedFaqDTO = convertToDTO(faqRepository.save(f));

                    // Notificar a través de WebSocket
                    messagingTemplate.convertAndSend("/topic/faqs",
                        faqRepository.findAll().stream()
                            .map(this::convertToDTO)
                            .collect(Collectors.toList())
                    );

                    return updatedFaqDTO;
                })
                .orElseThrow(() -> new IllegalStateException("Error al procesar la respuesta"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqDTO> getPreguntasByUsuario(Long usuarioId) {
        return faqRepository.findByUsuarioPregunta_Id(usuarioId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqDTO> getPreguntasSinResponder() {
        return faqRepository.findPreguntasSinResponder().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqDTO> getPreguntasPublicas() {
        return faqRepository.findPreguntasPublicas().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqDTO> getPreguntasPrivadasByUsuario(Long usuarioId) {
        return faqRepository.findByEsPublicaFalseAndUsuarioPregunta_Id(usuarioId).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    // Nuevos métodos para el estado de las preguntas
    @Override
    @Transactional
    public FaqDTO cambiarEstadoFaq(Long faqId, EstadoFaq estado) {
        // Usar programación funcional para todo el flujo
        return faqRepository.findById(faqId)
                .map(faq -> {
                    // Actualizar estado
                    faq.setEstado(estado);

                    // Guardar y convertir
                    FaqDTO updatedFaqDTO = convertToDTO(faqRepository.save(faq));

                    // Notificar a través de WebSocket
                    messagingTemplate.convertAndSend("/topic/faqs",
                        faqRepository.findAll().stream()
                            .map(this::convertToDTO)
                            .collect(Collectors.toList())
                    );

                    return updatedFaqDTO;
                })
                .orElseThrow(() -> new EntityNotFoundException("FAQ no encontrado con id: " + faqId));
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqDTO> getPreguntasPorEstado(EstadoFaq estado) {
        return faqRepository.findPreguntasPorEstado(estado).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    // Nuevos métodos para múltiples respuestas
    @Override
    @Transactional
    public FaqRespuestaDTO agregarRespuesta(Long faqId, FaqRespuestaDTO respuestaDTO, Long usuarioId) {
        return faqRespuestaService.agregarRespuesta(faqId, respuestaDTO, usuarioId);
    }

    @Override
    @Transactional
    public FaqRespuestaDTO actualizarRespuesta(Long respuestaId, FaqRespuestaDTO respuestaDTO) {
        return faqRespuestaService.actualizarRespuesta(respuestaId, respuestaDTO);
    }

    @Override
    @Transactional
    public void eliminarRespuesta(Long respuestaId) {
        faqRespuestaService.eliminarRespuesta(respuestaId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqRespuestaDTO> getRespuestasByFaqId(Long faqId) {
        return faqRespuestaService.getRespuestasByFaqId(faqId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FaqRespuestaDTO> getRespuestasByUsuarioId(Long usuarioId) {
        return faqRespuestaService.getRespuestasByUsuarioId(usuarioId);
    }

    // Métodos de conversión entre Entity y DTO
    private FaqDTO convertToDTO(Faq faq) {
        FaqDTO faqDTO = new FaqDTO();

        // Establecer propiedades básicas
        faqDTO.setId(faq.getId());
        faqDTO.setPregunta(faq.getPregunta());
        faqDTO.setRespuesta(faq.getRespuesta());
        faqDTO.setCategoria(faq.getCategoria());
        faqDTO.setViews(faq.getViews());
        faqDTO.setCreatedAt(faq.getCreatedAt());
        faqDTO.setUpdatedAt(faq.getUpdatedAt());
        faqDTO.setEsPublica(faq.getEsPublica());
        faqDTO.setRespondida(faq.getRespondida());
        faqDTO.setEstado(faq.getEstado());

        // Información del usuario que creó la pregunta usando Optional
        Optional.ofNullable(faq.getUsuarioPregunta())
                .ifPresent(usuario -> {
                    faqDTO.setUsuarioPreguntaId(usuario.getId());
                    faqDTO.setUsuarioPreguntaNombre(usuario.getNombre() + " " + usuario.getApellido());

                    // Crear UserDTO para el usuario que pregunta
                    UserDTO usuarioPreguntaDTO = new UserDTO();
                    usuarioPreguntaDTO.setId(usuario.getId());
                    usuarioPreguntaDTO.setNombre(usuario.getNombre());
                    usuarioPreguntaDTO.setApellido(usuario.getApellido());
                    usuarioPreguntaDTO.setUsername(usuario.getUsername());
                    usuarioPreguntaDTO.setEmail(usuario.getEmail());
                    usuarioPreguntaDTO.setRole(usuario.getRole());
                    faqDTO.setUsuarioPregunta(usuarioPreguntaDTO);
                });

        // Información del usuario que respondió la pregunta usando Optional
        Optional.ofNullable(faq.getUsuarioRespuesta())
                .ifPresent(usuario -> {
                    faqDTO.setUsuarioRespuestaId(usuario.getId());
                    faqDTO.setUsuarioRespuestaNombre(usuario.getNombre() + " " + usuario.getApellido());

                    // Crear UserDTO para el usuario que responde
                    UserDTO usuarioRespuestaDTO = new UserDTO();
                    usuarioRespuestaDTO.setId(usuario.getId());
                    usuarioRespuestaDTO.setNombre(usuario.getNombre());
                    usuarioRespuestaDTO.setApellido(usuario.getApellido());
                    usuarioRespuestaDTO.setUsername(usuario.getUsername());
                    usuarioRespuestaDTO.setEmail(usuario.getEmail());
                    usuarioRespuestaDTO.setRole(usuario.getRole());
                    faqDTO.setUsuarioRespuesta(usuarioRespuestaDTO);
                });

        // Procesar archivos usando Optional y programación funcional
        Optional.ofNullable(faq.getArchivos())
                .map(archivos -> archivos.stream()
                        .map(file -> {
                            FileFaqDTO fileDTO = new FileFaqDTO();
                            fileDTO.setId(file.getId());
                            fileDTO.setName(file.getName());
                            fileDTO.setType(file.getType());
                            fileDTO.setSize(file.getSize());
                            fileDTO.setUrl(file.getUrl());
                            return fileDTO;
                        })
                        .collect(Collectors.toList()))
                .ifPresent(faqDTO::setArchivos);

        return faqDTO;
    }

    private Faq convertToEntity(FaqDTO faqDTO) {
        Faq faq = new Faq();

        // Establecer propiedades básicas
        faq.setId(faqDTO.getId());
        faq.setPregunta(faqDTO.getPregunta());
        faq.setRespuesta(faqDTO.getRespuesta());
        faq.setCategoria(faqDTO.getCategoria());

        // Usar Optional para valores que pueden ser nulos
        faq.setViews(Optional.ofNullable(faqDTO.getViews()).orElse(0));
        faq.setEsPublica(Optional.ofNullable(faqDTO.getEsPublica()).orElse(true));
        faq.setRespondida(Optional.ofNullable(faqDTO.getRespondida()).orElse(false));
        faq.setEstado(Optional.ofNullable(faqDTO.getEstado()).orElse(EstadoFaq.ABIERTA));

        // Establecer usuario que pregunta usando Optional y flatMap
        Optional.ofNullable(faqDTO.getUsuarioPreguntaId())
                .flatMap(userRepository::findById)
                .ifPresent(faq::setUsuarioPregunta);

        // Establecer usuario que responde usando Optional y flatMap
        Optional.ofNullable(faqDTO.getUsuarioRespuestaId())
                .flatMap(userRepository::findById)
                .ifPresent(faq::setUsuarioRespuesta);

        // Procesar archivos usando Optional y programación funcional
        Optional.ofNullable(faqDTO.getArchivos())
                .map(archivos -> archivos.stream()
                        .map(fileDTO -> {
                            FileFaq file = new FileFaq();
                            file.setName(fileDTO.getName());
                            file.setType(fileDTO.getType());
                            file.setSize(fileDTO.getSize());
                            file.setUrl(fileDTO.getUrl());
                            return file;
                        })
                        .collect(Collectors.toList()))
                .ifPresent(faq::setArchivos);

        return faq;
    }
}
