import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { User } from '@app/models/backend/user';
import { Curso } from '@app/models/backend/curso/curso.model';
import { CursoUsuarioService } from '@app/services/curso-usuario.service';
import { UserService } from '@app/services/user.service';
import { NotificationService } from '@app/services/notification/notification.service';
import { CursoUsuario, AsignacionMasivaResponse } from '@app/models/backend/curso/curso-usuario.model';
import { GenericResponse } from '@app/models/backend';

@Component({
  selector: 'app-curso-asignar-usuarios',
  templateUrl: './curso-asignar-usuarios.component.html',
  styleUrls: ['./curso-asignar-usuarios.component.scss']
})
export class CursoAsignarUsuariosComponent implements OnInit, OnDestroy {
  curso: Curso;
  usuarios: User[] = [];
  usuariosAsignados: CursoUsuario[] = [];
  usuariosSeleccionados: number[] = [];
  loading = false;
  error: string | null = null;
  searchControl = new FormControl('');
  filteredUsuarios: User[] = [];

  // Búsqueda
  searchTerm = '';
  searchLoading = false;

  // Paginación
  currentPage = 0;
  pageSize = 10;
  totalItems = 0;
  totalPages = 0;
  allUsersLoaded = false;

  private destroy$ = new Subject<void>();

  // Inicializar el formulario
  form = this.fb.group({
    usuariosIds: [[] as number[]]
  });

  constructor(
    private fb: FormBuilder,
    private cursoUsuarioService: CursoUsuarioService,
    private userService: UserService,
    private notification: NotificationService,
    private dialogRef: MatDialogRef<CursoAsignarUsuariosComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { curso: Curso }
  ) {
    this.curso = data.curso;
  }

  ngOnInit(): void {
    // Cargar usuarios iniciales
    this.loadUsuarios();

    // Configurar búsqueda con debounce
    this.searchControl.valueChanges
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300), // Esperar 300ms después de la última entrada
        distinctUntilChanged() // Solo emitir si el valor ha cambiado
      )
      .subscribe(value => {
        this.searchTerm = value || '';
        this.searchUsers(this.searchTerm);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Carga los usuarios disponibles para el curso con paginación
   * @param page Número de página (0-indexed)
   */
  loadUsuarios(page: number = 0): void {
    if (this.allUsersLoaded && page > 0) {
      return; // Si ya se cargaron todos los usuarios, no hacer nada (excepto para la página 0)
    }

    this.loading = true;
    this.error = null;

    // Si estamos cargando la primera página, reiniciar el estado
    if (page === 0) {
      this.usuarios = [];
      this.filteredUsuarios = [];
      this.allUsersLoaded = false;
    }

    // Usar el nuevo endpoint para obtener usuarios disponibles para el curso
    this.cursoUsuarioService.getUsuariosDisponiblesParaCurso(this.curso.id, page, this.pageSize)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<any>) => {
          if (response.rpta === 1 && response.data) {
            // El endpoint devuelve un objeto paginado
            const newUsers = response.data.users || [];

            // Actualizar información de paginación
            this.currentPage = response.data.currentPage || 0;
            this.totalItems = response.data.totalItems || 0;
            this.totalPages = response.data.totalPages || 0;

            // Verificar si hay duplicados antes de añadir
            const existingIds = new Set(this.usuarios.map((u: User) => u.id));
            const uniqueNewUsers = newUsers.filter((u: User) => !existingIds.has(u.id));

            if (page === 0) {
              // Si es la primera página, reemplazar la lista
              this.usuarios = uniqueNewUsers;
            } else if (uniqueNewUsers.length > 0) {
              // Si no, añadir a la lista existente (solo usuarios únicos)
              this.usuarios = [...this.usuarios, ...uniqueNewUsers];
            }

            // Actualizar la lista filtrada
            this.filteredUsuarios = [...this.usuarios];

            // Verificar si se han cargado todos los usuarios
            this.allUsersLoaded = this.currentPage >= this.totalPages - 1 || newUsers.length === 0;
          } else {
            this.error = response.msg || 'Error al cargar usuarios disponibles';
            this.notification.error(this.error || 'Error desconocido');
          }
          this.loading = false;
        },
        error: (error: any) => {
          this.loading = false;
          this.error = 'Error al cargar usuarios disponibles. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al cargar usuarios disponibles:', error);
        }
      });
  }

  /**
   * Carga más usuarios (siguiente página)
   */
  loadMoreUsuarios(): void {
    if (this.loading || this.allUsersLoaded) {
      return;
    }

    // Cargar la siguiente página
    this.loadUsuarios(this.currentPage + 1);
  }



  /**
   * Busca usuarios disponibles para el curso utilizando la API de búsqueda
   * @param query Término de búsqueda
   */
  searchUsers(query: string): void {
    // Si no hay término de búsqueda, mostrar todos los usuarios cargados
    if (!query || query.trim() === '') {
      this.filteredUsuarios = [...this.usuarios];

      // Si no hay usuarios cargados, cargar la primera página
      if (this.usuarios.length === 0) {
        this.loadUsuarios();
      }
      return;
    }

    // Activar indicador de carga
    this.searchLoading = true;

    // Usar el servicio para buscar usuarios disponibles para el curso
    this.cursoUsuarioService.getUsuariosDisponiblesParaCurso(this.curso.id, 0, this.pageSize, query)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<any>) => {
          if (response.rpta === 1 && response.data) {
            // Actualizar la lista filtrada con los resultados de la búsqueda
            this.filteredUsuarios = response.data.users || [];

            // Actualizar información de paginación
            this.totalItems = response.data.totalItems || 0;
          } else {
            this.error = response.msg || 'Error al buscar usuarios disponibles';
            this.notification.error(this.error || 'Error desconocido');
            this.filteredUsuarios = [];
          }
          this.searchLoading = false;
        },
        error: (error: any) => {
          this.searchLoading = false;
          this.error = 'Error al buscar usuarios disponibles. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al buscar usuarios disponibles:', error);
          this.filteredUsuarios = [];
        }
      });
  }



  isUsuarioAsignado(usuarioId: number): boolean {
    // Verificar que usuariosAsignados exista y tenga elementos antes de usar some
    return this.usuariosAsignados && this.usuariosAsignados.length > 0
      ? this.usuariosAsignados.some(ua => ua.usuarioId === usuarioId)
      : false;
  }

  onSelectionChange(event: any): void {
    // Obtener los elementos seleccionados
    const selectedOptions = event.source.selectedOptions.selected;
    // Mapear a un array de IDs
    this.usuariosSeleccionados = selectedOptions.map((option: any) => option.value);

    // Actualizar el valor del formulario
    this.form.patchValue({
      usuariosIds: this.usuariosSeleccionados
    });
  }

  onSubmit(): void {
    if (this.loading) return;

    this.loading = true;
    this.error = null;

    // Obtener los IDs de usuarios seleccionados
    const usuariosIds = this.form.value.usuariosIds as number[] || [];

    // Asignar el curso a los usuarios seleccionados
    this.cursoUsuarioService.asignarCursoAUsuariosMasivo(this.curso.id, usuariosIds)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: GenericResponse<AsignacionMasivaResponse>) => {
          this.loading = false;
          if (response.rpta === 1) {
            this.notification.success(`Curso asignado a ${response.data?.totalAsignados || 0} usuarios correctamente`);
            this.dialogRef.close(true);
          } else {
            this.error = response.msg || 'Error al asignar curso a usuarios';
            this.notification.error(this.error || 'Error desconocido');
          }
        },
        error: (error: any) => {
          this.loading = false;
          this.error = 'Error al asignar curso a usuarios. Por favor, inténtelo de nuevo.';
          this.notification.error(this.error || 'Error desconocido');
          console.error('Error al asignar curso a usuarios:', error);
        }
      });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  /**
   * Limpia el campo de búsqueda
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.searchControl.setValue('');
    this.filteredUsuarios = [...this.usuarios];

    // Si no hay usuarios cargados, cargar la primera página
    if (this.usuarios.length === 0) {
      this.loadUsuarios();
    }
  }

  /**
   * Obtiene las iniciales del nombre y apellido del usuario
   * @param nombre Nombre del usuario
   * @param apellido Apellido del usuario
   * @returns Iniciales del nombre y apellido
   */
  getInitials(nombre?: string, apellido?: string): string {
    const n = nombre ? nombre.charAt(0) : '';
    const a = apellido ? apellido.charAt(0) : '';
    return (n + a).toUpperCase();
  }

  /**
   * Genera un color para el avatar basado en el username
   * @param username Username del usuario
   * @returns Color en formato hexadecimal
   */
  getAvatarColor(username?: string): string {
    if (!username) return '#3f51b5'; // Color por defecto

    // Generar un color basado en el username
    let hash = 0;
    for (let i = 0; i < username.length; i++) {
      hash = username.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Convertir a color hexadecimal
    let color = '#';
    for (let i = 0; i < 3; i++) {
      const value = (hash >> (i * 8)) & 0xFF;
      // Usar padStart en lugar de substr (que está obsoleto)
      color += value.toString(16).padStart(2, '0');
    }

    return color;
  }
}
