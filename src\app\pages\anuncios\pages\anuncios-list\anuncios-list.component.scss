.anuncios-container {
  padding: 24px;
  min-height: 100vh;

  // Estilos para el contenedor de carga
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 40px 0;

    .loading-text {
      margin-top: 16px;
      font-size: 16px;
      color: #1976d2;
      font-weight: 500;
    }
  }

  // Estilos para el contenedor de error
  .error-container {
    display: flex;
    justify-content: center;
    margin: 40px 0;

    .error-card {
      max-width: 500px;
      padding: 20px;
      text-align: center;

      .error-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        color: #f44336;
        margin-bottom: 16px;
      }

      .error-message {
        font-size: 16px;
        margin-bottom: 20px;
      }

      button {
        margin-top: 10px;
      }
    }
  }

  // Indicador de modo de respaldo
  .fallback-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff3cd;
    color: #856404;
    padding: 8px 16px;
    border-radius: 4px;
    margin-bottom: 16px;

    mat-icon {
      margin-right: 8px;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background-color: white;
    padding: 16px 24px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);

    h2 {
      margin: 0;
      font-size: 24px;
      color: #1976d2;
      font-weight: 500;
    }

    .actions {
      display: flex;
      gap: 16px;
      align-items: center;

      .search-field {
        width: 300px;
        position: relative;

        input {
          border-radius: 50px;
          border: 1px solid #e0e0e0;
          padding: 10px 15px;
          width: 100%;
          transition: all 0.3s ease;

          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
          }
        }

        mat-icon {
          position: absolute;
          right: 15px;
          top: 50%;
          transform: translateY(-50%);
          color: #9e9e9e;
        }
      }

      .nuevo-btn {
        background-color: #4caf50;
        color: white;
        border-radius: 50px;
        padding: 0 20px;
        height: 40px;
        box-shadow: 0 3px 5px rgba(76, 175, 80, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;

        &:hover {
          background-color: #43a047;
          box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
        }
      }

      .refresh-btn {
        background-color: #2196f3;
        color: white;
        border-radius: 50px;
        width: 40px;
        height: 40px;
        min-width: 40px;
        padding: 0;
        box-shadow: 0 3px 5px rgba(33, 150, 243, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: #1976d2;
          box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
          transform: rotate(180deg);
        }

        &:active {
          transform: rotate(180deg) translateY(0);
          box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
        }
      }

      .refresh-btn {
        background-color: #2196f3;
        color: white;
        border-radius: 50px;
        width: 40px;
        height: 40px;
        min-width: 40px;
        padding: 0;
        box-shadow: 0 3px 5px rgba(33, 150, 243, 0.3);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: #1976d2;
          box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
        }
      }
    }
  }

  .anuncios-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    padding: 8px;

    .anuncio-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      transition: all 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
      box-shadow: 0 4px 12px rgba(0,0,0,0.06);
      position: relative;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0,0,0,0.1);
      }

      .clickable-image {
        cursor: pointer;
        position: relative;
        overflow: hidden;
        height: 220px;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(to top, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0) 50%);
          z-index: 1;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover::before {
          opacity: 1;
        }

        img {
          height: 100%;
          width: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;
        }

        &:hover img {
          transform: scale(1.05);
        }
      }

      mat-card-content {
        flex-grow: 1;
        padding: 20px;
        position: relative;

        h3 {
          margin: 0 0 12px;
          font-size: 18px;
          color: #2c3e50;
          font-weight: 500;
          line-height: 1.3;
        }

        .categoria {
          display: inline-block;
          color: #fff;
          font-size: 12px;
          margin-bottom: 12px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          font-weight: 500;
          background-color: #1e88e5;
          padding: 4px 10px;
          border-radius: 4px;
        }

        .sede-fecha {
          font-size: 13px;
          color: #666;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .tipo-badge {
          position: absolute;
          top: 20px;
          right: 20px;
          padding: 5px 12px;
          border-radius: 50px;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;

          &.interno {
            background-color: #03a9f4;
            color: white;
          }

          &.externo {
            background-color: #0d47a1;
            color: white;
          }
        }

        .descripcion {
          color: #666;
          font-size: 14px;
          line-height: 1.6;
          margin: 0 0 16px 0;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        // Estilos para la información del anuncio
        .anuncio-info {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 16px;

          .info-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;

            mat-icon {
              font-size: 18px;
              height: 18px;
              width: 18px;
              color: #555;
            }

            &.categoria-container {
              mat-icon {
                color: #1976d2;
              }
            }

            &.estado-container {
              mat-icon {
                &.check_circle {
                  color: #4caf50;
                }
                &.cancel {
                  color: #f44336;
                }
              }
            }

            &.orden-container {
              mat-icon {
                color: #ff9800;
              }
            }
          }
        }

        // Estilos para el badge de estado
        .estado-badge {
          display: inline-block;
          padding: 4px 10px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;

          &.ACTIVO {
            background-color: #4caf50;
            color: white;
          }

          &.INACTIVO {
            background-color: #f44336;
            color: white;
          }
        }

        // Estilos para las fechas
        .fechas-container {
          margin-top: 16px;
          padding: 12px;
          background-color: #f9f9f9;
          border-radius: 8px;
          border-left: 4px solid #1976d2;

          .fecha-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            font-weight: 500;
            color: #1976d2;

            mat-icon {
              font-size: 18px;
              height: 18px;
              width: 18px;
            }
          }

          .fechas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;

            .fecha-item {
              display: flex;
              flex-direction: column;

              .fecha-label {
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
              }

              .fecha-value {
                font-size: 14px;
                font-weight: 500;
                color: #333;
              }
            }
          }
        }
      }

      .card-actions {
        padding: 12px 20px;
        margin: 0;
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #f0f0f0;

        .action-btn {
          font-weight: 500;
          position: relative;
          overflow: hidden;
          padding: 8px 20px;
          border-radius: 6px;
          transition: all 0.3s ease;

          &.update {
            color: #1976d2;

            &:hover {
              background-color: rgba(25, 118, 210, 0.1);
            }
          }

          &.delete {
            color: #f44336;

            &:hover {
              background-color: rgba(244, 67, 54, 0.1);
            }
          }

          .circle-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #1976d2;
            color: white;
            transition: all 0.3s ease;

            &:hover {
              transform: rotate(180deg);
              background-color: #0d47a1;
            }
          }
        }
      }
    }
  }

  .paginator-container {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    padding: 16px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);

    .mat-paginator {
      background: transparent;
      display: flex;
      justify-content: center;
    }
  }
}

// Media queries for responsiveness
@media (max-width: 900px) {
  .anuncios-container {
    .anuncios-grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  }
}

/* Estilos para el tema oscuro */
:host-context(.dark-theme) {
  .anuncios-container {
    background-color: #0e1c33 !important;

    .header {
      background-color: #0a1628 !important;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;

      h2 {
        color: #ffffff !important;
      }

      .search-field {
        input {
          background-color: rgba(255, 255, 255, 0.1) !important;
          border-color: rgba(255, 255, 255, 0.2) !important;
          color: #ffffff !important;
          caret-color: #ffffff !important;

          &:focus {
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2) !important;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.5) !important;
          }
        }

        mat-icon {
          color: rgba(255, 255, 255, 0.7) !important;
        }
      }
    }

    .anuncios-grid {
      .anuncio-card {
        background-color: #0a1628 !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;

        &:hover {
          box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3) !important;
        }

        mat-card-content {
          h3 {
            color: #ffffff !important;
          }

          .descripcion {
            color: rgba(255, 255, 255, 0.7) !important;
          }

          .anuncio-info {
            .info-item {
              color: rgba(255, 255, 255, 0.7) !important;

              mat-icon {
                color: rgba(255, 255, 255, 0.6) !important;
              }

              &.categoria-container {
                mat-icon {
                  color: #64b5f6 !important;
                }
              }

              &.estado-container {
                mat-icon {
                  &.check_circle {
                    color: #66bb6a !important;
                  }
                  &.cancel {
                    color: #ef5350 !important;
                  }
                }
              }

              &.orden-container {
                mat-icon {
                  color: #ffb74d !important;
                }
              }
            }
          }

          .estado-badge {
            &.ACTIVO {
              background-color: #43a047 !important;
            }

            &.INACTIVO {
              background-color: #d32f2f !important;
            }
          }

          .fechas-container {
            background-color: rgba(255, 255, 255, 0.05) !important;
            border-left-color: #64b5f6 !important;

            .fecha-header {
              color: #64b5f6 !important;
            }

            .fechas-grid {
              .fecha-item {
                .fecha-label {
                  color: rgba(255, 255, 255, 0.5) !important;
                }

                .fecha-value {
                  color: rgba(255, 255, 255, 0.9) !important;
                }
              }
            }
          }

          .sede-fecha {
            color: rgba(255, 255, 255, 0.6) !important;
          }
        }

        .card-actions {
          border-top-color: rgba(255, 255, 255, 0.1) !important;

          .action-btn {
            color: #ffffff !important;

            &.update:hover {
              background-color: rgba(255, 255, 255, 0.1) !important;
            }

            &.delete:hover {
              background-color: rgba(255, 255, 255, 0.1) !important;
            }
          }

          .circle-icon {
            background-color: #1e4976 !important;

            &:hover {
              background-color: #26AFE5 !important;
            }
          }
        }
      }
    }

    .paginator-container {
      background-color: #0a1628 !important;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;

      .mat-paginator {
        color: #ffffff !important;
      }
    }

    .loading-container {
      .loading-text {
        color: #64b5f6 !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .anuncios-container {
    padding: 16px;

    .header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      padding: 16px;

      .actions {
        width: 100%;
        flex-direction: column;

        .search-field {
          width: 100%;
        }

        .nuevo-btn {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .anuncios-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}
.faq-add-button {
  padding: 0.375rem 0.75rem;
  background-color: #4e73df;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.faq-add-button:hover {
  background-color: #2e59d9;
}