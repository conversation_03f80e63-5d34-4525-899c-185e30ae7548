package com.midas.crm.service;

import com.midas.crm.entity.DTO.curso.CursoCreateDTO;
import com.midas.crm.entity.DTO.curso.CursoDTO;
import com.midas.crm.entity.DTO.curso.CursoUpdateDTO;

import java.util.List;

public interface CursoService {
    CursoDTO createCurso(CursoCreateDTO dto);
    List<CursoDTO> listCursos();
    CursoDTO getCursoById(Long id);
    CursoDTO updateCurso(Long id, CursoUpdateDTO dto);
    void deleteCurso(Long id);
    // En CursoService.java (interfaz)
    List<CursoDTO> getCursosByIds(List<Long> ids);
}
