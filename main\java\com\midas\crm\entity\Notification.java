package com.midas.crm.entity;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Entidad para almacenar notificaciones
 */
@Entity
@Table(name = "notifications")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Notification {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // Usuario destinatario (null si es para todos)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_id")
    private User recipient;

    // ID del usuario destinatario (para mantener compatibilidad)
    // Debe ser insertable=false, updatable=false porque la columna ya está mapeada
    // por la relación @ManyToOne
    @Column(name = "recipient_id", insertable = false, updatable = false)
    private Long recipientId;

    // Usuario remitente (null si es del sistema)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id")
    private User sender;

    // ID del usuario remitente (para mantener compatibilidad)
    @Column(name = "sender_id", insertable = false, updatable = false)
    private Long senderId;

    @Column(name = "sender_name")
    private String senderName;

    @Column(name = "title", length = 200)
    private String title;

    @Column(name = "message", length = 500)
    private String message;

    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "is_read")
    private boolean read = false;

    @Column(name = "avatar")
    private String avatar;

    // Tipo de notificación
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private NotificationType type;

    // Categoría para organización en la UI
    @Enumerated(EnumType.STRING)
    @Column(name = "category")
    private NotificationCategory category;

    // Datos adicionales en formato JSON (opcional)
    @Column(name = "data", columnDefinition = "TEXT")
    private String data;

    // Tipos de notificación
    public enum NotificationType {
        SYSTEM, // Notificación del sistema
        USER_MESSAGE, // Mensaje directo de usuario a usuario
        BROADCAST, // Notificación para todos los usuarios
        ROLE_BASED, // Notificación para usuarios con un rol específico
        ALERT, // Alerta importante
        INFO // Información general
    }

    // Categorías para organizar en la UI
    public enum NotificationCategory {
        INBOX, // Bandeja de entrada
        GENERAL, // General
        ARCHIVED // Archivado
    }
}
