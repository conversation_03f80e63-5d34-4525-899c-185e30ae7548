package com.midas.crm.service;


import com.midas.crm.entity.DTO.asesor.AsesorConClientesDTO;
import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsignacionAsesorDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorDTO;
import com.midas.crm.entity.User;
import com.midas.crm.utils.GenericResponse;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface CoordinadorService {
    CoordinadorDTO asignarAsesoresACoordinador(AsignacionAsesorDTO asignacionDTO);
    Page<CoordinadorDTO> obtenerTodosLosCoordinadoresPaginado(int page, int size);
    CoordinadorDTO obtenerCoordinadorPorId(Long coordinadorId);
    List<AsesorDTO> obtenerAsesoresSinCoordinador();
    boolean eliminarAsesorDeCoordinador(Long coordinadorId, Long asesorId);



    GenericResponse<Map<String, Object>> obtenerClientesPorCoordinador(
            Long coordinadorId,
            String dni,
            String nombre,
            String numeroMovil,
            String fecha,
            int page,
            int size);
}