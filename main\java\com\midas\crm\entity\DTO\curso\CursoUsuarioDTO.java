package com.midas.crm.entity.DTO.curso;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class CursoUsuarioDTO {
    private Long id;
    private Long cursoId;
    private Long usuarioId;
    private String usuarioNombre;
    private String usuarioApellido;
    private String usuarioUsername;
    private String usuarioEmail;
    private String usuarioDni;
    private String usuarioRole;
    private LocalDateTime fechaAsignacion;
    private String estado;
    private Boolean completado;
    private LocalDateTime fechaCompletado;
    private Integer porcentajeCompletado;
    private LocalDateTime ultimaVisualizacion;

    // Constructor con campos básicos
    public CursoUsuarioDTO(Long id, Long cursoId, Long usuarioId, LocalDateTime fechaAsignacion, String estado, Boolean completado, LocalDateTime fechaCompletado, Integer porcentajeCompletado, LocalDateTime ultimaVisualizacion) {
        this.id = id;
        this.cursoId = cursoId;
        this.usuarioId = usuarioId;
        this.fechaAsignacion = fechaAsignacion;
        this.estado = estado;
        this.completado = completado;
        this.fechaCompletado = fechaCompletado;
        this.porcentajeCompletado = porcentajeCompletado;
        this.ultimaVisualizacion = ultimaVisualizacion;
    }

    // Constructor con todos los campos
    public CursoUsuarioDTO(Long id, Long cursoId, Long usuarioId, String usuarioNombre, String usuarioApellido, String usuarioUsername, String usuarioEmail, String usuarioDni, Role usuarioRole, LocalDateTime fechaAsignacion, String estado, Boolean completado, LocalDateTime fechaCompletado, Integer porcentajeCompletado, LocalDateTime ultimaVisualizacion) {
        this.id = id;
        this.cursoId = cursoId;
        this.usuarioId = usuarioId;
        this.usuarioNombre = usuarioNombre;
        this.usuarioApellido = usuarioApellido;
        this.usuarioUsername = usuarioUsername;
        this.usuarioEmail = usuarioEmail;
        this.usuarioDni = usuarioDni;
        this.usuarioRole = usuarioRole != null ? usuarioRole.name() : null;
        this.fechaAsignacion = fechaAsignacion;
        this.estado = estado;
        this.completado = completado;
        this.fechaCompletado = fechaCompletado;
        this.porcentajeCompletado = porcentajeCompletado;
        this.ultimaVisualizacion = ultimaVisualizacion;
    }
}