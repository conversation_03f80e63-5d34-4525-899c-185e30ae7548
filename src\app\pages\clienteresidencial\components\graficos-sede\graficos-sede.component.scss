// Estilos específicos para el componente de gráficos por sede
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    .mat-mdc-form-field-flex {
      .mat-mdc-form-field-infix {
        input[type="date"] {
          color: inherit;
        }
      }
    }
  }
}

// Estilos para modo oscuro
:host-context(.dark) {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background-color: rgb(31 41 55);
      
      .mat-mdc-form-field-flex {
        .mat-mdc-form-field-infix {
          input {
            color: white;
          }
        }
      }
    }
  }
}

// Estilos para la tabla
.mat-mdc-table {
  background: transparent;
  
  .mat-mdc-header-row {
    background: transparent;
  }
  
  .mat-mdc-row {
    background: transparent;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}

// Estilos para números destacados
.font-bold {
  font-weight: 700;
}

// Animaciones suaves
.transition-all {
  transition: all 0.3s ease;
}

// Responsive
@media (max-width: 768px) {
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }
}
