/**
 * Modelo para representar información de usuarios que han leído una notificación
 */
export interface NotificationReadUser {
  userId: number;
  userName: string;
  userEmail: string;
  readAt: string; // Fecha ISO
}

/**
 * Respuesta del backend para usuarios que han leído una notificación
 */
export interface NotificationReadersResponse {
  readers: NotificationReadUser[];
  readCount: number;
  notificationId: number;
}
