import { Model } from "../calendar/Model";

export class Manual extends Model{
  public id: number;
  public nombre: string;
  public tipo: string;
  public archivo: string;
  public isActive: boolean;
  public userCreateId: number;
  public createdAt: string;
  public updatedAt: string;
  public tipoText: string;

  constructor(data?: object){
    super(data);
    this.id = 0;
    this.nombre = '';
    this.tipo = '';
    this.archivo = '';
    this.isActive = true;
    this.userCreateId = 0;
    this.createdAt = '';
    this.updatedAt = '';
    this.tipoText = '';

    // Asignar datos si se proporcionan
    if (data) {
      Object.assign(this, data);
    }
  }

  public static cast(data: object): Manual{
    return new Manual(data);
  }

  public static casts(dataArray: object[]): Manual[]{
    return dataArray.map((data) => Manual.cast(data));
  }
}

export class ManualList extends Model{
  public id: number;
  public nombre: string;
  public tipo: string;
  public tipoText: string;
  public archivo: string;
  public isActive: boolean;
  public userCreateId: number;
  public userUpdateId: number;
  public userDeleteId: number;
  public createdAt: string;
  public updatedAt: string;
  public deletedAt: string;

  constructor(data?: object){
    super(data);
    this.id = 0;
    this.nombre = '';
    this.tipo = '';
    this.tipoText = '';
    this.archivo = '';
    this.isActive = false;
    this.userCreateId = 0;
    this.userUpdateId = 0;
    this.userDeleteId = 0;
    this.createdAt = '';
    this.updatedAt = '';
    this.deletedAt = '';

    // Asignar datos si se proporcionan
    if (data) {
      Object.assign(this, data);
    }

    // Asegurarse de que tipoText se genere correctamente si no viene en los datos
    if (this.tipo && !this.tipoText) {
      this.tipoText = this.getTipoText(this.tipo);
    }
  }

  private getTipoText(tipo: string): string {
    switch (tipo ? tipo.toUpperCase() : '') {
      case 'S': return 'Manual de Software';
      case 'B': return 'Gestión de Backlog';
      case 'M': return 'Vodafone Micropyme';
      case 'R': return 'Vodafone Residencial';
      case 'O': return 'Otro';
      default: return '';
    }
  }

  public static cast(data: object): ManualList{
    return new ManualList(data);
  }

  public static casts(dataArray: object[]): ManualList[]{
    return dataArray.map((data) => ManualList.cast(data));
  }
}