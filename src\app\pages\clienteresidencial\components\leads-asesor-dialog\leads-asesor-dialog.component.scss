// Estilos específicos para el diálogo de leads del asesor
.mat-dialog-container {
  padding: 0 !important;
}

// Asegurar que el diálogo sea responsivo
:host {
  display: block;
  width: 100%;
  max-width: 90vw;
  
  @media (min-width: 768px) {
    max-width: 80vw;
  }
  
  @media (min-width: 1024px) {
    max-width: 70vw;
  }
}

// Estilos para la tabla dentro del diálogo
.table-container {
  max-height: 400px;
  overflow-y: auto;
}

// Estilos para el estado de carga
.loading-container {
  min-height: 200px;
}

// Estilos para el estado vacío
.empty-state {
  min-height: 200px;
}
