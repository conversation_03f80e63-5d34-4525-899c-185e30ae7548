package com.midas.crm.event;

import com.midas.crm.entity.Manual;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Evento que se dispara cuando se crea un nuevo manual
 */
@Getter
public class ManualCreatedEvent extends ApplicationEvent {
    
    private final Manual manual;
    
    public ManualCreatedEvent(Object source, Manual manual) {
        super(source);
        this.manual = manual;
    }
}
