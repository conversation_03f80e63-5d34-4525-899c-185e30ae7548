.asignar-usuarios-container {
  padding: 16px;
  max-width: 800px;
  width: 100%;
  box-sizing: border-box;
  height: 70vh;
  display: flex;
  flex-direction: column;

  h2 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #3f51b5;
    font-size: 18px;

    .dark-theme & {
      color: #7986cb;
    }
  }
}

.search-container {
  margin-bottom: 12px;

  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
  }

  ::ng-deep .mat-form-field-flex {
    transition: box-shadow 0.3s ease;
  }

  ::ng-deep .mat-form-field-flex:hover {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  ::ng-deep .mat-form-field-infix {
    padding: 0.5em 0;
    width: auto;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.5em 0;
  }
}

.full-width {
  width: 100%;
}

.search-results-info {
  display: flex;
  align-items: center;
  background-color: #e3f2fd;
  padding: 8px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;

  mat-icon {
    margin-right: 8px;
    color: #1976d2;
  }

  span {
    color: #0d47a1;
  }

  .dark-theme & {
    background-color: #1a2035;

    mat-icon {
      color: #7986cb;
    }

    span {
      color: #c5cae9;
    }
  }
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  text-align: center;

  mat-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    margin-bottom: 16px;
    color: #b0bec5;
  }

  span {
    font-size: 16px;
    margin-bottom: 16px;
    color: #546e7a;
  }

  .dark-theme & {
    mat-icon {
      color: #455a64;
    }

    span {
      color: #b0bec5;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;

  p {
    margin-top: 16px;
    color: #666;

    .dark-theme & {
      color: #bbb;
    }
  }
}

.error-message {
  display: flex;
  align-items: center;
  color: #f44336;
  margin: 16px 0;

  mat-icon {
    margin-right: 8px;
  }
}

.usuario-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 6px 0;
  margin-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .dark-theme & {
    border-bottom-color: #333;
  }

  .usuario-avatar {
    margin-right: 12px;
    flex-shrink: 0;

    .avatar-circle {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
      font-size: 14px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
  }

  .usuario-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0; // Evita que el contenido se desborde

    .usuario-nombre-container {
      display: flex;
      align-items: baseline;
      flex-wrap: wrap;
      margin-bottom: 2px;

      .usuario-nombre {
        font-weight: 500;
        font-size: 14px;
        margin-right: 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .usuario-username {
        font-size: 12px;
        color: #666;
        white-space: nowrap;

        .dark-theme & {
          color: #bbb;
        }
      }
    }

    .usuario-detalles {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin: 2px 0;

      .usuario-email, .usuario-dni, .usuario-sede {
        font-size: 12px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .dark-theme & {
          color: #bbb;
        }
      }
    }

    .usuario-rol-container {
      margin-top: 2px;

      .usuario-rol {
        font-size: 11px;
        font-weight: 500;
        padding: 2px 8px;
        border-radius: 10px;
        display: inline-block;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        &.role-admin {
          background-color: #f44336;
          color: white;
        }

        &.role-asesor {
          background-color: #4caf50;
          color: white;
        }

        &.role-coordinador {
          background-color: #2196f3;
          color: white;
        }

        &.role-backoffice {
          background-color: #ff9800;
          color: white;
        }

        &.role-auditor {
          background-color: #9c27b0;
          color: white;
        }

        &.role-programador {
          background-color: #607d8b;
          color: white;
        }
      }
    }
  }

  .usuario-status {
    margin-left: 8px;
    flex-shrink: 0;

    .asignado-icon {
      font-size: 20px;
    }
  }
}

.seleccion-info {
  margin-top: 16px;
  font-size: 14px;
  color: #666;

  p {
    margin: 4px 0;
  }

  .loading-more {
    color: #3f51b5;
    font-style: italic;

    &::after {
      content: '...';
      animation: dots 1.5s infinite;
    }
  }

  .dark-theme & {
    color: #bbb;

    .loading-more {
      color: #7986cb;
    }
  }
}

@keyframes dots {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%, 100% {
    content: '...';
  }
}

mat-dialog-content {
  flex: 1;
  overflow: auto;
  max-height: none !important;
}

.usuarios-list-container {
  flex: 1;
  overflow: auto;
  margin-bottom: 16px;

  mat-selection-list {
    padding-top: 0;

    ::ng-deep .mat-list-option {
      margin-bottom: 2px;
      height: auto !important;
      padding: 2px 0;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      .dark-theme & {
        &:hover {
          background-color: rgba(255, 255, 255, 0.04);
        }
      }

      .mat-list-item-content {
        padding: 0 8px;
        height: auto !important;
        min-height: 36px;
      }

      .mat-pseudo-checkbox {
        margin-right: 8px;
      }
    }
  }
}

mat-dialog-actions {
  margin-top: 16px;
  padding: 8px 0;
  border-top: 1px solid #e0e0e0;

  .dark-theme & {
    border-top-color: #424242;
  }
}
