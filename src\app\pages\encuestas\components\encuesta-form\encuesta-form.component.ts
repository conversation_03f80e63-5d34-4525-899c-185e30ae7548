import { Component, OnInit, OnDestroy, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';

import { EncuestaService } from '@app/services/encuesta.service';
import { NotificationService } from '@app/services/notification/notification.service';
import {
  Encuesta,
  EncuestaCreateRequest,
  TipoAsignacion,
} from '@app/models/backend/encuesta/encuesta.model';
import { TipoPregunta } from '@app/models/backend/encuesta/pregunta-encuesta.model';
import { UserService } from '@app/services/user.service';
import { SedeService } from '@app/services/sede.service';

@Component({
  selector: 'app-encuesta-form',
  templateUrl: './encuesta-form.component.html',
  styleUrls: ['./encuesta-form.component.scss'],
})
export class EncuestaFormComponent implements OnInit, OnDestroy {
  // Formulario
  form: FormGroup;
  preguntasForm: FormArray;

  // Enums
  tipoAsignacion = TipoAsignacion;
  tipoPregunta = TipoPregunta;

  // Listas
  sedes: any[] = [];
  coordinadores: any[] = [];
  usuarios: any[] = [];

  // Estado
  loading = false;
  submitting = false;

  // Destructor del componente
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private encuestaService: EncuestaService,
    private userService: UserService,
    private sedeService: SedeService,
    private notificationService: NotificationService,
    private dialogRef: MatDialogRef<EncuestaFormComponent>,
    private http: HttpClient,
    @Inject(MAT_DIALOG_DATA)
    public data: { isNew: boolean; encuesta?: Encuesta }
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadSedes();
    this.loadUsuarios();

    // Intentar cargar coordinadores directamente desde la API de ventas
    // para evitar errores cuando se seleccione el tipo de asignación COORDINACION
    this.loadCoordinadoresDirecto();

    // Cargar datos de la encuesta si estamos editando
    if (!this.data.isNew && this.data.encuesta) {
      this.loadEncuestaData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Inicializa el formulario
   */
  private initForm(): void {
    this.form = this.fb.group({
      titulo: ['', [Validators.required, Validators.maxLength(100)]],
      descripcion: ['', [Validators.maxLength(500)]],
      fechaInicio: [null],
      fechaFin: [null],
      tiempoLimite: [null, [Validators.min(1)]],
      esAnonima: [false],
      mostrarResultados: [false],
      tipoAsignacion: [TipoAsignacion.TODOS, [Validators.required]],
      sedeId: [null],
      coordinadorId: [null],
      usuarioId: [null],
      estado: ['A'],
      preguntas: this.fb.array([]),
    });

    this.preguntasForm = this.form.get('preguntas') as FormArray;

    // Validaciones condicionales
    this.form
      .get('tipoAsignacion')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((value) => {
        const sedeIdControl = this.form.get('sedeId');
        const coordinadorIdControl = this.form.get('coordinadorId');
        const usuarioIdControl = this.form.get('usuarioId');

        // Resetear todos los controles
        sedeIdControl?.clearValidators();
        coordinadorIdControl?.clearValidators();
        usuarioIdControl?.clearValidators();

        // Aplicar validadores según el tipo de asignación
        switch (value) {
          case TipoAsignacion.SEDE:
            sedeIdControl?.setValidators([Validators.required]);
            break;
          case TipoAsignacion.COORDINACION:
            coordinadorIdControl?.setValidators([Validators.required]);
            // Cargar coordinadores solo si no se han cargado ya
            if (this.coordinadores.length === 0) {
              this.loadCoordinadoresDirecto();
            }
            break;
          case TipoAsignacion.PERSONAL:
            usuarioIdControl?.setValidators([Validators.required]);
            break;
        }

        sedeIdControl?.updateValueAndValidity();
        coordinadorIdControl?.updateValueAndValidity();
        usuarioIdControl?.updateValueAndValidity();
      });
  }

  /**
   * Carga los datos de la encuesta para edición
   */
  private loadEncuestaData(): void {
    const encuesta = this.data.encuesta;

    if (!encuesta) {
      return;
    }

    this.form.patchValue({
      titulo: encuesta.titulo,
      descripcion: encuesta.descripcion,
      fechaInicio: encuesta.fechaInicio ? new Date(encuesta.fechaInicio) : null,
      fechaFin: encuesta.fechaFin ? new Date(encuesta.fechaFin) : null,
      tiempoLimite: encuesta.tiempoLimite,
      esAnonima: encuesta.esAnonima,
      mostrarResultados: encuesta.mostrarResultados,
      tipoAsignacion: encuesta.tipoAsignacion,
      sedeId: encuesta.sedeId,
      coordinadorId: encuesta.coordinadorId,
      usuarioId: encuesta.usuarioId,
      estado: encuesta.estado,
    });

    // Cargar preguntas si existen
    if (encuesta.preguntas && encuesta.preguntas.length > 0) {
      encuesta.preguntas.forEach((pregunta) => {
        const preguntaGroup = this.createPreguntaGroup();
        preguntaGroup.patchValue({
          id: pregunta.id,
          enunciado: pregunta.enunciado,
          descripcion: pregunta.descripcion,
          orden: pregunta.orden,
          tipo: pregunta.tipo,
          esObligatoria: pregunta.esObligatoria,
        });

        // Cargar opciones si existen y el tipo de pregunta lo requiere
        if (
          pregunta.opciones &&
          pregunta.opciones.length > 0 &&
          (pregunta.tipo === TipoPregunta.OPCION_MULTIPLE ||
            pregunta.tipo === TipoPregunta.SELECCION_MULTIPLE ||
            pregunta.tipo === TipoPregunta.ESCALA_LIKERT)
        ) {
          const opcionesArray = preguntaGroup.get('opciones') as FormArray;

          // Limpiar opciones existentes
          while (opcionesArray.length > 0) {
            opcionesArray.removeAt(0);
          }

          // Agregar opciones
          pregunta.opciones.forEach((opcion) => {
            opcionesArray.push(
              this.fb.group({
                id: [opcion.id],
                texto: [opcion.texto, [Validators.required]],
                orden: [opcion.orden],
                valor: [opcion.valor],
              })
            );
          });
        }

        this.preguntasForm.push(preguntaGroup);
      });
    }
  }

  /**
   * Carga las sedes disponibles
   */
  private loadSedes(): void {
    // Si ya tenemos sedes cargadas, no es necesario volver a cargarlas
    if (this.sedes.length > 0) {
      return;
    }

    // Mostrar indicador de carga
    this.loading = true;

    // Implementar según el servicio de sedes existente
    this.sedeService
      .getAllSedes()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.sedes = response.data || [];
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Error al cargar sedes:', error);
          // Solo mostrar el error si estamos en modo de asignación por sede
          if (this.form.get('tipoAsignacion')?.value === TipoAsignacion.SEDE) {
            this.notificationService.error('Error al cargar sedes');
          }
          this.loading = false;
        },
      });
  }

  /**
   * Intenta cargar coordinadores directamente con el endpoint específico
   * Este método se usa como respaldo si el método principal falla
   */
  private loadCoordinadoresDirecto(): void {
    this.loading = true;

    // Usar el endpoint de ventas que funciona para obtener coordinadores
    const baseUrl =
      'https://apisozarusac.com/ventas/api/coordinadores/listado/';

    this.http
      .get<any>(baseUrl)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          if (response && response.data) {
            this.coordinadores = response.data;
            console.log(
              'Coordinadores cargados correctamente desde API de ventas:',
              this.coordinadores.length
            );
          } else {
            console.warn(
              'La respuesta de la API de ventas no tiene el formato esperado:',
              response
            );
            // Intentar con el endpoint original como último recurso
            this.loadCoordinadoresOriginal();
          }
          this.loading = false;
        },
        error: (error: any) => {
          console.error(
            'Error al cargar coordinadores desde API de ventas:',
            error
          );
          // Intentar con el endpoint original como último recurso
          this.loadCoordinadoresOriginal();
        },
      });
  }

  /**
   * Último intento para cargar coordinadores usando el endpoint original
   */
  private loadCoordinadoresOriginal(): void {
    this.loading = true;

    this.userService
      .getUsersByRole('COORDINADOR')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.coordinadores = response.data || [];
          this.loading = false;
        },
        error: (error: any) => {
          console.error(
            'Error al cargar coordinadores con todos los métodos:',
            error
          );

          // Si estamos en modo de asignación por coordinador, mostrar mensaje y sugerir alternativa
          if (
            this.form.get('tipoAsignacion')?.value ===
            TipoAsignacion.COORDINACION
          ) {
            this.notificationService.error(
              'No se pudieron cargar los coordinadores. Por favor, seleccione otro tipo de asignación o contacte al administrador.'
            );
          }

          // Crear un coordinador de ejemplo para evitar que el formulario quede inutilizable
          if (this.coordinadores.length === 0) {
            this.coordinadores = [
              {
                id: 0,
                nombre: 'Error al cargar coordinadores',
                apellido: 'Contacte al administrador',
              },
            ];
          }

          this.loading = false;
        },
      });
  }

  /**
   * Carga los usuarios disponibles
   */
  private loadUsuarios(): void {
    // Si ya tenemos usuarios cargados, no es necesario volver a cargarlos
    if (this.usuarios.length > 0) {
      return;
    }

    // Mostrar indicador de carga
    this.loading = true;

    // Implementar según el servicio de usuarios existente
    this.userService
      .getUsers()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.usuarios = response.data?.users || [];
          this.loading = false;
        },
        error: (error: any) => {
          console.error('Error al cargar usuarios:', error);
          // Solo mostrar el error si estamos en modo de asignación personal
          if (
            this.form.get('tipoAsignacion')?.value === TipoAsignacion.PERSONAL
          ) {
            this.notificationService.error('Error al cargar usuarios');
          }
          this.loading = false;
        },
      });
  }

  /**
   * Crea un grupo de formulario para una pregunta
   */
  createPreguntaGroup(): FormGroup {
    return this.fb.group({
      id: [null],
      enunciado: ['', [Validators.required]],
      descripcion: [''],
      orden: [this.preguntasForm ? this.preguntasForm.length + 1 : 1],
      tipo: [TipoPregunta.OPCION_MULTIPLE, [Validators.required]],
      esObligatoria: [true],
      opciones: this.fb.array([
        this.createOpcionGroup(),
        this.createOpcionGroup(),
      ]),
    });
  }

  /**
   * Crea un grupo de formulario para una opción de respuesta
   */
  createOpcionGroup(): FormGroup {
    return this.fb.group({
      id: [null],
      texto: ['', [Validators.required]],
      orden: [null],
      valor: [null],
    });
  }

  /**
   * Agrega una nueva pregunta al formulario
   */
  addPregunta(): void {
    this.preguntasForm.push(this.createPreguntaGroup());
  }

  /**
   * Elimina una pregunta del formulario
   */
  removePregunta(index: number): void {
    this.preguntasForm.removeAt(index);

    // Actualizar orden de las preguntas restantes
    for (let i = 0; i < this.preguntasForm.length; i++) {
      const ordenControl = this.preguntasForm.at(i).get('orden');
      if (ordenControl) {
        ordenControl.setValue(i + 1);
      }
    }
  }

  /**
   * Obtiene el array de opciones para una pregunta específica
   */
  getOpciones(preguntaIndex: number): FormArray {
    return this.preguntasForm.at(preguntaIndex).get('opciones') as FormArray;
  }

  /**
   * Agrega una nueva opción a una pregunta
   */
  addOpcion(preguntaIndex: number): void {
    const opcionesArray = this.getOpciones(preguntaIndex);
    opcionesArray.push(this.createOpcionGroup());

    // Actualizar orden de las opciones
    for (let i = 0; i < opcionesArray.length; i++) {
      const ordenControl = opcionesArray.at(i).get('orden');
      if (ordenControl) {
        ordenControl.setValue(i + 1);
      }
    }
  }

  /**
   * Elimina una opción de una pregunta
   */
  removeOpcion(preguntaIndex: number, opcionIndex: number): void {
    const opcionesArray = this.getOpciones(preguntaIndex);
    opcionesArray.removeAt(opcionIndex);

    // Actualizar orden de las opciones restantes
    for (let i = 0; i < opcionesArray.length; i++) {
      const ordenControl = opcionesArray.at(i).get('orden');
      if (ordenControl) {
        ordenControl.setValue(i + 1);
      }
    }
  }

  /**
   * Guarda la encuesta
   */
  onSubmit(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.notificationService.error(
        'Por favor, complete todos los campos requeridos'
      );
      return;
    }

    this.submitting = true;

    const formValue = this.form.value;

    // Convertir fechas a formato ISO
    if (formValue.fechaInicio) {
      formValue.fechaInicio = new Date(formValue.fechaInicio).toISOString();
    }

    if (formValue.fechaFin) {
      formValue.fechaFin = new Date(formValue.fechaFin).toISOString();
    }

    if (this.data.isNew) {
      // Crear nueva encuesta
      this.encuestaService
        .create(formValue)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.notificationService.success('Encuesta creada correctamente');
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error al crear encuesta:', error);
            this.notificationService.error('Error al crear encuesta');
            this.submitting = false;
          },
        });
    } else if (this.data.encuesta) {
      // Actualizar encuesta existente
      this.encuestaService
        .update(this.data.encuesta.id, formValue)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.notificationService.success(
              'Encuesta actualizada correctamente'
            );
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('Error al actualizar encuesta:', error);
            this.notificationService.error('Error al actualizar encuesta');
            this.submitting = false;
          },
        });
    }
  }

  /**
   * Cierra el diálogo
   */
  onCancel(): void {
    this.dialogRef.close(false);
  }
}
