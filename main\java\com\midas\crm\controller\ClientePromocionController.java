package com.midas.crm.controller;

import com.midas.crm.entity.DTO.cliente.ClientePromocionBody;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialWithCoordinadorDTO;
import com.midas.crm.event.ClienteCreatedEvent;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.ClienteResidencialWithCoordinadorMapper;
import com.midas.crm.service.ClienteResidencialService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;
import java.util.concurrent.CompletableFuture;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;

@RestController
@RequestMapping("${api.route.cliente-promocion}")
@RequiredArgsConstructor
public class ClientePromocionController {

    private final ClienteResidencialService clienteService;
    private final SimpMessagingTemplate messagingTemplate;
    private final ApplicationEventPublisher eventPublisher;

    @PostMapping
    public ResponseEntity<GenericResponse<?>> crearClienteYPromocion(@RequestBody ClientePromocionBody body) {
        // Validar datos de entrada usando Optional
        return Optional.ofNullable(body)
                .filter(b -> b.getClienteResidencial() != null && b.getUsuarioId() != null)
                .<ResponseEntity<GenericResponse<?>>>map(b -> {
                    try {

                        // Guardar cliente
                        ClienteResidencial clienteGuardado = clienteService.guardar(
                                b.getClienteResidencial(),
                                b.getUsuarioId());

                        // Publicar evento de creación de forma asíncrona usando CompletableFuture
                        // para no bloquear la respuesta al cliente
                        CompletableFuture.runAsync(() -> {
                            try {
                                eventPublisher.publishEvent(new ClienteCreatedEvent(this, clienteGuardado));
                            } catch (Exception e) {
                                // Error handling for event publishing
                            }
                        });

                        // Crear respuesta usando HashMap para evitar limitaciones de Map.of()
                        HashMap<String, Object> respuesta = new HashMap<>();
                        respuesta.put("mensaje", "Datos guardados con éxito");
                        respuesta.put("cliente", clienteGuardado);

                        // Construir y retornar respuesta exitosa
                        return ResponseEntity.status(HttpStatus.CREATED)
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.SUCCESS,
                                        "Cliente promoción creado exitosamente",
                                        respuesta));
                    } catch (Exception e) {
                        throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
                    }
                })
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_INVALID_DATA));
    }

    /**
     * Método para obtener un cliente por DNI, nombre completo, móvil y fecha de
     * creación
     * Ampliado para manejar posibles diferencias de zona horaria
     * Implementado con programación funcional
     * Permite búsqueda por DNI o por nombre completo del usuario
     */
    @GetMapping("/detalle")
    public ResponseEntity<GenericResponse<ClienteResidencialWithCoordinadorDTO>> obtenerClientePorDniMovilYFecha(
            @RequestParam String dni,
            @RequestParam(required = false) String nombreCompleto,
            @RequestParam String movil,
            @RequestParam("fechaCreacion") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fechaCreacion) {
        try {
            // Definir rangos de búsqueda
            long nanosPor150ms = 150_000_000L;

            // Función para buscar clientes en un rango de fechas
            BiFunction<LocalDateTime, LocalDateTime, List<ClienteResidencial>> buscarEnRango = (inicio,
                    fin) -> clienteService.buscarPorDniMovilYFechaEntre(dni, nombreCompleto, movil, inicio, fin);

            // Primer intento: búsqueda con rango pequeño (±150ms)
            return Optional.of(buscarEnRango.apply(
                    fechaCreacion.minusNanos(nanosPor150ms),
                    fechaCreacion.plusNanos(nanosPor150ms)))
                    .filter(resultados -> !resultados.isEmpty())
                    .map(resultados -> procesarResultados(resultados, fechaCreacion))
                    .orElseGet(() -> {
                        // Segundo intento: búsqueda con rango amplio (±6 horas)
                        return Optional.of(buscarEnRango.apply(
                                fechaCreacion.minusHours(6),
                                fechaCreacion.plusHours(6)))
                                .filter(resultados -> !resultados.isEmpty())
                                .map(resultados -> procesarResultados(resultados, fechaCreacion))
                                .orElse(ResponseEntity.ok(
                                        new GenericResponse<>(0, "Cliente no encontrado", null)));
                    });
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(0, "Error: " + e.getMessage(), null));
        }
    }

    /**
     * Método auxiliar para procesar los resultados de la búsqueda de clientes
     * Implementado con programación funcional
     */
    private ResponseEntity<GenericResponse<ClienteResidencialWithCoordinadorDTO>> procesarResultados(
            List<ClienteResidencial> resultados,
            LocalDateTime fechaCreacion) {
        // Encontrar el registro más cercano a la fecha solicitada usando programación
        // funcional
        return resultados.stream()
                .min(Comparator
                        .comparingLong(c -> Math.abs(Duration.between(c.getFechaCreacion(), fechaCreacion).toMillis())))
                .map(cliente -> {
                    // Inicializar la lista de móviles a portar si es null o copiarla para evitar
                    // problemas de serialización
                    cliente.setMovilesAPortar(Optional.ofNullable(cliente.getMovilesAPortar())
                            .map(ArrayList::new)
                            .orElseGet(ArrayList::new));

                    // Asegurarse de que el campo numeroMoviles esté presente
                    cliente.setNumeroMoviles(Optional.ofNullable(cliente.getNumeroMoviles()).orElse(""));

                    // Convertir la entidad a DTO con coordinador
                    ClienteResidencialWithCoordinadorDTO clienteDTO = ClienteResidencialWithCoordinadorMapper
                            .toDTO(cliente);

                    // Asegurar que la lista de móviles a portar en el DTO esté inicializada
                    clienteDTO.setMovilesAPortar(Optional.ofNullable(clienteDTO.getMovilesAPortar())
                            .orElseGet(ArrayList::new));

                    // Retornar respuesta exitosa
                    return ResponseEntity.ok(new GenericResponse<>(1, "Cliente encontrado", clienteDTO));
                })
                .orElse(ResponseEntity.ok(
                        new GenericResponse<>(0, "Cliente no encontrado en el rango de fechas", null)));
    }

    /**
     * Elimina un cliente por su ID
     * Implementado con programación funcional y manejo de excepciones mejorado
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<String>> eliminarCliente(@PathVariable Long id) {
        // Función para crear respuestas de error
        Function<Exception, ResponseEntity<GenericResponse<String>>> crearRespuestaError = e -> {
            if (e instanceof NoSuchElementException) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                e.getMessage(),
                                null));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Error al eliminar cliente: " + e.getMessage(),
                                null));
            }
        };

        try {
            // Eliminar cliente y retornar respuesta exitosa
            clienteService.eliminar(id);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Cliente eliminado correctamente",
                    "ID eliminado: " + id));
        } catch (Exception e) {
            return crearRespuestaError.apply(e);
        }
    }

    /**
     * Endpoint WebSocket para obtener la lista de clientes
     * Implementado con programación funcional
     */
    @MessageMapping("/clientes.list")
    @SendTo("/topic/clientes")
    public List<ClienteResidencial> getClientesWs() {
        // Obtener y retornar la lista de clientes directamente
        return clienteService.listarTodos();
    }

    /**
     * Método para escuchar el evento de creación de cliente y notificar a los
     * clientes
     * Implementado con programación funcional y procesamiento asíncrono
     */
    @EventListener
    @Async
    public void handleClienteCreatedEvent(ClienteCreatedEvent event) {
        // Extraer cliente del evento y notificar
        Optional.ofNullable(event)
                .map(ClienteCreatedEvent::getCliente)
                .ifPresent(cliente -> {
                    try {
                        // Notificar solo sobre el nuevo cliente (optimización)
                        messagingTemplate.convertAndSend("/topic/clientes/new", cliente);

                        // Evitamos enviar la lista completa de clientes para mejorar el rendimiento
                        // En su lugar, los clientes pueden solicitar la lista completa si la necesitan
                    } catch (Exception e) {
                        // Error handling for event processing
                    }
                });
    }
}
