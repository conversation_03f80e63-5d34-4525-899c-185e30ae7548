import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Angular Material
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';

// Componentes compartidos
import { GraficosSedeComponent } from '../graficos-sede/graficos-sede.component';
import { PaginationComponent } from '../pagination/pagination.component';
import { LeadsAsesorDialogComponent } from '../leads-asesor-dialog/leads-asesor-dialog.component';
import { GraficosRendimientoLeadsComponent } from '../graficos-rendimiento-leads/graficos-rendimiento-leads.component';

@NgModule({
  declarations: [
    GraficosSedeComponent,
    PaginationComponent,
    LeadsAsesorDialogComponent,
    GraficosRendimientoLeadsComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,

    // Angular Material
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTableModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatTooltipModule,
  ],
  exports: [
    // Exportamos los componentes para que estén disponibles en los módulos que importen este módulo
    GraficosSedeComponent,
    PaginationComponent,
    LeadsAsesorDialogComponent,
    GraficosRendimientoLeadsComponent,

    // También exportamos los módulos comunes para que estén disponibles
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTableModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatTooltipModule,
  ],
})
export class SharedComponentsModule {}
