<div class="bg-white dark:bg-[#0a1628] rounded-lg shadow-md max-w-2xl mx-auto">
  <!-- Encabezado -->
  <div
    class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-blue-500/20"
  >
    <h2 class="text-xl font-semibold text-gray-800 dark:text-blue-100">
      Notificaciones
    </h2>
    <div class="flex items-center space-x-2">
      <button
        class="px-3 py-1 text-sm text-indigo-600 dark:text-blue-300 hover:text-indigo-800 dark:hover:text-blue-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
        [disabled]="contadorNoLeidas === 0"
        (click)="marcarTodasComoLeidas()"
      >
        Marcar todas como leídas
      </button>
      <button
        class="p-1 rounded-full text-indigo-600 dark:text-blue-300 hover:bg-indigo-50 dark:hover:bg-blue-900/20"
        matTooltip="Actualizar notificaciones"
        (click)="solicitarNotificaciones()"
      >
        <mat-icon>refresh</mat-icon>
      </button>
    </div>
  </div>

  <!-- Contenido -->
  <div class="overflow-y-auto max-h-[70vh]">
    <!-- Estado de carga -->
    <div *ngIf="cargando" class="flex flex-col items-center justify-center p-8">
      <mat-spinner diameter="40" [color]="'accent'"></mat-spinner>
      <p class="mt-4 text-gray-500 dark:text-blue-300/70">
        Cargando notificaciones...
      </p>
    </div>

    <!-- Sin notificaciones -->
    <div
      *ngIf="!cargando && notificaciones.length === 0"
      class="flex flex-col items-center justify-center p-8"
    >
      <div class="flex flex-col items-center justify-center text-center">
        <div
          class="w-16 h-16 flex items-center justify-center rounded-full bg-gray-100 dark:bg-blue-900/20 mb-4"
        >
          <mat-icon class="text-gray-400 dark:text-blue-300/70 text-4xl"
            >notifications_off</mat-icon
          >
        </div>
        <p class="text-gray-500 dark:text-blue-300/70 mb-1">
          No tienes notificaciones
        </p>
        <p class="text-xs text-gray-400 dark:text-blue-300/50">
          Las notificaciones aparecerán aquí
        </p>
      </div>
    </div>

    <!-- Lista de notificaciones -->
    <div
      *ngIf="!cargando && notificaciones.length > 0"
      class="divide-y divide-gray-200 dark:divide-blue-500/20"
    >
      <div
        *ngFor="let notificacion of notificaciones"
        class="p-4 cursor-pointer transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-blue-900/10"
        [ngClass]="{ 'bg-blue-50 dark:bg-blue-900/20': !notificacion.leida }"
        (click)="manejarClicNotificacion(notificacion)"
      >
        <div class="flex">
          <!-- Icono -->
          <div
            class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4"
            [ngClass]="{
              'bg-blue-100 text-blue-600 dark:bg-blue-900/40 dark:text-blue-300':
                notificacion.tipo === 'INFO',
              'bg-green-100 text-green-600 dark:bg-green-900/40 dark:text-green-300':
                notificacion.tipo === 'EXITO',
              'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/40 dark:text-yellow-300':
                notificacion.tipo === 'ADVERTENCIA',
              'bg-red-100 text-red-600 dark:bg-red-900/40 dark:text-red-300':
                notificacion.tipo === 'ERROR',
              'bg-purple-100 text-purple-600 dark:bg-purple-900/40 dark:text-purple-300':
                notificacion.tipo === 'SISTEMA'
            }"
          >
            <mat-icon>{{
              obtenerIconoNotificacion(notificacion.tipo)
            }}</mat-icon>
          </div>

          <!-- Contenido -->
          <div class="flex-1 min-w-0">
            <div class="flex justify-between items-start">
              <h3
                class="text-sm font-medium text-gray-900 dark:text-blue-100"
                [ngClass]="{ 'font-semibold': !notificacion.leida }"
              >
                {{ notificacion.titulo || "Sin título" }}
              </h3>
              <span class="text-xs text-gray-500 dark:text-blue-300/60 ml-2">
                {{ formatearFecha(notificacion.fechaCreacion) }}
              </span>
            </div>

            <!-- Remitente de la notificación -->
            <div
              class="mt-1 text-xs text-gray-500 dark:text-blue-300/70 flex items-center"
            >
              <span class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-3 w-3 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                {{ notificacion.datos?.senderName || "Sistema" }}
              </span>
            </div>

            <p
              class="mt-1 text-sm text-gray-600 dark:text-blue-200/80 line-clamp-2"
            >
              {{ notificacion.mensaje || "Sin mensaje" }}
            </p>

            <div class="mt-2 flex">
              <button
                *ngIf="!notificacion.leida"
                class="mr-2 text-xs text-indigo-600 dark:text-blue-300 hover:text-indigo-800 dark:hover:text-blue-200 font-medium"
                (click)="marcarComoLeida(notificacion, $event)"
              >
                Marcar como leída
              </button>

              <button
                *ngIf="notificacion.enlace"
                class="text-xs text-indigo-600 dark:text-blue-300 hover:text-indigo-800 dark:hover:text-blue-200 font-medium"
                (click)="manejarClicNotificacion(notificacion, $event)"
              >
                Ver detalles
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
