/***************************************************
 * SECCIÓN PRINCIPAL DEL FORM
 ***************************************************/
.form-section {
  max-width: 80%;
  margin: 3rem auto;
  padding: 0 1rem;
}

/***************************************************
 * CARDS
 ***************************************************/
.main-card {
  margin-bottom: 3rem; /* Separación entre tarjetas */
  position: relative; /* Para el icono flotante */
  border-radius: 20px; /* Borde redondeado en la tarjeta */
  overflow: visible;
  background-color: #fff;
  box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
  border: 0.0625rem solid #dce3ec;

  /* Ajustar padding interno */
  ::ng-deep .mat-card-content {
    padding: 2rem !important;
  }
}

/***************************************************
 * ÍCONO FLOTANTE (CONTENEDOR CUADRADO)
 ***************************************************/
.icon-container {
  position: absolute;
  top: -2rem;
  left: 0.935rem;
  width: 3.5rem;
  height: 3.5rem;

  background-color: #fff;
  border: 0.125rem solid #26afe5; /* Borde #006a66 */
  border-radius: 0.5rem; /* Esquinas redondeadas */
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.15);

  display: flex;
  justify-content: center;
  align-items: center;
}

.icono-flotante {
  font-size: 1.5rem;
  color: #26afe5; /* Color del ícono */
}

/***************************************************
 * TÍTULOS DE SECCIÓN
 ***************************************************/
.section-title {
  /* Si quieres que sean negro, pónselo aquí.
     Pero si no, #006a66 para distinguir.
     Cambiar a #000 si deseas título negro.
  */
  color: #2e74bb;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

/***************************************************
 * BORDE Y LABEL DE LOS CAMPOS (#006a66)
 ***************************************************/
/* 1) El contorno outline normal, SIEMPRE #006a66 */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #26afe5 !important;
  border-radius: 0.5rem;
  box-shadow: 0.5rem 0.5rem 0px 0px #f5f7fb;
  border-radius: 0.75rem;
}
/* 2) El "trazo grueso" (cuando enfoca) también #26AFE5 */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: #26afe5 !important;
  box-shadow: 0.5rem 0.5rem 0px 0px #f5f7fb;
  border-radius: 0.75rem;
}
/* 3) Etiquetas (label) en negro */
::ng-deep .mat-form-field-label {
  color: #333 !important;
  font-weight: 500;
  transform: translateY(0) scale(1) !important;
  transition: transform 0.2s ease-in-out !important;
}

/* Cuando el campo está enfocado o tiene valor, la etiqueta sube */
::ng-deep .mat-form-field.mat-focused .mat-form-field-label,
::ng-deep .mat-form-field.mat-form-field-should-float .mat-form-field-label {
  transform: translateY(-1.5em) scale(0.75) !important;
  color: #26afe5 !important;
  margin-top: -0.25rem !important;
}

/* Cuando hay error, la etiqueta cambia de color pero mantiene su posición */
::ng-deep .mat-form-field.mat-form-field-invalid .mat-form-field-label {
  color: #f44336 !important;
}

/* Cuando hay error y el campo tiene valor, la etiqueta sube */
::ng-deep
  .mat-form-field.mat-form-field-invalid.mat-form-field-should-float
  .mat-form-field-label {
  transform: translateY(-1.5em) scale(0.75) !important;
  margin-top: -0.25rem !important;
}

/* 4) Quitar cualquier hover/focus que cambie color a otro */
::ng-deep
  .mat-form-field-appearance-outline.mat-form-field-can-hover:hover
  .mat-form-field-outline-thick {
  color: #26afe5 !important;
}
.form-section mat-card {
  background-color: #fff !important;
}

::ng-deep .mat-input-element {
  caret-color: #000; /* Asegúrate de que el cursor sea visible */
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: #26afe5; /* Color del borde cuando no está enfocado */
}

::ng-deep
  .mat-form-field-appearance-outline.mat-focused
  .mat-form-field-outline {
  color: #26afe5; /* Color del borde cuando está enfocado */
}
/***************************************************
 * TEXTO INTERNO DE LOS INPUTS (placeholder/typed)
 ***************************************************/
/* Normalmente Angular Material deja el texto en negro,
   pero si necesitas forzar:
*/
::ng-deep .mat-input-element,
::ng-deep .mat-select-value-text {
  color: #000 !important; /* Texto real que escribe el usuario */
}

/***************************************************
 * QUITAR PADDINGS EXTRAS
 ***************************************************/
::ng-deep .mat-form-field-wrapper {
  padding: 0 !important;
}
::ng-deep .mat-form-field-subscript-wrapper {
  margin: 0;
}
::ng-deep .mat-form-field-flex {
  border-radius: 0.5rem;
}

/***************************************************
 * SEPARADOR (HR)
 ***************************************************/
.title-separator {
  border: none;
  border-top: 0.0625rem solid #ccc;
  margin-bottom: 1rem;
}

/***************************************************
 * GRID 3x3 Y GRID 2x2
 ***************************************************/
.grid-three-columns {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}
.grid-two-columns {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
}

/***************************************************
 * MÓVILES A PORTAR
 ***************************************************/
.moviles-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .movil-row {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  .movil-field {
    flex: 1;
  }
  .btn-add-movil {
    margin-top: 0.3rem;
    align-self: flex-start;
  }
}

/***************************************************
 * CHECKBOXES
 ***************************************************/
.checkboxes-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.2rem 0;
}

/***************************************************
 * DATEPICKER
 ***************************************************/
.datepicker-container {
  margin-top: 0.3rem;

  label {
    display: block;
    margin-bottom: 0.2rem;
    font-size: 0.85rem;
    font-weight: 500;
  }
}

/***************************************************
 * BOTÓN DE REGISTRAR
 ***************************************************/
.button-row {
  margin-top: 1rem;
  text-align: right;
}
.btn-registrar {
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 600;
}

/***************************************************
 * FOOTER / CERRAR SESIÓN
 ***************************************************/
.footer-section {
  max-width: 1200px;
  margin: 0.5rem auto;
  text-align: right;
}

/***************************************************
 * FOOTER / CERRAR SESIÓN
 ***************************************************/
.mat-icon {
  height: 1.5rem;
  width: 1.5rem;
}

.date-selector-field {
  width: 100%;

  ::ng-deep {
    .mat-form-field-flex {
      align-items: center;
      background-color: #fff;
      border-radius: 0.5rem;
      padding: 0.5rem;
    }

    .date-selector-container {
      display: flex;
      gap: 10px;
      margin-top: 1rem;

      mat-select {
        flex: 1;
        width: 100%;

        .mat-select-trigger {
          display: flex;
          align-items: center;
          height: 100%;
        }

        .mat-select-value {
          color: #000;
          font-weight: 500;
        }

        .mat-select-arrow {
          color: #26afe5;
        }
      }
    }

    .mat-form-field-wrapper {
      padding-bottom: 0;
    }

    .mat-form-field-infix {
      padding: 0.5rem 0;
      border-top: 0;
    }

    .mat-form-field-outline {
      height: 3.5rem;
      border-radius: 0.5rem;
    }
  }
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: nowrap; /* Evita que los elementos se envuelvan */
}

.numero-agente-field {
  width: 200px;
  margin-left: 1rem;
  flex-shrink: 0; /* Evita que el campo se encoja */
}

/* Media queries para responsividad */
@media screen and (max-width: 1024px) {
  .form-section {
    max-width: 98%;
    margin: 2rem auto;
    padding: 0 0.5rem;
  }

  .grid-three-columns {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem;
  }

  .grid-two-columns {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem;
  }

  .main-card {
    margin-bottom: 2rem;
  }

  .icon-container {
    top: -1.5rem;
    left: 0.75rem;
    width: 3rem;
    height: 3rem;
    z-index: 10;
  }

  .icono-flotante {
    font-size: 1.25rem;
  }

  ::ng-deep .mat-card-content {
    padding: 1.25rem !important;
    overflow: hidden;
  }

  .section-title {
    font-size: 1.1rem;
  }

  .header-row {
    flex-direction: row; /* Mantiene la dirección en fila */
    align-items: center;
  }

  .numero-agente-field {
    width: 180px; /* Ligeramente más pequeño en tablets */
    margin-left: 1rem;
  }

  ::ng-deep .mat-form-field {
    width: 100%;
    min-width: 0;
  }

  ::ng-deep .mat-form-field-infix {
    width: 100% !important;
    min-width: 0 !important;
  }

  ::ng-deep .mat-form-field-wrapper {
    margin: 0;
    padding: 0 !important;
  }
}

@media screen and (max-width: 768px) {
  .form-section {
    max-width: 100%;
    margin: 1rem auto;
    padding: 0 0.25rem;
  }

  .grid-three-columns,
  .grid-two-columns {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .main-card {
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: visible;
  }

  .icon-container {
    top: -0.5rem;
    left: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    z-index: 10;
  }

  .icono-flotante {
    font-size: 1.2rem;
  }

  ::ng-deep .mat-card-content {
    padding: 1.25rem !important;
  }

  .section-title {
    font-size: 1rem;
    flex: 1; /* Permite que el título se ajuste */
    min-width: 0; /* Permite que el texto se ajuste */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  ::ng-deep .mat-form-field {
    width: 100%;
  }

  ::ng-deep .mat-form-field-infix {
    min-width: 100%;
    width: 100%;
  }

  .moviles-container {
    .movil-field {
      width: 100%;
    }
  }

  .button-row {
    text-align: center;
  }

  .btn-registrar {
    width: 100%;
    max-width: 300px;
  }

  .numero-agente-field {
    width: 150px; /* Más pequeño en móviles */
    margin-left: 0.5rem;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    box-shadow: 0.25rem 0.25rem 0px 0px #f5f7fb;
  }
}

@media screen and (max-width: 480px) {
  .form-section {
    margin: 0.5rem auto;
    padding: 0 0.5rem;
  }

  .main-card {
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: visible;
  }

  .icon-container {
    top: -0.25rem;
    left: 0.5rem;
    width: 2.25rem;
    height: 2.25rem;
    z-index: 10;
  }

  .icono-flotante {
    font-size: 1.1rem;
  }

  ::ng-deep .mat-card-content {
    padding: 1rem !important;
  }

  .section-title {
    font-size: 0.9rem;
  }

  .title-separator {
    margin-bottom: 0.75rem;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    border-radius: 0.5rem;
  }

  ::ng-deep .mat-form-field-label {
    font-size: 0.875rem;
  }

  ::ng-deep .mat-input-element {
    font-size: 0.875rem !important;
  }

  /* Mejoras para los mensajes de error en pantallas pequeñas */
  ::ng-deep .mat-error {
    font-size: 0.7rem !important;
    line-height: 1.1 !important;
    margin-top: 0 !important;
    padding: 0 !important;
    white-space: normal !important;
    display: block !important;
    position: relative !important;
    visibility: visible !important;
    opacity: 1 !important;
    clear: both !important;
  }

  ::ng-deep .mat-hint {
    font-size: 0.7rem !important;
    line-height: 1.1 !important;
    margin-top: 0 !important;
  }

  ::ng-deep .mat-form-field-subscript-wrapper {
    margin-top: 0 !important;
    min-height: 1.5em !important;
    position: static !important;
    padding: 0 !important;
  }

  /* Asegura que los campos tengan suficiente espacio para los mensajes de error */
  ::ng-deep .mat-form-field {
    margin-bottom: 0.75rem !important;
  }

  /* Ajustes para las etiquetas en pantallas pequeñas */
  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
    font-size: 0.875rem !important;
  }

  .checkboxes-row {
    font-size: 0.875rem;
  }

  .datepicker-container {
    label {
      font-size: 0.75rem;
    }
  }

  .btn-add-movil {
    width: 100%;
    margin-top: 0.5rem;
  }

  .footer-section {
    text-align: center;
    padding: 0 0.5rem;
  }

  .footer-section button {
    width: 100%;
    max-width: 300px;
  }
}

/* Ajustes para el alineamiento vertical de las etiquetas */
::ng-deep .mat-form-field {
  .mat-form-field-wrapper {
    padding-bottom: 0;
    margin: 0;
  }

  .mat-form-field-flex {
    background-color: #fff;
    border-radius: 0.5rem;
    min-height: 48px;
    align-items: center;
  }

  .mat-form-field-infix {
    padding: 0.5rem 0;
    border-top: 0;
    width: 100% !important;
    min-width: 0 !important;
    display: flex;
    align-items: center;
  }

  .mat-form-field-label {
    transform: translateY(0) scale(1) !important;
    transition: transform 0.2s ease-in-out !important;
  }

  &.mat-focused .mat-form-field-label,
  &.mat-form-field-should-float .mat-form-field-label {
    transform: translateY(-1.28125em) scale(0.75) !important;
    color: #26afe5 !important;
  }

  /* Ajuste específico para campos con error */
  &.mat-form-field-invalid {
    /* Mejora para los mensajes de error */
    .mat-error {
      font-size: 0.75rem !important;
      line-height: 1.2 !important;
      white-space: normal !important; /* Permite que el texto se envuelva */
      display: block !important;
      margin-top: 0 !important;
      position: relative !important;
      visibility: visible !important;
      opacity: 1 !important;
      clear: both !important; /* Asegura que no haya elementos flotantes interfiriendo */
      padding-left: 0 !important;
    }

    /* Asegura que el contenedor del mensaje tenga suficiente espacio */
    .mat-form-field-subscript-wrapper {
      position: static !important;
      margin-top: 0 !important;
      padding-top: 0 !important;
      min-height: 1.5em !important;
    }
  }

  /* Ajuste para el espacio del error */
  .mat-form-field-subscript-wrapper {
    margin-top: 0.25em;
    min-height: 0;
  }

  /* Ajuste para el contenedor del input */
  .mat-form-field-infix {
    padding: 0.5rem 0;
    border-top: 0;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  /* Ajuste para el contenedor del label */
  .mat-form-field-label-wrapper {
    top: 0;
    padding-top: 0;
  }
}

/* Ajuste específico para campos con select */
::ng-deep .mat-select-trigger {
  min-height: 48px;
  display: flex;
  align-items: center;
}

/* Ajuste para el contenedor del input en campos con error */
::ng-deep .mat-form-field-invalid {
  .mat-form-field-infix {
    min-height: 48px;
  }
}

/* Ajuste para mantener consistencia en todos los campos */
::ng-deep .mat-form-field-appearance-outline {
  .mat-form-field-outline {
    height: 48px;
  }

  .mat-form-field-infix {
    padding: 0.5rem 0;
    border-top: 0;
  }

  /* Mejora para los mensajes de error */
  .mat-error {
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
    white-space: normal !important;
    display: block !important;
    margin-top: 0.25em !important;
    position: relative !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #f44336 !important; /* Asegura que el color del error sea visible */
  }

  /* Asegura que el contenedor del mensaje de error tenga suficiente espacio */
  .mat-form-field-subscript-wrapper {
    position: static !important;
    margin-top: 0.25em !important;
    min-height: 1.5em !important;
  }
}

/* Ajuste para el espacio del error y hint */
::ng-deep .mat-form-field-subscript-wrapper {
  margin-top: 0;
  min-height: 1.5em; /* Aumentado para dar más espacio a los mensajes de error */
  position: static; /* Asegura que el contenedor de error no se posicione de forma absoluta */
  padding: 0 1em; /* Añade padding horizontal para evitar que el texto toque los bordes */
}

/* Estilo para los hints */
::ng-deep .mat-hint {
  font-size: 0.75rem !important;
  line-height: 1.2 !important;
  display: block !important;
  margin-top: 0 !important;
  color: rgba(0, 0, 0, 0.6) !important;
  position: absolute !important;
  bottom: -1.5em !important;
}

/* Ajuste para mantener la consistencia en campos con iconos */
::ng-deep .mat-form-field-suffix {
  top: 0;
  bottom: 0;
  margin: auto;
  height: 100%;
  display: flex;
  align-items: center;
}

/* Mejoras en la accesibilidad */
::ng-deep .mat-form-field-label {
  color: rgba(0, 0, 0, 0.87) !important;
}

::ng-deep .mat-form-field.mat-focused {
  .mat-form-field-label {
    color: #26afe5 !important;
  }
}

/* Ajustes para el datepicker */
::ng-deep .mat-datepicker-toggle {
  color: #26afe5;
}

::ng-deep .mat-datepicker-content {
  .mat-calendar {
    width: 100%;
    max-width: 300px;
  }
}

/* Ajuste específico para los inputs de fecha */
::ng-deep input[matDatepicker] {
  margin-top: 0.5rem !important;
}

/* Mejoras en los botones */
.btn-registrar {
  min-height: 44px;
  padding: 0.75rem 1.5rem;
}

/* Ajustes para los checkboxes */
::ng-deep .mat-checkbox {
  .mat-checkbox-layout {
    margin-bottom: 0;
  }
  .mat-checkbox-label {
    line-height: 1;
    min-height: auto;
  }
}

/* Ajuste para contenedores de inputs */
.datos-cliente-content,
.datos-servicio-content,
.datos-informacion-content {
  width: 100%;
  overflow: hidden;
}

/* Ajuste específico para los textareas */
::ng-deep textarea.mat-input-element {
  margin-top: 0.5rem !important;
}

/* Estilos específicos para la tarjeta de información */
.datos-informacion {
  .datos-informacion-content {
    .info-section {
      margin-bottom: 0.5rem;

      /* Ajuste específico para los íconos en los campos */
      .mat-form-field {
        ::ng-deep .mat-form-field-suffix {
          position: relative;
          top: 0;
          bottom: 0;
          margin: auto;
          height: 100%;
          display: flex;
          align-items: center;

          .mat-icon {
            position: relative;
            top: 0;
            margin: auto;
            height: 24px;
            width: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
      margin-bottom: 0.75rem;
    }

    .observation-field {
      width: 100%;
      margin-bottom: 0.75rem;

      textarea {
        min-height: 60px;
        resize: vertical;
      }

      ::ng-deep .mat-form-field-suffix {
        position: relative;
        top: 0;
        bottom: 0;
        margin: auto;
        height: 100%;
        display: flex;
        align-items: center;

        .mat-icon {
          position: relative;
          top: 0;
          margin: auto;
          height: 24px;
          width: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #26afe5;
        }
      }
    }

    .subsection-title {
      color: #2e74bb;
      font-size: 1rem;
      font-weight: 600;
      margin: 1rem 0 0.5rem 0;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #e0e0e0;
      letter-spacing: 0.01em;
    }

    .checkbox-container {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-bottom: 1.5rem;
      margin-top: 0.5rem;

      .checkbox-row {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #eaeaea;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(46, 116, 187, 0.03);
        }

        &:last-child {
          border-bottom: none;
        }
      }

      .custom-checkbox {
        display: flex;
        align-items: center;
        margin: 0;
        width: 100%;

        ::ng-deep .mat-checkbox-layout {
          margin-bottom: 0;
          width: 100%;
        }

        ::ng-deep .mat-checkbox-inner-container {
          height: 18px;
          width: 18px;
          margin: 2px 12px 2px 2px;
        }

        ::ng-deep .mat-checkbox-background {
          background-color: #555555;
        }

        ::ng-deep .mat-checkbox-frame {
          border-width: 2px;
          border-color: #d0d0d0;
        }

        ::ng-deep .mat-checkbox-checkmark-path {
          stroke: #ffffff !important;
        }

        .checkbox-label {
          font-size: 0.95rem;
          color: #333;
          line-height: 1.3;
          font-weight: 400;
          letter-spacing: 0.01em;
        }
      }
    }

    .button-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 0.75rem;

      .btn-registrar {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1.25rem;
        height: 36px;
        min-height: 36px;

        .mat-icon {
          font-size: 1.1rem;
          height: 1.1rem;
          width: 1.1rem;
        }
      }
    }
  }
}

/* Media queries para la tarjeta de información */
@media screen and (max-width: 768px) {
  .datos-informacion {
    .datos-informacion-content {
      .info-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
      }

      .observation-field {
        margin-bottom: 0.5rem;
      }

      .subsection-title {
        font-size: 0.95rem;
        margin: 0.75rem 0 0.5rem 0;
        padding-bottom: 0.4rem;
      }

      .checkbox-container {
        gap: 0.75rem;
        margin-bottom: 1.25rem;

        .checkbox-row {
          padding: 0.4rem 0;
        }

        .custom-checkbox {
          ::ng-deep .mat-checkbox-inner-container {
            height: 16px;
            width: 16px;
            margin: 2px 10px 2px 2px;
          }

          .checkbox-label {
            font-size: 0.9rem;
            line-height: 1.25;
          }
        }
      }

      .button-container {
        margin-top: 0.5rem;
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .datos-informacion {
    .datos-informacion-content {
      .subsection-title {
        font-size: 0.9rem;
        margin: 0.35rem 0;
      }

      .observation-field textarea {
        min-height: 50px;
      }

      .subsection-title {
        font-size: 0.9rem;
        margin: 0.5rem 0 0.4rem 0;
        padding-bottom: 0.3rem;
      }

      .checkbox-container {
        gap: 0.5rem;
        margin-bottom: 1rem;

        .checkbox-row {
          padding: 0.35rem 0;
        }

        .custom-checkbox {
          ::ng-deep .mat-checkbox-inner-container {
            height: 15px;
            width: 15px;
            margin: 2px 8px 2px 2px;
          }

          .checkbox-label {
            font-size: 0.85rem;
            line-height: 1.2;
          }
        }
      }
    }
  }
}

/* Ajustes específicos para los íconos en los campos de información */
.datos-informacion {
  .datos-informacion-content {
    .info-section {
      .mat-form-field {
        ::ng-deep .mat-form-field-suffix {
          top: 0;
          display: flex;
          align-items: center;
          height: 100%;

          .mat-icon {
            margin: 0;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}

/* Estilos específicos para los selectores */
::ng-deep .mat-select-panel {
  background: white;
  border-radius: 8px !important;
  margin-top: 4px;

  .mat-option {
    height: 48px;
    line-height: 48px;
    padding: 0 16px;
    font-size: 14px;

    &:hover:not(.mat-option-disabled) {
      background: rgba(38, 175, 229, 0.04) !important;
    }

    &.mat-selected:not(.mat-option-multiple):not(.mat-option-disabled) {
      background: rgba(38, 175, 229, 0.12);

      &:hover {
        background: rgba(38, 175, 229, 0.16) !important;
      }
    }
  }
}

/* Ajustes adicionales para los campos de selección */
::ng-deep .mat-select-trigger {
  height: 48px !important;
  display: flex !important;
  align-items: center !important;
}

::ng-deep .mat-select-value-text {
  padding-top: 0 !important;
  margin-top: 0.25rem !important;
}

/* Estilos para el tema oscuro */
:host-context(.dark-theme) {
  .main-card {
    background-color: #0a1628 !important;
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  ::ng-deep .mat-form-field-flex {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  ::ng-deep input.mat-input-element {
    color: #ffffff !important;
    caret-color: #ffffff !important;
  }

  ::ng-deep textarea.mat-input-element {
    color: #ffffff !important;
    caret-color: #ffffff !important;
  }

  .section-title {
    color: #ffffff !important;
  }

  .mat-card-content {
    color: #ffffff !important;
  }

  ::ng-deep .mat-form-field-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: none !important;
  }

  ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: rgba(255, 255, 255, 0.5) !important;
  }

  ::ng-deep .mat-input-element {
    color: #ffffff !important;
  }

  ::ng-deep .mat-select-value-text {
    color: #ffffff !important;
  }

  ::ng-deep .mat-select-arrow {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .icon-container {
    background-color: #1e4976 !important;
  }

  .icono-flotante {
    color: #ffffff !important;
  }

  .datos-informacion {
    background-color: #0a1628 !important;
    color: #ffffff !important;
  }

  .datos-informacion-content .info-item .info-label {
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .datos-informacion-content .info-item .info-value {
    color: #ffffff !important;
  }

  .subsection-title {
    color: #26afe5 !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
  }

  .checkbox-container .checkbox-row {
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;

    &:hover {
      background-color: rgba(38, 175, 229, 0.05) !important;
    }
  }

  .checkbox-container .custom-checkbox .checkbox-label {
    color: #e0e0e0 !important;
  }

  .checkbox-container .custom-checkbox ::ng-deep .mat-checkbox-frame {
    border-color: rgba(255, 255, 255, 0.4) !important;
  }

  .checkbox-container .custom-checkbox ::ng-deep .mat-checkbox-background {
    background-color: #555555 !important;
  }

  .datepicker-container {
    background-color: #0a1628 !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
  }

  .datepicker-container label {
    color: #ffffff !important;
  }

  .mat-checkbox-label {
    color: #ffffff !important;
  }

  .mat-checkbox-frame {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  .mat-radio-button .mat-radio-label-content {
    color: #ffffff !important;
  }
}

::ng-deep .mat-select-arrow {
  color: #26afe5;
}

::ng-deep .mat-form-field.mat-focused {
  .mat-select-arrow {
    color: #26afe5;
  }
}

/* Ajustes para el campo de permanencia */
.mat-form-field {
  &.permanencia-field {
    .mat-select-value {
      font-weight: 500;
    }
  }
}

/* Ajustes para el datepicker de permanencia */
.datepicker-container {
  margin-top: 8px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;

  label {
    color: #495057;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .mat-datepicker-toggle {
    color: #26afe5;
  }
}

/* Ajuste adicional para el alineamiento del texto del select */
::ng-deep .mat-select-value {
  padding-top: 2px;
}

/* Estilos para evitar el autocompletado del navegador */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: inherit !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* Animación para el panel hover del mapa */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Animación para el indicador de tiempo */
@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 1s infinite;
}

.dark-theme input:-webkit-autofill,
.dark-theme input:-webkit-autofill:hover,
.dark-theme input:-webkit-autofill:focus,
.dark-theme input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px #0a1628 inset !important;
  -webkit-text-fill-color: white !important;
}

/* Ocultar el autocompletado del navegador */
input[autocomplete="new-password"] {
  background-position: 150% 50% !important;
}
.numero-agente-wrapper {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;

  .numero-agente-field {
    width: 200px;
    margin: 0;
  }
}
