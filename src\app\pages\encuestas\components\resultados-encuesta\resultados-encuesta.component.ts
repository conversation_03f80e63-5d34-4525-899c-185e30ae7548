import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EncuestaService } from '@app/services/encuesta.service';
import { RespuestaEncuestaUsuarioService } from '@app/services/respuesta-encuesta-usuario.service';
import { Encuesta } from '@app/models/backend/encuesta/encuesta.model';
import {
  PreguntaEncuesta,
  TipoPregunta,
} from '@app/models/backend/encuesta/pregunta-encuesta.model';

@Component({
  selector: 'app-resultados-encuesta',
  templateUrl: './resultados-encuesta.component.html',
  styleUrls: ['./resultados-encuesta.component.scss'],
})
export class ResultadosEncuestaComponent implements OnInit {
  encuestaId: number;
  encuesta: Encuesta | null = null;
  estadisticas: any;

  // Estado
  loading = false;
  error = false;

  // Enums
  tipoPregunta = TipoPregunta;

  constructor(
    private route: ActivatedRoute,
    private encuestaService: EncuestaService,
    private respuestaService: RespuestaEncuestaUsuarioService
  ) {}

  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    this.encuestaId = idParam ? +idParam : 0;
    this.loadEncuesta();
    this.loadEstadisticas();
  }

  loadEncuesta(): void {
    this.loading = true;
    this.encuestaService.getCompleta(this.encuestaId).subscribe({
      next: (response) => {
        this.encuesta = response.data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error al cargar encuesta:', error);
        this.loading = false;
        this.error = true;
      },
    });
  }

  loadEstadisticas(): void {
    this.respuestaService.getEstadisticas(this.encuestaId).subscribe({
      next: (response) => {
        this.estadisticas = response.data;
      },
      error: (error) => {
        console.error('Error al cargar estadísticas:', error);
      },
    });
  }

  getPorcentajeCompletadas(): number {
    if (!this.estadisticas || !this.estadisticas.totalRespuestas) return 0;
    return (
      (this.estadisticas.respuestasCompletadas /
        this.estadisticas.totalRespuestas) *
      100
    );
  }

  getPorcentajeOpcion(pregunta: PreguntaEncuesta, opcionId: number): number {
    if (!this.estadisticas || !this.estadisticas.preguntasEstadisticas)
      return 0;

    const preguntaEstadistica = this.estadisticas.preguntasEstadisticas.find(
      (p: any) => p.preguntaId === pregunta.id
    );
    if (!preguntaEstadistica || !preguntaEstadistica.opcionesEstadisticas)
      return 0;

    const opcionEstadistica = preguntaEstadistica.opcionesEstadisticas.find(
      (o: any) => o.opcionId === opcionId
    );
    if (!opcionEstadistica) return 0;

    return opcionEstadistica.porcentaje;
  }

  getCantidadOpcion(pregunta: PreguntaEncuesta, opcionId: number): number {
    if (!this.estadisticas || !this.estadisticas.preguntasEstadisticas)
      return 0;

    const preguntaEstadistica = this.estadisticas.preguntasEstadisticas.find(
      (p: any) => p.preguntaId === pregunta.id
    );
    if (!preguntaEstadistica || !preguntaEstadistica.opcionesEstadisticas)
      return 0;

    const opcionEstadistica = preguntaEstadistica.opcionesEstadisticas.find(
      (o: any) => o.opcionId === opcionId
    );
    if (!opcionEstadistica) return 0;

    return opcionEstadistica.cantidad;
  }

  getPromedioNumerico(pregunta: PreguntaEncuesta): number {
    if (!this.estadisticas || !this.estadisticas.preguntasEstadisticas)
      return 0;

    const preguntaEstadistica = this.estadisticas.preguntasEstadisticas.find(
      (p: any) => p.preguntaId === pregunta.id
    );
    if (!preguntaEstadistica) return 0;

    return preguntaEstadistica.promedioNumerico || 0;
  }

  getTotalRespuestasPregunta(pregunta: PreguntaEncuesta): number {
    if (!this.estadisticas || !this.estadisticas.preguntasEstadisticas)
      return 0;

    const preguntaEstadistica = this.estadisticas.preguntasEstadisticas.find(
      (p: any) => p.preguntaId === pregunta.id
    );
    if (!preguntaEstadistica) return 0;

    return preguntaEstadistica.totalRespuestas || 0;
  }
}
