package com.midas.crm.entity.DTO.encuesta;

import com.midas.crm.entity.Encuesta;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para crear una nueva encuesta
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EncuestaCreateDTO {
    
    @NotBlank(message = "El título es obligatorio")
    private String titulo;
    
    private String descripcion;
    
    private LocalDateTime fechaInicio;
    
    private LocalDateTime fechaFin;
    
    private Integer tiempoLimite;
    
    private Boolean esAnonima = false;
    
    private Boolean mostrarResultados = false;
    
    @NotNull(message = "El tipo de asignación es obligatorio")
    private Encuesta.TipoAsignacion tipoAsignacion = Encuesta.TipoAsignacion.TODOS;
    
    // ID de la sede (si tipoAsignacion = SEDE)
    private Long sedeId;
    
    // ID del coordinador (si tipoAsignacion = COORDINACION)
    private Long coordinadorId;
    
    // ID del usuario específico (si tipoAsignacion = PERSONAL)
    private Long usuarioId;
    
    // Lista de preguntas (opcional, se pueden agregar después)
    private List<PreguntaEncuestaCreateDTO> preguntas;
}
