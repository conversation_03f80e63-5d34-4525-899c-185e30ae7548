import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'example',
        loadChildren: () => import('./map-example/map-example.module').then(m => m.MapExampleModule),
        data: { animation: 'MapExamplePage' }
      },
      {
        path: 'tipificacion-leaflet',
        loadChildren: () => import('./mapa-tipificacion-leaflet/mapa-tipificacion.module').then(m => m.MapaTipificacionModule),
        data: { animation: 'MapaTipificacionLeafletPage' }
      },
      {
        path: 'tipificacion-mapbox',
        loadChildren: () => import('./mapa-tipificacion-mapbox/mapa-tipificacion-mapbox.module').then(m => m.MapaTipificacionMapboxModule),
        data: { animation: 'MapaTipificacionMapboxPage' }
      },
      {
        path: '',
        redirectTo: 'example',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MapasRoutingModule { }
