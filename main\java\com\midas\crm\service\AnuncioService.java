package com.midas.crm.service;

import com.midas.crm.entity.Anuncio;
import com.midas.crm.entity.DTO.anuncio.AnuncioDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioListDTO;
import com.midas.crm.entity.DTO.anuncio.AnuncioUpdateResponseDTO;
import com.midas.crm.entity.Sede;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface AnuncioService {
    Anuncio guardar(AnuncioDTO dto);

    @Transactional(readOnly = true)
    List<AnuncioListDTO> listarPaginado(int page, int size);

    @Transactional(readOnly = true)
    List<AnuncioListDTO> listarPaginadoPorSede(int page, int size, Long sedeId);

    Anuncio obtenerPorId(Long id);
    void eliminar(Long id);

    AnuncioUpdateResponseDTO actualizarParcial(Long id, AnuncioDTO dto);

    @Transactional(readOnly = true)
    Page<AnuncioListDTO> listarMasRecientesPaginado(int page, int size);

    @Transactional(readOnly = true)
    Page<AnuncioListDTO> listarMasRecientesPaginadoPorSede(int page, int size, Long sedeId);

    @Transactional(readOnly = true)
    Page<AnuncioListDTO> listarAnunciosActivosYVigentes(int page, int size);

    @Transactional(readOnly = true)
    Page<AnuncioListDTO> listarAnunciosActivosYVigentesPorSede(int page, int size, Long sedeId);

    @Transactional(readOnly = true)
    Page<AnuncioListDTO> listarTodosLosAnuncios(int page, int size);

    @Transactional(readOnly = true)
    Page<AnuncioListDTO> listarTodosLosAnunciosPorSede(int page, int size, Long sedeId);

    @Transactional
    AnuncioUpdateResponseDTO actualizarOrden(Long id, Integer orden);

    /**
     * Actualiza el estado de los anuncios expirados a INACTIVO
     * @return Número de anuncios actualizados
     */
    @Transactional
    int actualizarAnunciosExpirados();


    /**
     * Obtiene todas las sedes activas
     * @return Lista de sedes activas
     */
    @Transactional(readOnly = true)
    List<Sede> obtenerTodasLasSedes();
}
