package com.midas.crm.repository;

import com.midas.crm.entity.OpcionRespuestaEncuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OpcionRespuestaEncuestaRepository extends JpaRepository<OpcionRespuestaEncuesta, Long> {

    // Buscar opciones por pregunta ordenadas por orden
    List<OpcionRespuestaEncuesta> findByPreguntaOrderByOrdenAsc(PreguntaEncuesta pregunta);
    List<OpcionRespuestaEncuesta> findByPreguntaIdOrderByOrdenAsc(Long preguntaId);

    // Buscar opciones activas por pregunta
    List<OpcionRespuestaEncuesta> findByPreguntaAndEstadoOrderByOrdenAsc(PreguntaEncuesta pregunta, String estado);
    List<OpcionRespuestaEncuesta> findByPreguntaIdAndEstadoOrderByOrdenAsc(Long preguntaId, String estado);

    // Contar opciones por pregunta
    int countByPreguntaId(Long preguntaId);
}
