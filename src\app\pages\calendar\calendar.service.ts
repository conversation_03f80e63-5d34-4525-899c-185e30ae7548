import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of, Subject } from 'rxjs';
import { catchError, map, shareReplay, takeUntil } from 'rxjs/operators';
import { CalendarList } from '@app/models/backend/calendar/calendar.model';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';

// Interfaz temporal para mantener compatibilidad con el código existente
interface GlobalResponse<T = any> {
  status: number;
  message: string;
  data: T;
}

@Injectable({
  providedIn: 'root'
})
export class CalendarService implements OnDestroy {
  private baseUrl = environment.url + 'api/calendar';

  // Estado global de la respuesta
  private globalStateSubject = new BehaviorSubject<GlobalResponse>({ status: 0, message: '', data: null });
  public globalState$ = this.globalStateSubject.asObservable();

  // Lista de calendarios como observable
  private listSubject = new BehaviorSubject<CalendarList[]>([]);
  public listObserver$ = this.listSubject.asObservable();

  // Control para cancelar solicitudes en curso
  private cancelFilterDates$ = new Subject<void>();

  constructor(private http: HttpClient) {}

  ngOnDestroy(): void {
    // Cancelar cualquier solicitud pendiente
    this.cancelFilterDates$.next();
    this.cancelFilterDates$.complete();

    // Limpiar los subjects
    this.globalStateSubject.complete();
    this.listSubject.complete();
  }

  private updateGlobalState(response: GlobalResponse): void {
    this.globalStateSubject.next(response);
  }

  // Obtener todos los calendarios
  getAll(_forceRefresh: boolean = false): Observable<GlobalResponse<CalendarList[]>> {
    return this.http.get<any>(this.baseUrl).pipe(
      map(response => {
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        this.updateGlobalState(res);
        return res;
      }),
      catchError(error => {
        const err: GlobalResponse = { status: 0, message: error.message || 'Error al obtener calendarios', data: [] };
        this.updateGlobalState(err);
        return of(err);
      })
    );
  }

  // Obtener calendario por ID
  getById(id: number): Observable<GlobalResponse<CalendarList>> {
    return this.http.get<any>(`${this.baseUrl}/${id}`).pipe(
      map(response => {
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        this.updateGlobalState(res);
        return res;
      }),
      catchError(error => {
        const err: GlobalResponse = { status: 0, message: error.message || 'Error al obtener calendario', data: null };
        this.updateGlobalState(err);
        return of(err);
      })
    );
  }

  // Obtener calendarios por usuario
  getFilterByUser(userId: number): Observable<GlobalResponse<CalendarList[]>> {
    return this.http.get<any>(`${this.baseUrl}/user/${userId}`).pipe(
      map(response => {
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        this.updateGlobalState(res);
        return res;
      }),
      catchError(error => {
        const err: GlobalResponse = { status: 0, message: error.message || 'Error al obtener calendarios del usuario', data: [] };
        this.updateGlobalState(err);
        return of(err);
      })
    );
  }

  // Variable para almacenar la única solicitud activa
  private activeFilterRequest: Observable<GlobalResponse<CalendarList[]>> | null = null;

  // Clave para almacenar en caché las solicitudes
  private lastFilterKey: string = '';

  // Obtener calendarios por fechas - versión optimizada
  getFilterDates(filter: any): Observable<GlobalResponse<CalendarList[]>> {
    console.log('[CalendarService] Solicitud filter-dates:', filter);

    // Cancelar cualquier solicitud anterior
    this.cancelFilterDates$.next();

    // Validar filtros
    if (!filter || !filter.fechaInicio || !filter.fechaFinal) {
      console.log('[CalendarService] Filtro inválido:', filter);
      const err: GlobalResponse = { status: 0, message: 'Datos de filtro inválidos', data: [] };
      return of(err);
    }

    if (filter.userCreateId === undefined || filter.userCreateId === null) {
      filter.userCreateId = 0;
    }

    // Crear una clave única para este filtro
    const filterKey = `${filter.fechaInicio}-${filter.fechaFinal}-${filter.userCreateId}`;
    console.log('[CalendarService] Clave de filtro:', filterKey);

    // Si es la misma solicitud que la última y no se fuerza actualización, reutilizar la solicitud activa
    if (this.activeFilterRequest && filterKey === this.lastFilterKey && !filter.forceRefresh) {
      console.log('[CalendarService] Reutilizando solicitud existente para:', filterKey);
      return this.activeFilterRequest;
    }

    // Actualizar la clave del último filtro
    this.lastFilterKey = filterKey;
    console.log('[CalendarService] Nueva solicitud HTTP para:', filterKey);

    // Crear una nueva solicitud
    this.activeFilterRequest = this.http.post<any>(`${this.baseUrl}/filter-dates`, filter).pipe(
      // Cancelar esta solicitud si se emite en cancelFilterDates$
      takeUntil(this.cancelFilterDates$),
      map(response => {
        console.log('[CalendarService] Respuesta recibida para:', filterKey);
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        return res;
      }),
      catchError(error => {
        console.error('[CalendarService] Error en solicitud:', error);
        const err: GlobalResponse = { status: 0, message: error.message || 'Error en la solicitud', data: [] };
        return of(err);
      }),
      // Compartir la respuesta entre múltiples suscriptores
      shareReplay(1)
    );

    return this.activeFilterRequest;
  }

  // Registrar calendario
  register(data: any): Observable<GlobalResponse> {
    return this.http.post<any>(this.baseUrl, data).pipe(
      map(response => {
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        this.updateGlobalState(res);
        return res;
      }),
      catchError(error => {
        const err: GlobalResponse = { status: 0, message: error.message || 'Error al crear calendario', data: null };
        this.updateGlobalState(err);
        return of(err);
      })
    );
  }

  // Actualizar calendario
  update(data: any, id: number): Observable<GlobalResponse> {
    return this.http.put<any>(`${this.baseUrl}/${id}`, data).pipe(
      map(response => {
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        this.updateGlobalState(res);
        return res;
      }),
      catchError(error => {
        const err: GlobalResponse = { status: 0, message: error.message || 'Error al actualizar calendario', data: null };
        this.updateGlobalState(err);
        return of(err);
      })
    );
  }

  // Eliminar calendario
  delete(id: number): Observable<GlobalResponse> {
    return this.http.delete<any>(`${this.baseUrl}/${id}`).pipe(
      map(response => {
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        this.updateGlobalState(res);
        return res;
      }),
      catchError(error => {
        const err: GlobalResponse = { status: 0, message: error.message || 'Error al eliminar calendario', data: null };
        this.updateGlobalState(err);
        return of(err);
      })
    );
  }

  // Restaurar calendario
  restore(id: number): Observable<GlobalResponse> {
    return this.http.post<any>(`${this.baseUrl}/restore/${id}`, {}).pipe(
      map(response => {
        const res: GlobalResponse = response.rpta !== undefined
          ? { status: response.rpta, message: response.msg, data: response.data }
          : response;
        this.updateGlobalState(res);
        return res;
      }),
      catchError(error => {
        const err: GlobalResponse = { status: 0, message: error.message || 'Error al restaurar calendario', data: null };
        this.updateGlobalState(err);
        return of(err);
      })
    );
  }

  // Métodos del patrón observer
  changeArrayObserver(list: CalendarList[]) {
    this.listSubject.next(list);
  }

  addObjectObserver(item: CalendarList) {
    const currentList = this.listSubject.getValue();
    this.listSubject.next([...currentList, item]);
  }

  updateObjectObserver(item: CalendarList) {
    const currentList = this.listSubject.getValue();
    const index = currentList.findIndex(x => x.id === item.id);
    if (index !== -1) {
      currentList[index] = item;
      this.listSubject.next([...currentList]);
    }
  }

  removeObjectObserver(id: number) {
    const currentList = this.listSubject.getValue();
    const newList = currentList.filter(x => x.id !== id);
    this.listSubject.next(newList);
  }
}