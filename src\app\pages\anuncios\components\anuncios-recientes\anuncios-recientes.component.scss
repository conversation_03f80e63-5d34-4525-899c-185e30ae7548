@keyframes shine {
  to {
    background-position-x: -200%;
  }
}

.anuncios-recientes {
  margin: 24px 0;
  padding: 0 16px;

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }

  .anuncios-title {
    font-size: 22px;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;

    .sede-badge {
      font-size: 12px;
      font-weight: 500;
      background-color: #4caf50;
      color: white;
      padding: 3px 8px;
      border-radius: 12px;
      display: inline-flex;
      align-items: center;
      vertical-align: middle;

      i {
        margin-right: 4px;
      }

      &.admin-badge {
        background-color: #3897f0;
        color: white;

        i {
          color: white;
        }
      }
    }

    &:after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -8px;
      width: 40px;
      height: 3px;
      background: linear-gradient(90deg, #3897f0, #6dd5ed);
      border-radius: 2px;
    }
  }

  .search-container {
    display: flex;
    position: relative;
    max-width: 400px;
    width: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;

    @media (max-width: 768px) {
      max-width: 100%;
    }

    .search-input {
      width: 100%;
      padding: 12px 40px 12px 16px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      background-color: #f8fafc;
      transition: all 0.3s ease;

      &:focus {
        outline: none;
        border-color: #3897f0;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(56, 151, 240, 0.15);
      }
    }

    .search-button {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      background-color: transparent;
      border: none;
      cursor: pointer;
      color: #64748b;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      padding: 6px;
      border-radius: 50%;
      z-index: 2;

      &:hover {
        color: #3897f0;
        background-color: rgba(56, 151, 240, 0.1);
      }

      &.clear-button {
        right: 42px;
        color: #94a3b8;

        &:hover {
          color: #ef4444;
          background-color: rgba(239, 68, 68, 0.1);
        }
      }
    }
  }

  // Sistema de grid adaptativo mejorado
  .anuncios-grid {
    display: flex;
    gap: 24px;
    transition: all 0.4s ease-out;
    position: relative; /* Para posicionar el mensaje de depuración */

    // Modificadores según cantidad de anuncios
    &.single-item {
      height: auto;
      justify-content: center; /* Centrar el contenido */

      .single-anuncio-container {
        width: 50%; /* Ancho del 50% para que sea similar a los anuncios en pares */
        max-width: 500px; /* Limitar el ancho máximo */
        margin: 0 auto; /* Centrar el contenedor */

        @media (max-width: 992px) {
          width: 70%; /* Aumentar el ancho en pantallas medianas */
        }

        @media (max-width: 768px) {
          width: 80%; /* Aumentar el ancho en pantallas pequeñas */
        }

        @media (max-width: 576px) {
          width: 100%; /* Ancho completo en móviles */
        }

        .anuncio-card {
          min-height: 450px; /* Altura estándar para la tarjeta */

          @media (max-width: 1200px) {
            min-height: 420px;
          }

          @media (max-width: 992px) {
            min-height: 400px;
          }

          @media (max-width: 768px) {
            min-height: 380px;
          }

          @media (max-width: 576px) {
            min-height: 350px;
          }
        }
      }
    }

    &.two-items {
      height: auto;
      align-items: stretch; /* Asegura que ambas columnas tengan la misma altura */
      display: flex; /* Asegura que el contenido se alinee correctamente */
      flex-wrap: nowrap; /* Evita que las columnas se envuelvan */

      .feature-column {
        flex: 1; /* Ambas columnas tienen el mismo ancho */
        height: auto; /* Cambiamos a auto para que se ajuste al contenido */
      }
    }

    &.multi-items {
      height: auto; /* Cambiamos a auto para que se ajuste al contenido */
    }

    @media (max-width: 768px) {
      flex-direction: column;
      height: auto;
      gap: 20px;
    }

    .feature-column {
      flex: 1.3;

      .anuncio-card.feature {
        height: 100%;

        .card-overlay {
          height: 40%; /* Reducimos aún más la altura para el anuncio destacado */
        }

        &:hover .card-overlay {
          height: 50%; /* Al hacer hover, mostramos un poco más de información */
        }

        .card-description {
          display: block;
          margin-bottom: 10px;
          -webkit-line-clamp: 2;
          line-clamp: 2;

          @media (max-width: 992px) {
            -webkit-line-clamp: 2;
            line-clamp: 2;
          }
        }
      }
    }

    /* Estilos específicos para el caso de dos anuncios */
    &.two-items .feature-column {
      display: flex; /* Asegura que el contenido se alinee correctamente */
      align-items: stretch; /* Estira el contenido para llenar el espacio */
      margin-top: 0; /* Elimina cualquier margen superior */
      margin-bottom: 0; /* Elimina cualquier margen inferior */

      .anuncio-card {
        width: 100%; /* Asegura que la tarjeta ocupe todo el ancho disponible */
        height: 100%; /* Asegura que la tarjeta ocupe toda la altura disponible */
        margin: 0; /* Elimina cualquier margen */
        /* Altura fija para las tarjetas en el caso de dos anuncios */
        min-height: 500px;

        @media (max-width: 1200px) {
          min-height: 480px;
        }

        @media (max-width: 992px) {
          min-height: 450px;
        }

        @media (max-width: 768px) {
          min-height: 420px;
        }

        @media (max-width: 576px) {
          min-height: 380px;
        }
      }
    }

    .grid-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 24px;

      @media (max-width: 992px) {
        gap: 20px;
      }

      @media (max-width: 576px) {
        gap: 16px;
      }

      .grid-row {
        display: flex;
        gap: 24px;
        height: auto; /* Cambiamos a auto para que se ajuste al contenido */

        /* Asegurar que los anuncios alternen entre izquierda y derecha */
        &:nth-child(odd) {
          .anuncio-card:first-child {
            background-color: rgba(56, 151, 240, 0.03); /* Ligero tinte azul */
          }
        }

        &:nth-child(even) {
          .anuncio-card:last-child {
            background-color: rgba(56, 151, 240, 0.03); /* Ligero tinte azul */
          }
        }

        @media (max-width: 992px) {
          gap: 20px;
        }

        @media (max-width: 576px) {
          gap: 16px;
        }

        @media (max-width: 480px) {
          flex-direction: column;
          gap: 16px;
        }

        .anuncio-card {
          flex: 1;
          height: 100%;
        }

        &.third-anuncio-container, &.fifth-anuncio-container {
          justify-content: center;

          .third-anuncio, .fifth-anuncio {
            width: 50%;
            max-width: 50%;
            flex: 0 0 50%;
            margin: 0 auto;
            min-height: 450px; /* Asegurar la misma altura que los otros anuncios */

            @media (max-width: 992px) {
              min-height: 400px;
              width: 70%;
              max-width: 70%;
              flex: 0 0 70%;
            }

            @media (max-width: 768px) {
              min-height: 380px;
              width: 80%;
              max-width: 80%;
              flex: 0 0 80%;
            }

            @media (max-width: 576px) {
              width: 100%;
              max-width: 100%;
              flex: 0 0 100%;
              min-height: 350px;
            }
          }
        }
      }

      /* Estilos para los anuncios centrados */
      .third-anuncio-container, .fifth-anuncio-container {
        margin-top: 24px;

        @media (max-width: 992px) {
          margin-top: 20px;
        }

        @media (max-width: 576px) {
          margin-top: 16px;
        }
      }
    }
  }

  .anuncio-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1),
                box-shadow 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    height: 100%;
    cursor: pointer;
    transform-origin: center bottom;
    display: flex;
    flex-direction: column;
    /* Altura fija para las tarjetas */
    min-height: 450px;

    /* Estilos específicos para anuncios normales (no destacados) */
    &.normal {
      min-height: 450px;

      .card-overlay {
        height: 35%; /* Altura estándar para el overlay */
      }

      &:hover .card-overlay {
        height: 45%; /* Al hacer hover, mostramos un poco más de información */
      }

      .card-description {
        display: block;
        margin-bottom: 10px;
        -webkit-line-clamp: 2;
        line-clamp: 2;
      }
    }

    @media (max-width: 1200px) {
      min-height: 420px;
    }

    @media (max-width: 992px) {
      min-height: 400px;
    }

    @media (max-width: 768px) {
      min-height: 380px;
    }

    @media (max-width: 576px) {
      min-height: 350px;
    }

    &:hover {
      transform: translateY(-6px) scale(1.01);
      box-shadow: 0 16px 30px rgba(0, 0, 0, 0.12), 0 6px 10px rgba(56, 151, 240, 0.1);
      z-index: 1;

      .card-image {
        transform: scale(1.08);
      }

      .card-overlay {
        background: linear-gradient(to top,
                    rgba(0, 0, 0, 0.85) 0%,
                    rgba(0, 0, 0, 0.65) 30%,
                    rgba(0, 0, 0, 0.4) 60%,
                    rgba(0, 0, 0, 0.2) 80%,
                    rgba(0, 0, 0, 0.05) 100%);
        height: 55%; /* Al hacer hover, mostramos un poco más de información */

        .card-title {
          transform: translateY(-2px);
        }
      }
    }

    &:active {
      transform: translateY(-2px) scale(0.99);
      transition: transform 0.1s;
    }

    @media (max-width: 576px) {
      border-radius: 12px;
    }

    .card-info-container {
      padding: 16px 20px;
      background-color: #ffffff;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      z-index: 2;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .card-badge {
        display: inline-block;
        background: linear-gradient(135deg, #3897f0, #2a75bb);
        color: white;
        font-size: 11px;
        font-weight: 700;
        padding: 5px 10px;
        border-radius: 20px;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

        @media (max-width: 992px) {
          font-size: 10px;
          padding: 4px 8px;
          margin-bottom: 6px;
        }

        @media (max-width: 576px) {
          font-size: 9px;
          padding: 3px 7px;
          margin-bottom: 4px;
        }
      }

      .card-title {
        font-size: 20px;
        font-weight: 700;
        color: #2d3748;
        margin: 0 0 10px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: 0.7px;
        text-align: center;
        font-family: 'Roboto', sans-serif;
        text-transform: uppercase;

        @media (max-width: 992px) {
          font-size: 18px;
          margin: 0 0 8px 0;
        }

        @media (max-width: 576px) {
          font-size: 16px;
          margin: 0 0 6px 0;
        }
      }

      .card-description {
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 0;
        color: #4a5568;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;

        @media (max-width: 992px) {
          font-size: 13px;
          -webkit-line-clamp: 2;
          line-clamp: 2;
        }

        @media (max-width: 576px) {
          font-size: 12px;
          -webkit-line-clamp: 1;
          line-clamp: 1;
        }
      }
    }

    .card-image-container {
      position: relative;
      width: 100%;
      flex: 1;
      overflow: hidden;
      background-color: #f1f5f9;
      /* Altura fija para el contenedor de imágenes */
      height: 300px;
      min-height: 300px;

      @media (max-width: 1200px) {
        height: 280px;
        min-height: 280px;
      }

      @media (max-width: 992px) {
        height: 250px;
        min-height: 250px;
      }

      @media (max-width: 768px) {
        height: 220px;
        min-height: 220px;
      }

      @media (max-width: 576px) {
        height: 200px;
        min-height: 200px;
      }

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 40%;
        background: linear-gradient(to bottom,
                  rgba(0, 0, 0, 0.2) 0%,
                  transparent 100%);
        z-index: 1;
        opacity: 0.6;
      }
    }

    .image-placeholder {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
      background-size: 200% 100%;
      animation: 1.5s shine linear infinite;
      z-index: 0;
    }

    .card-image {
      position: relative;
      width: 100%;
      height: 100%;
      object-fit: cover; /* Mantiene la proporción y cubre el contenedor */
      object-position: center; /* Centra la imagen */
      transition: transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1), opacity 0.3s ease;
      z-index: 1;
      opacity: 0;
      /* Aseguramos que la imagen siempre tenga el mismo tamaño */
      display: block;

      &.loaded {
        opacity: 1;
      }
    }

    /* Aseguramos que las imágenes en el caso de dos anuncios tengan el mismo tamaño */
    &.two-items .feature-column .card-image {
      object-position: center; /* Centra la imagen */
      height: 100%;
      width: 100%;
    }

    /* Aseguramos que las imágenes en el caso de un solo anuncio tengan el mismo tamaño */
    &.single-item .feature-column .card-image {
      object-position: center; /* Centra la imagen */
      height: 100%;
      width: 100%;
    }

    .card-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16px; /* Reducimos el padding */
      background: linear-gradient(to top,
                  rgba(0, 0, 0, 0.7) 0%,
                  rgba(0, 0, 0, 0.5) 30%,
                  rgba(0, 0, 0, 0.3) 60%,
                  rgba(0, 0, 0, 0.1) 80%,
                  transparent 100%);
      color: white;
      transition: all 0.4s ease;
      height: 20%; /* Reducimos la altura del overlay */
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8); /* Sombra global para todo el texto */

      @media (max-width: 992px) {
        padding: 14px;
      }

      @media (max-width: 576px) {
        padding: 12px;
      }

      .card-meta {
        font-size: 12px;
        opacity: 0.9;
        font-weight: 500;
        display: flex;
        align-items: center;
        color: white;

        .time-ago {
          display: flex;
          align-items: center;

          &:before {
            content: '•';
            display: inline-block;
            margin-right: 5px;
            color: #3897f0;
            font-size: 14px;
          }
        }

        @media (max-width: 992px) {
          font-size: 11px;
        }

        @media (max-width: 576px) {
          font-size: 10px;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    min-height: 200px;
    width: 100%;
    padding: 16px;

    .header-skeleton {
      width: 100%;
      height: 40px;
      margin-bottom: 24px;
      background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
      background-size: 200% 100%;
      animation: 1.5s shine linear infinite;
      border-radius: 8px;
      opacity: 0.7;
    }

    .anuncios-grid-skeleton {
      width: 100%;
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      @media (max-width: 768px) {
        flex-direction: column;
      }

      .feature-skeleton {
        flex: 1.5;
        height: 400px;
        background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
        background-size: 200% 100%;
        animation: 1.5s shine linear infinite;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

        @media (max-width: 768px) {
          height: 300px;
        }
      }

      .grid-skeleton {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;

        .grid-row-skeleton {
          display: flex;
          gap: 20px;
          height: 190px;

          @media (max-width: 768px) {
            height: 150px;
          }

          @media (max-width: 576px) {
            flex-direction: column;
            height: auto;
          }

          .card-skeleton {
            flex: 1;
            height: 100%;
            background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
            background-size: 200% 100%;
            animation: 1.5s shine linear infinite;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

            @media (max-width: 576px) {
              height: 150px;
              margin-bottom: 16px;
            }
          }
        }
      }
    }
  }

  .no-anuncios {
    text-align: center;
    padding: 80px 0;
    color: #64748b;
    font-size: 16px;
    font-weight: 500;
  }

  .no-results-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    background-color: #f8fafc;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 20px 0;

    i {
      font-size: 32px;
      color: #94a3b8;
      margin-bottom: 16px;
    }

    p {
      color: #64748b;
      font-size: 16px;
      margin-bottom: 20px;
      line-height: 1.5;

      strong {
        color: #334155;
        font-weight: 600;
      }
    }

    .clear-search-btn {
      padding: 8px 16px;
      background-color: #3897f0;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: #2a75bb;
      }
    }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes shine {
    to {
      background-position-x: -200%;
    }
  }

  /* Estilos para la paginación */
  .custom-paginator {
    margin-top: 32px;
    margin-bottom: 16px;
    position: relative;
    z-index: 100;
    clear: both;
    width: 100%;
    padding: 15px 0;
    border-top: 3px solid #3897f0;
    background-color: rgba(56, 151, 240, 0.05);
    border-radius: 8px;

    // Estilos para el tema oscuro
    &.dark-theme-paginator {
      background-color: rgba(26, 32, 53, 0.7);
      border-top-color: #26AFE5;
    }

    // Hacer que los botones sean más grandes y visibles
    ::ng-deep .mat-mdc-paginator-container {
      justify-content: center;
      padding: 0 16px;

      .mat-mdc-paginator-range-label {
        margin: 0 24px;
        font-size: 16px;
        font-weight: 500;
        color: #3897f0;
      }

      .mat-mdc-paginator-navigation-first,
      .mat-mdc-paginator-navigation-previous,
      .mat-mdc-paginator-navigation-next,
      .mat-mdc-paginator-navigation-last {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin: 0 4px;

        &:hover {
          background-color: #f0f8ff;
        }

        .mat-mdc-paginator-icon {
          width: 24px;
          height: 24px;
          fill: #3897f0;
        }
      }
    }

    // Estilos para el tema oscuro
    &.dark-theme-paginator {
      ::ng-deep .mat-mdc-paginator-container {
        .mat-mdc-paginator-range-label {
          color: #26AFE5;
        }

        .mat-mdc-paginator-navigation-first,
        .mat-mdc-paginator-navigation-previous,
        .mat-mdc-paginator-navigation-next,
        .mat-mdc-paginator-navigation-last {
          background-color: #1a2035;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);

          &:hover {
            background-color: #1e2746;
          }

          .mat-mdc-paginator-icon {
            fill: #26AFE5;
          }
        }
      }
    }
  }

  /* Estilos para la paginación antigua (mantener por compatibilidad) */
  .pagination-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 32px;
    margin-bottom: 16px;
    position: relative;
    z-index: 100;
    clear: both;
    width: 100%;
    padding: 20px 0;
    border-top: 3px solid #3897f0;
    background-color: rgba(56, 151, 240, 0.05);

    .pagination-info {
      font-size: 16px;
      color: #3897f0;
      margin-bottom: 12px;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
    }

    .pagination {
      display: flex;
      align-items: center;
      gap: 8px;
      background-color: #f8fafc;
      border-radius: 12px;
      padding: 10px 15px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(56, 151, 240, 0.2);

      @media (max-width: 576px) {
        gap: 4px;
        padding: 6px 8px;
      }

      .pagination-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: none;
        background-color: #ffffff;
        color: #3897f0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        font-size: 16px;

        &:hover:not(:disabled) {
          background-color: #3897f0;
          color: #ffffff;
          box-shadow: 0 2px 5px rgba(56, 151, 240, 0.3);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        @media (max-width: 576px) {
          width: 32px;
          height: 32px;
          font-size: 12px;
        }
      }

      .pagination-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: none;
        background-color: #ffffff;
        color: #64748b;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 600;
        font-size: 16px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

        &:hover:not(.active) {
          background-color: #e2e8f0;
          color: #334155;
        }

        &.active {
          background-color: #3897f0;
          color: #ffffff;
          box-shadow: 0 2px 5px rgba(56, 151, 240, 0.3);
        }

        @media (max-width: 576px) {
          width: 32px;
          height: 32px;
          font-size: 12px;
        }
      }
    }
  }
}

/* Estilos para el tema oscuro */
:host-context(.dark-theme) {
  .anuncios-recientes {
    .header-container {
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .anuncios-title {
      color: #ffffff;

      .sede-badge.admin-badge {
        background-color: #1e4976;
        color: #ffffff;

        i {
          color: #ffffff;
        }
      }
    }

    .search-container {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      .search-input {
        background-color: rgba(30, 39, 70, 0.7);
        border-color: rgba(38, 175, 229, 0.3);
        color: #ffffff;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          border-color: #26AFE5;
          background-color: rgba(30, 39, 70, 0.9);
          box-shadow: 0 0 0 3px rgba(38, 175, 229, 0.3);
        }
      }

      .search-button {
        color: rgba(255, 255, 255, 0.7);

        &:hover {
          color: #26AFE5;
          background-color: rgba(38, 175, 229, 0.2);
        }

        &.clear-button {
          color: rgba(255, 255, 255, 0.5);

          &:hover {
            color: #ef4444;
            background-color: rgba(239, 68, 68, 0.2);
          }
        }
      }
    }

    .anuncios-grid {
      .anuncio-card {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        &:hover {
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
        }

        &.third-anuncio, &.fifth-anuncio {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

          &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
          }
        }

        .card-info-container {
          background-color: #1a2035;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

          .card-badge {
            background: linear-gradient(135deg, #1e4976, #26AFE5);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            color: #ffffff;
          }

          .card-title {
            color: #ffffff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }

          .card-description {
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }
        }

        .card-image-container {
          background-color: #1e2746;

          .image-placeholder {
            background: linear-gradient(110deg, #1a2035 8%, #1e2746 18%, #1a2035 33%);
            background-size: 200% 100%;
            animation: 1.5s shine linear infinite;
          }
        }

        .card-overlay {
          background: linear-gradient(to top,
                      rgba(0, 0, 0, 0.8) 0%,
                      rgba(0, 0, 0, 0.6) 30%,
                      rgba(0, 0, 0, 0.4) 60%,
                      rgba(0, 0, 0, 0.2) 80%,
                      transparent 100%);

          .card-meta {
            color: #ffffff;

            .time-ago {
              color: #ffffff;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);

              &:before {
                color: #26AFE5;
              }
            }
          }
        }
      }
    }

    .loading-container {
      .header-skeleton,
      .feature-skeleton,
      .card-skeleton {
        background: linear-gradient(110deg, #1a2035 8%, #1e2746 18%, #1a2035 33%);
      }

      p {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .no-anuncios {
      color: rgba(255, 255, 255, 0.7);
    }

    .no-results-message {
      background-color: #1a2035;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

      i {
        color: rgba(255, 255, 255, 0.5);
      }

      p {
        color: rgba(255, 255, 255, 0.7);

        strong {
          color: #ffffff;
        }
      }

      .clear-search-btn {
        background-color: #1e4976;

        &:hover {
          background-color: #26AFE5;
        }
      }
    }

    /* Estilos para la paginación en modo oscuro */
    .pagination-container {
      .pagination-info {
        color: rgba(255, 255, 255, 0.7);
      }

      .pagination {
        background-color: rgba(255, 255, 255, 0.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

        .pagination-button {
          background-color: #1a2035;
          color: rgba(255, 255, 255, 0.7);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

          &:hover:not(:disabled) {
            background-color: #26AFE5;
            color: #ffffff;
            box-shadow: 0 2px 5px rgba(38, 175, 229, 0.4);
          }
        }

        .pagination-number {
          background-color: #1a2035;
          color: rgba(255, 255, 255, 0.7);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

          &:hover:not(.active) {
            background-color: #1e2746;
            color: #ffffff;
          }

          &.active {
            background-color: #26AFE5;
            color: #ffffff;
            box-shadow: 0 2px 5px rgba(38, 175, 229, 0.4);
          }
        }
      }
    }
  }
}

/* Estilos para la paginación profesional */
.pagination-container {
  margin-top: 30px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .custom-paginator {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    background-color: white;

    // Estilos para los elementos internos del paginador
    ::ng-deep {
      .mat-mdc-paginator-container {
        min-height: 56px;
        padding: 0 16px;
        justify-content: space-between;

        // Selector de elementos por página
        .mat-mdc-paginator-page-size {
          margin-right: 16px;
          align-items: center;

          .mat-mdc-paginator-page-size-label {
            margin: 0 8px 0 0;
            color: #3897f0;
            font-weight: 500;
          }

          .mat-mdc-paginator-page-size-select {
            margin: 0;
            width: 70px;

            .mat-mdc-select-trigger {
              height: 36px;
            }

            .mat-mdc-select-value {
              color: #3897f0;
              font-weight: 500;
            }
          }
        }

        // Etiqueta de rango
        .mat-mdc-paginator-range-label {
          margin: 0 32px;
          font-weight: 500;
          color: #3897f0;
        }

        // Botones de navegación
        .mat-mdc-paginator-navigation-previous,
        .mat-mdc-paginator-navigation-next,
        .mat-mdc-paginator-navigation-first,
        .mat-mdc-paginator-navigation-last {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background-color: #f0f7ff;
          margin: 0 4px;

          &:hover {
            background-color: #e0f0ff;
          }

          .mat-mdc-paginator-icon {
            width: 24px;
            height: 24px;
            fill: #3897f0;
          }

          &:disabled .mat-mdc-paginator-icon {
            fill: #ccc;
          }
        }
      }
    }
  }
}

/* Estilos para la paginación en tema oscuro */
:host-context(.dark-theme) {
  .pagination-container {
    .custom-paginator {
      background-color: #1a2035;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

      ::ng-deep {
        .mat-mdc-paginator-container {
          // Selector de elementos por página
          .mat-mdc-paginator-page-size {
            .mat-mdc-paginator-page-size-label {
              color: #26AFE5;
            }

            .mat-mdc-paginator-page-size-select {
              .mat-mdc-select-value {
                color: #26AFE5;
              }
            }
          }

          // Etiqueta de rango
          .mat-mdc-paginator-range-label {
            color: #26AFE5;
          }

          // Botones de navegación
          .mat-mdc-paginator-navigation-previous,
          .mat-mdc-paginator-navigation-next,
          .mat-mdc-paginator-navigation-first,
          .mat-mdc-paginator-navigation-last {
            background-color: #1e2746;

            &:hover {
              background-color: #263256;
            }

            .mat-mdc-paginator-icon {
              fill: #26AFE5;
            }

            &:disabled .mat-mdc-paginator-icon {
              fill: #4a5568;
            }
          }
        }
      }
    }
  }
}