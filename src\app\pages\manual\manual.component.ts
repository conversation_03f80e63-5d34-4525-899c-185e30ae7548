import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BehaviorSubject, Subscription, debounceTime } from 'rxjs';
import { environment } from '../../../environments/environment.dev';
import {
  Manual,
  ManualList,
  ManualService,
  Pagination,
  PaginationResult,
} from '../../services/manual.service';
import { User } from '@app/models/backend/user';
import { GenericResponse } from '@app/models/backend/generic-response';
import { MatDialog } from '@angular/material/dialog';
import { ManualDialogComponent } from './manual-dialog/manual-dialog.component';
import Swal from 'sweetalert2';

// Servicios auxiliares
class SweetAlertService {
  showTopEnd({ title = '', message = '', type = 'success' }) {
    // Mostrar alerta
  }

  loadingUp(message: string = 'Procesando...') {
    // Mostrar carga
  }

  stop() {
    // Detener carga
  }

  showConfirmationAlert(message: string) {
    // Mostrar confirmación
    return Promise.resolve({ isConfirmed: true });
  }
}

class ApiErrorFormattingService {
  formatAsHtml(errors: any): string {
    return JSON.stringify(errors);
  }
}

class FormService {
  modelToFormGroupData(model: any): any {
    return model;
  }
}

@Component({
  selector: 'app-manual',
  templateUrl: './manual.component.html',
  styleUrls: ['./manual.component.scss'],
})
export class ManualComponent implements OnInit, OnDestroy {
  // Variables para la interfaz
  tableColumns = [
    { property: 'index', name: 'N°', type: 'number' },
    { property: 'nombre', name: 'Nombre', type: 'text', sortable: true },
    { property: 'tipoText', name: 'Tipo', type: 'text', sortable: true },
    {
      property: 'archivo',
      name: 'Archivo',
      type: 'link',
      icon: 'description',
      linkText: 'Ver archivo',
    },
    {
      property: 'isActive',
      name: 'Estado',
      type: 'badge',
      badgeClass: (row: any) => (row.isActive ? 'bg-success' : 'bg-danger'),
      badgeText: (row: any) => (row.isActive ? 'Activo' : 'Inactivo'),
    },
    {
      property: 'createdAt',
      name: 'Fecha creado',
      type: 'date',
      sortable: true,
      dateFormat: 'dd/MM/yyyy HH:mm',
    },
    {
      property: 'updatedAt',
      name: 'Fecha modificado',
      type: 'date',
      sortable: true,
      dateFormat: 'dd/MM/yyyy HH:mm',
    },
  ];

  dataUserSession: User | null = null;

  // bread crumb items
  breadCrumbItems: Array<{ label: string; active?: boolean }> = [];

  // Permisos
  allowAddNew: boolean = false;
  allowEditing: boolean = false;
  allowDelete: boolean = false;

  // Form
  isNewData: boolean = true;
  submitted: boolean = false;
  manualForm: FormGroup;

  // LOADING
  loadingData: boolean = false;

  // PAGINACIÓN
  countElements: number[] = [2, 5, 10, 25, 50, 100];
  pagination: BehaviorSubject<Pagination> = new BehaviorSubject<Pagination>({
    page: 1,
    perPage: 10,
    search: '',
    column: 'id',
    order: 'desc',
  });

  paginationResult: PaginationResult = new PaginationResult();

  // Archivos subidos
  uploadFiles: File[] = [];

  // Table data
  lists: ManualList[] = [];

  URL_FILES: string = environment.url + 'files/manual/';

  // Servicios auxiliares
  private _formService: FormService = new FormService();
  private _apiErrorFormattingService: ApiErrorFormattingService =
    new ApiErrorFormattingService();
  private _sweetAlertService: SweetAlertService = new SweetAlertService();

  private subscription: Subscription = new Subscription();

  constructor(
    private _manualService: ManualService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog
  ) {
    // Inicializar formulario
    this.manualForm = this.formBuilder.group({});
  }

  /**
   * Verifica los permisos del usuario basado en su rol
   */
  private checkUserPermissions(): void {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        this.dataUserSession = JSON.parse(userStr);

        // Verificar que el usuario tenga un ID válido
        if (!this.dataUserSession || !this.dataUserSession.id) {
          // Intentar obtener el ID de otra manera si es necesario
          if (this.dataUserSession && (this.dataUserSession as any).userId) {
            this.dataUserSession.id = (this.dataUserSession as any).userId;
          }
        }

        // Verificar el rol del usuario para establecer permisos
        if (this.dataUserSession && this.dataUserSession.role) {
          const userRole = this.dataUserSession.role.trim().toUpperCase();
          // Solo los usuarios con rol ADMIN pueden agregar, editar y eliminar
          this.allowAddNew = userRole === 'ADMIN';
          this.allowEditing = userRole === 'ADMIN';
          this.allowDelete = userRole === 'ADMIN';
        }
      } catch (error) {
        // Error al parsear el usuario
      }
    }
  }

  /**
   * Método para depurar la estructura de un objeto
   * @param obj Objeto a depurar
   * @param depth Profundidad máxima (para evitar ciclos infinitos)
   * @returns Descripción del objeto
   */
  private debugObject(obj: any, depth: number = 3): string {
    if (depth <= 0) return 'Máxima profundidad alcanzada';
    if (obj === null) return 'null';
    if (obj === undefined) return 'undefined';

    try {
      if (Array.isArray(obj)) {
        return `Array[${obj.length}]: ${
          obj.length > 0
            ? `[0]=${this.debugObject(obj[0], depth - 1)}${
                obj.length > 1 ? ', ...' : ''
              }`
            : 'vacío'
        }`;
      }

      if (typeof obj === 'object') {
        const keys = Object.keys(obj);
        return `Object{${keys.length}}: {${
          keys.length > 0
            ? keys
                .slice(0, 3)
                .map((k) => `${k}=${this.debugObject(obj[k], depth - 1)}`)
                .join(', ') + (keys.length > 3 ? ', ...' : '')
            : 'vacío'
        }}`;
      }

      return String(obj);
    } catch (error) {
      return `Error al depurar: ${error}`;
    }
  }

  ngOnInit(): void {
    this.initForm();
    // Verificar permisos del usuario
    this.checkUserPermissions();

    // NO cargar los manuales aquí, se cargarán a través de la suscripción
    // this.apiManualListPagination(); <-- Eliminamos esta llamada

    // Suscribirse a cambios en la paginación
    this.subscription.add(
      this.pagination
        .asObservable()
        .pipe(debounceTime(300)) // Evitar múltiples llamadas rápidas
        .subscribe(() => {
          this.apiManualListPagination();
        })
    );

    // Emitir el valor inicial para cargar los datos una sola vez
    // Esto disparará la suscripción de arriba
    this.pagination.next(this.pagination.getValue());
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  /**
   * ****************************************************************
   * OPERACIONES CON LA API
   * ****************************************************************
   */
  /**
   * Método para obtener todos los manuales (solo se usa en casos excepcionales)
   */
  public listDataApi() {
    // Si ya está cargando, no hacer nada para evitar múltiples llamadas
    if (this.loadingData) {
      return;
    }

    this.loadingData = true;
    this._sweetAlertService.loadingUp('Obteniendo todos los manuales');

    this._manualService.getAll().subscribe({
      next: (response: GenericResponse<ManualList[]>) => {
        this._sweetAlertService.stop();
        this.loadingData = false;

        if (response.rpta == 1) {
          // Mapear los datos al modelo ManualList y agregar índice
          if (response.data && Array.isArray(response.data)) {
            this.lists = response.data.map((item: any, index: number) => {
              // Asegurarse de que los nombres de propiedades coincidan con el modelo actualizado
              if (item.is_active !== undefined && item.isActive === undefined) {
                item.isActive = item.is_active;
              }
              if (
                item.created_at !== undefined &&
                item.createdAt === undefined
              ) {
                item.createdAt = item.created_at;
              }
              if (
                item.updated_at !== undefined &&
                item.updatedAt === undefined
              ) {
                item.updatedAt = item.updated_at;
              }
              if (item.tipo_text !== undefined && item.tipoText === undefined) {
                item.tipoText = item.tipo_text;
              }
              if (
                item.user_create_id !== undefined &&
                item.userCreateId === undefined
              ) {
                item.userCreateId = item.user_create_id;
              }

              const manual = ManualList.cast(item);
              manual.index = index + 1; // Agregar índice para mostrar en la columna N°
              return manual;
            });
          } else {
            this.lists = [];
          }

          // Si hay datos, actualizar la paginación
          if (this.lists && this.lists.length > 0) {
            this.paginationResult.data = this.lists;
            this.paginationResult.total = this.lists.length;
            this.paginationResult.currentPage = 1;
            this.paginationResult.lastPage = 1;
            this.paginationResult.perPage = this.lists.length;
          }
        }

        if (response.rpta == 0) {
          if (response.errors) {
            this._sweetAlertService.showTopEnd({
              type: 'error',
              title: response.errors?.message,
              message: response.errors?.error,
            });
          } else {
            this._sweetAlertService.showTopEnd({
              type: 'error',
              title: 'Error',
              message: response.msg || 'Error al obtener los manuales',
            });
          }
        }
      },
      error: (error: any) => {
        this._sweetAlertService.stop();
        this.loadingData = false;
        this._sweetAlertService.showTopEnd({
          type: 'error',
          title: 'Error',
          message: 'Error al obtener los manuales',
        });
      },
    });
  }

  private saveDataApi(data: Manual | FormData) {
    if (this.loadingData) return;

    this.loadingData = true;

    Swal.fire({
      title: 'Registrando...',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    this.subscription.add(
      this._manualService.register(data).subscribe({
        next: (response: GenericResponse<ManualList>) => {
          Swal.close();
          this.loadingData = false;

          if (response.rpta === 1 && response.data) {
            const manualData: ManualList = ManualList.cast(response.data);
            this._manualService.addObjectObserver(manualData);

            Swal.fire({
              icon: 'success',
              title: 'Éxito',
              text: 'Manual registrado correctamente',
              toast: true,
              position: 'top-end',
              timer: 3000,
              showConfirmButton: false,
            });

            this.apiManualListPagination(); // Refresca la lista
          }

          if (response.rpta === 0) {
            const message =
              response.message ||
              response.msg ||
              'Error al registrar el manual';
            const textErrors = response.errors
              ? this._apiErrorFormattingService.formatAsHtml(response.errors)
              : '';

            Swal.fire({
              icon: 'error',
              title: 'Error',
              html: textErrors || message,
              toast: true,
              position: 'top-end',
              timer: 4000,
              showConfirmButton: false,
            });
          }
        },
        error: () => {
          Swal.close();
          this.loadingData = false;
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Error al registrar el manual',
            toast: true,
            position: 'top-end',
            timer: 3000,
            showConfirmButton: false,
          });
        },
      })
    );
  }

  private updateDataApi(data: Manual | FormData, id: number) {
    if (this.loadingData) return;

    if (!id || isNaN(Number(id))) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'ID inválido para actualizar manual',
        toast: true,
        position: 'top-end',
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    const numericId = Number(id);
    console.log('Actualizando manual con ID:', numericId);

    // Si es FormData, verificar si contiene un archivo nuevo
    if (data instanceof FormData) {
      const hasNewFile = data.has('file');
      console.log('¿Contiene un archivo nuevo?', hasNewFile);

      // Verificar si contiene el campo 'archivo'
      // Usamos un enfoque compatible con todos los navegadores
      let hasArchivoField = false;
      data.forEach((_, key) => {
        if (key === 'archivo') {
          hasArchivoField = true;
        }
      });
      console.log('¿Contiene campo archivo?', hasArchivoField);

      if (!hasNewFile && !hasArchivoField) {
        console.log(
          'No se está enviando un nuevo archivo ni campo archivo, se mantendrá el archivo existente'
        );
      }
    }

    this.loadingData = true;
    Swal.fire({
      title: 'Actualizando...',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    this._manualService.update(data, numericId).subscribe({
      next: (response: GenericResponse<ManualList>) => {
        Swal.close();
        this.loadingData = false;

        if (response.rpta === 1 && response.data) {
          const manualData: ManualList = ManualList.cast(response.data);
          this._manualService.updateObjectObserver(manualData);

          Swal.fire({
            icon: 'success',
            title: 'Éxito',
            text: 'Manual actualizado correctamente',
            toast: true,
            position: 'top-end',
            timer: 3000,
            showConfirmButton: false,
          });

          this.apiManualListPagination(); // Actualiza la lista
        }

        if (response.rpta === 0) {
          const message =
            response.message || response.msg || 'Error al actualizar el manual';
          const textErrors = response.errors
            ? this._apiErrorFormattingService.formatAsHtml(response.errors)
            : '';

          Swal.fire({
            icon: 'error',
            title: 'Error',
            html: textErrors || message,
            toast: true,
            position: 'top-end',
            timer: 4000,
            showConfirmButton: false,
          });
        }
      },
      error: (error) => {
        Swal.close();
        this.loadingData = false;
        console.error('Error al actualizar manual:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Error al actualizar el manual',
          toast: true,
          position: 'top-end',
          timer: 3000,
          showConfirmButton: false,
        });
      },
    });
  }

  private deleteDataApi(id: number) {
    // Si ya está cargando, no hacer nada para evitar múltiples llamadas
    if (this.loadingData) {
      return;
    }

    this.loadingData = true;
    this._sweetAlertService.loadingUp();

    this._manualService.delete(id).subscribe({
      next: (response: GenericResponse<ManualList>) => {
        this._sweetAlertService.stop();
        this.loadingData = false;

        if (response.rpta == 1) {
          const data: ManualList = ManualList.cast(response.data);
          this._manualService.removeObjectObserver(data.id);
          Swal.fire({
            icon: 'success',
            title: 'Éxito',
            text: 'Manual eliminado correctamente',
            toast: true,
            position: 'top-end',
            timer: 3000,
            showConfirmButton: false,
          });
          // Refrescar la lista de manuales
          this.apiManualListPagination();
        }

        if (response.rpta === 0) {
          if (response.errors) {
            const textErrors = this._apiErrorFormattingService.formatAsHtml(
              response.errors
            );

            Swal.fire({
              icon: 'error',
              title: response.message || response.msg || 'Error',
              html: textErrors,
              toast: true,
              position: 'top-end',
              timer: 5000,
              showConfirmButton: false,
            });
          } else {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: response.msg || 'Error al eliminar el manual',
              toast: true,
              position: 'top-end',
              timer: 4000,
              showConfirmButton: false,
            });
          }
        }
      },
      error: () => {
        this._sweetAlertService.stop();
        this.loadingData = false;
        this._sweetAlertService.showTopEnd({
          type: 'error',
          title: 'Error',
          message: 'Error al eliminar el manual',
        });
      },
    });
  }

  /**
   * Convierte un array de fecha [año, mes, día, hora, minuto, segundo, nanosegundo] a un objeto Date
   * @param dateArray Array de fecha
   * @returns Objeto Date
   */
  private convertArrayToDate(dateArray: any[]): Date | null {
    if (!dateArray || !Array.isArray(dateArray)) {
      console.warn('convertArrayToDate: El valor no es un array', dateArray);
      return null;
    }

    if (dateArray.length < 3) {
      console.warn(
        'convertArrayToDate: El array no tiene suficientes elementos',
        dateArray
      );
      return null;
    }

    try {
      // Extraer componentes de la fecha
      const year = dateArray[0];
      const month = dateArray[1] - 1; // En JavaScript los meses van de 0 a 11
      const day = dateArray[2];
      const hour = dateArray.length > 3 ? dateArray[3] : 0;
      const minute = dateArray.length > 4 ? dateArray[4] : 0;
      const second = dateArray.length > 5 ? dateArray[5] : 0;

      // Si hay nanosegundos, convertirlos a milisegundos
      let millisecond = 0;
      if (dateArray.length > 6 && dateArray[6]) {
        // Convertir nanosegundos a milisegundos (dividir por 1,000,000)
        millisecond = Math.floor(dateArray[6] / 1000000);
      }

      const date = new Date(
        year,
        month,
        day,
        hour,
        minute,
        second,
        millisecond
      );

      // Verificar si la fecha es válida
      if (isNaN(date.getTime())) {
        console.warn(
          'convertArrayToDate: La fecha resultante no es válida',
          dateArray
        );
        return null;
      }

      return date;
    } catch (error) {
      console.error(
        'convertArrayToDate: Error al convertir array a fecha',
        error,
        dateArray
      );
      return null;
    }
  }

  public apiManualListPagination() {
    // Si ya está cargando, no hacer nada para evitar múltiples llamadas
    if (this.loadingData) {
      return;
    }

    this.loadingData = true;
    console.log('Cargando manuales paginados...');

    this.subscription.add(
      this._manualService.getPagination(this.pagination.getValue()).subscribe({
        next: (response: GenericResponse<any>) => {
          this.loadingData = false;
          console.log('Respuesta de la API:', response);
          console.log(
            'Estructura de la respuesta:',
            this.debugObject(response)
          );

          if (response.rpta == 1) {
            // Inicializar el resultado de paginación
            this.paginationResult = new PaginationResult();

            // Verificar si los datos están en content (formato Spring Boot)
            if (
              response.data &&
              response.data.content &&
              Array.isArray(response.data.content)
            ) {
              console.log('Datos encontrados en response.data.content');
              console.log(
                'Contenido de response.data.content:',
                response.data.content
              );
              console.log(
                'Estructura de content:',
                this.debugObject(response.data.content)
              );

              // Mapear los datos al modelo ManualList y agregar índice
              this.lists = response.data.content.map(
                (item: any, index: number) => {
                  console.log(`Procesando item ${index}:`, item);

                  // Procesar fechas en formato array
                  if (item.createdAt && Array.isArray(item.createdAt)) {
                    const date = this.convertArrayToDate(item.createdAt);
                    if (date) {
                      item.createdAt = date;
                    }
                  }

                  if (item.updatedAt && Array.isArray(item.updatedAt)) {
                    const date = this.convertArrayToDate(item.updatedAt);
                    if (date) {
                      item.updatedAt = date;
                    }
                  }

                  if (item.deletedAt && Array.isArray(item.deletedAt)) {
                    const date = this.convertArrayToDate(item.deletedAt);
                    if (date) {
                      item.deletedAt = date;
                    }
                  }

                  // Asegurarse de que los nombres de propiedades coincidan con el modelo actualizado
                  if (
                    item.is_active !== undefined &&
                    item.isActive === undefined
                  ) {
                    item.isActive = item.is_active;
                  }
                  if (
                    item.created_at !== undefined &&
                    item.createdAt === undefined
                  ) {
                    item.createdAt = item.created_at;
                  }
                  if (
                    item.updated_at !== undefined &&
                    item.updatedAt === undefined
                  ) {
                    item.updatedAt = item.updated_at;
                  }
                  if (
                    item.tipo_text !== undefined &&
                    item.tipoText === undefined
                  ) {
                    item.tipoText = item.tipo_text;
                  }
                  if (
                    item.user_create_id !== undefined &&
                    item.userCreateId === undefined
                  ) {
                    item.userCreateId = item.user_create_id;
                  }

                  // Asegurarse de que tipoText se genere correctamente si no viene en los datos
                  if (item.tipo && !item.tipoText) {
                    item.tipoText = ManualList.getTipoText(item.tipo);
                  }

                  const manual = ManualList.cast(item);
                  manual.index = index + 1; // Agregar índice para mostrar en la columna N°
                  return manual;
                }
              );

              // Actualizar la paginación con los datos de Spring Boot
              this.paginationResult.data = this.lists;
              this.paginationResult.total =
                response.data.totalElements || this.lists.length;
              this.paginationResult.currentPage =
                response.data.number !== undefined
                  ? response.data.number + 1
                  : 1;
              this.paginationResult.lastPage = response.data.totalPages || 1;
              this.paginationResult.perPage =
                response.data.size || this.pagination.getValue().perPage;

              console.log('Manuales procesados:', this.lists);
              console.log('Paginación actualizada:', this.paginationResult);
            }
            // Verificar si los datos están en data.data (formato anterior)
            else if (
              response.data &&
              response.data.data &&
              Array.isArray(response.data.data)
            ) {
              console.log('Datos encontrados en response.data.data');
              console.log(
                'Contenido de response.data.data:',
                response.data.data
              );

              this.lists = response.data.data.map(
                (item: any, index: number) => {
                  console.log(`Procesando item ${index}:`, item);

                  // Asegurarse de que los nombres de propiedades coincidan con el modelo actualizado
                  if (
                    item.is_active !== undefined &&
                    item.isActive === undefined
                  ) {
                    item.isActive = item.is_active;
                  }
                  if (
                    item.created_at !== undefined &&
                    item.createdAt === undefined
                  ) {
                    item.createdAt = item.created_at;
                  }
                  if (
                    item.updated_at !== undefined &&
                    item.updatedAt === undefined
                  ) {
                    item.updatedAt = item.updated_at;
                  }
                  if (
                    item.tipo_text !== undefined &&
                    item.tipoText === undefined
                  ) {
                    item.tipoText = item.tipo_text;
                  }
                  if (
                    item.user_create_id !== undefined &&
                    item.userCreateId === undefined
                  ) {
                    item.userCreateId = item.user_create_id;
                  }

                  // Asegurarse de que tipoText se genere correctamente si no viene en los datos
                  if (item.tipo && !item.tipoText) {
                    item.tipoText = ManualList.getTipoText(item.tipo);
                  }

                  const manual = ManualList.cast(item);
                  manual.index = index + 1; // Agregar índice para mostrar en la columna N°
                  return manual;
                }
              );

              this.paginationResult = PaginationResult.cast(response.data);
            }
            // Verificar si los datos están directamente en response.data (otro formato posible)
            else if (response.data && Array.isArray(response.data)) {
              console.log('Datos encontrados directamente en response.data');
              console.log('Contenido de response.data:', response.data);

              this.lists = response.data.map((item: any, index: number) => {
                console.log(`Procesando item ${index}:`, item);

                // Procesar fechas en formato array
                if (item.createdAt && Array.isArray(item.createdAt)) {
                  const date = this.convertArrayToDate(item.createdAt);
                  if (date) {
                    item.createdAt = date;
                  }
                }

                if (item.updatedAt && Array.isArray(item.updatedAt)) {
                  const date = this.convertArrayToDate(item.updatedAt);
                  if (date) {
                    item.updatedAt = date;
                  }
                }

                if (item.deletedAt && Array.isArray(item.deletedAt)) {
                  const date = this.convertArrayToDate(item.deletedAt);
                  if (date) {
                    item.deletedAt = date;
                  }
                }

                // Asegurarse de que los nombres de propiedades coincidan con el modelo actualizado
                if (
                  item.is_active !== undefined &&
                  item.isActive === undefined
                ) {
                  item.isActive = item.is_active;
                }
                if (
                  item.created_at !== undefined &&
                  item.createdAt === undefined
                ) {
                  item.createdAt = item.created_at;
                }
                if (
                  item.updated_at !== undefined &&
                  item.updatedAt === undefined
                ) {
                  item.updatedAt = item.updated_at;
                }
                if (
                  item.tipo_text !== undefined &&
                  item.tipoText === undefined
                ) {
                  item.tipoText = item.tipo_text;
                }
                if (
                  item.user_create_id !== undefined &&
                  item.userCreateId === undefined
                ) {
                  item.userCreateId = item.user_create_id;
                }

                // Asegurarse de que tipoText se genere correctamente si no viene en los datos
                if (item.tipo && !item.tipoText) {
                  item.tipoText = ManualList.getTipoText(item.tipo);
                }

                const manual = ManualList.cast(item);
                manual.index = index + 1; // Agregar índice para mostrar en la columna N°
                return manual;
              });

              // Crear una paginación básica
              this.paginationResult.data = this.lists;
              this.paginationResult.total = this.lists.length;
              this.paginationResult.currentPage = 1;
              this.paginationResult.lastPage = 1;
              this.paginationResult.perPage = this.lists.length;

              console.log('Manuales procesados:', this.lists);
              console.log('Paginación actualizada:', this.paginationResult);
            }
            // Si no se encuentra en ninguno de los formatos esperados
            else {
              console.warn(
                'No se encontraron datos en el formato esperado:',
                response.data
              );

              // Intentar extraer datos de cualquier estructura
              let extractedData: any[] = [];

              if (response.data) {
                // Buscar cualquier propiedad que pueda contener un array
                Object.keys(response.data).forEach((key) => {
                  if (Array.isArray(response.data[key])) {
                    console.log(
                      `Encontrado array en response.data.${key}:`,
                      response.data[key]
                    );
                    extractedData = response.data[key];
                  }
                });
              }

              if (extractedData.length > 0) {
                console.log(
                  'Datos extraídos de estructura desconocida:',
                  extractedData
                );

                this.lists = extractedData.map((item: any, index: number) => {
                  // Procesar el item como en los casos anteriores
                  if (
                    item.is_active !== undefined &&
                    item.isActive === undefined
                  ) {
                    item.isActive = item.is_active;
                  }
                  // ... (resto de mapeos)

                  const manual = ManualList.cast(item);
                  manual.index = index + 1;
                  return manual;
                });

                // Actualizar paginación
                this.paginationResult.data = this.lists;
                this.paginationResult.total = this.lists.length;
                this.paginationResult.currentPage = 1;
                this.paginationResult.lastPage = 1;
                this.paginationResult.perPage = this.lists.length;
              } else {
                // Si realmente no hay datos, inicializar con arrays vacíos
                this.lists = [];
                this.paginationResult.data = [];
                this.paginationResult.total = 0;

                // Mostrar mensaje informativo al usuario
                this._sweetAlertService.showTopEnd({
                  type: 'info',
                  title: 'Sin datos',
                  message:
                    'No se encontraron manuales. Puedes agregar uno nuevo usando el botón "Agregar manual".',
                });
              }
            }
          } else {
            console.error('Error en la respuesta de la API:', response.msg);
            this._sweetAlertService.showTopEnd({
              type: 'error',
              title: 'Error',
              message: response.msg || 'Error al obtener los manuales',
            });
          }
        },
        error: (error: any) => {
          this.loadingData = false;
          console.error('Error al obtener los manuales:', error);
          this._sweetAlertService.showTopEnd({
            type: 'error',
            title: 'Error',
            message: 'Error al obtener los manuales',
          });
        },
      })
    );
  }

  /**
   * ****************************************************************
   * OPERACIONES CON EL FORMULARIO
   * ****************************************************************
   */

  /**
   * Inicializa el formulario
   */
  private initForm() {
    const manual = new Manual();
    const formGroupData = this.getFormGroupData(manual);
    this.manualForm = this.formBuilder.group(formGroupData);
  }

  /**
   * Obtiene los datos para el formulario
   * @param model
   * @returns
   */
  private getFormGroupData(model: Manual): object {
    return {
      ...this._formService.modelToFormGroupData(model),
      nombre: ['', [Validators.required, Validators.maxLength(50)]],
      tipo: ['', [Validators.required]],
      file: [null, []],
    };
  }

  /**
   * Obtiene los controles del formulario
   */
  get form() {
    return this.manualForm.controls;
  }

  /**
   * Abre el modal de creación de manual usando MatDialog
   */
  openModal() {
    // Verificar permisos primero
    this.checkUserPermissions();

    // Solo permitir agregar si el usuario tiene permisos
    if (!this.allowAddNew) {
      this._sweetAlertService.showTopEnd({
        type: 'error',
        title: 'Acceso denegado',
        message: 'No tienes permisos para agregar manuales',
      });
      return;
    }

    // Verificar que tengamos el usuario cargado
    if (!this.dataUserSession || !this.dataUserSession.id) {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          this.dataUserSession = JSON.parse(userStr);
          if (
            this.dataUserSession &&
            !this.dataUserSession.id &&
            (this.dataUserSession as any).userId
          ) {
            this.dataUserSession.id = (this.dataUserSession as any).userId;
          }
        } catch (error) {
          // Error al cargar usuario
        }
      }
    }

    const dialogRef = this.dialog.open(ManualDialogComponent, {
      width: '550px',
      maxWidth: '95vw',
      disableClose: false,
      maxHeight: '90vh',
      panelClass: ['responsive-dialog', 'modern-modal'],
      data: {
        URL_FILES: this.URL_FILES,
        dataUserSession: this.dataUserSession,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result.action === 'create') {
          this.saveDataApi(result.data);
        }
      }
    });
  }

  /**
   * Obtiene los datos para editar y abre el diálogo de edición
   * @param id id del registro a editar
   */
  editDataGet(id: any) {
    console.log('entro a editdataget');
    // Verificar permisos primero
    this.checkUserPermissions();

    // Solo permitir editar si el usuario tiene permisos
    if (!this.allowEditing) {
      Swal.fire({
        icon: 'error',
        title: 'Acceso denegado',
        text: 'No tienes permisos para editar manuales',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });
      return;
    }

    console.log('Editando manual con ID:', id);
    // Cargando datos al formulario
    var data = this.lists?.find((data: { id: any }) => data.id === id);
    if (data) {
      const manual = Manual.cast(data);
      console.log('Manual encontrado:', manual);
      console.log('ID real del manual en la base de datos:', manual.id);
      console.log('Índice en la tabla:', data.index);
      console.log('Archivo del manual:', manual.archivo);

      // Verificar que el manual tenga la URL del archivo
      if (!manual.archivo && data.archivo) {
        manual.archivo = data.archivo;
        console.log('Asignando archivo desde data:', data.archivo);
      }

      // Verificar si la URL del archivo es correcta
      if (manual.archivo) {
        // Si la URL ya contiene la URL base, asegurarse de que no se duplique
        if (manual.archivo.startsWith('http') && this.URL_FILES) {
          // Si la URL del archivo ya incluye la URL base, eliminarla
          if (manual.archivo.startsWith(this.URL_FILES)) {
            manual.archivo = manual.archivo.substring(this.URL_FILES.length);
            console.log(
              'URL del archivo corregida (eliminando URL base):',
              manual.archivo
            );
          } else {
            // Si es una URL completa pero no incluye la URL base (por ejemplo, Firebase),
            // mantenerla como está
            console.log(
              'URL del archivo es una URL completa externa:',
              manual.archivo
            );
          }
        }
      }

      // Verificar que tengamos el usuario cargado
      if (!this.dataUserSession || !this.dataUserSession.id) {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          try {
            this.dataUserSession = JSON.parse(userStr);
            if (
              this.dataUserSession &&
              !this.dataUserSession.id &&
              (this.dataUserSession as any).userId
            ) {
              this.dataUserSession.id = (this.dataUserSession as any).userId;
            }
          } catch (error) {
            // Error al cargar usuario
          }
        }
      }

      const dialogRef = this.dialog.open(ManualDialogComponent, {
        width: '550px',
        maxWidth: '95vw',
        disableClose: false,
        maxHeight: '90vh',
        panelClass: ['responsive-dialog', 'modern-modal'],
        data: {
          URL_FILES: this.URL_FILES,
          dataUserSession: this.dataUserSession,
          manual: manual,
        },
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          if (result.action === 'update') {
            console.log('ID del manual a actualizar:', result.id);
            // Asegurarse de que estamos usando el ID correcto
            // Siempre usar el ID del manual original, no el del resultado
            const manualId = manual.id;
            console.log('Usando ID del manual original:', manualId);
            this.updateDataApi(result.data, manualId);
          }
        }
      });
    } else {
      console.error('No se encontró el manual con ID:', id);
    }
  }

  /**
   * Elimina un registro
   * @param id id del registro a eliminar
   */
  deleteRow(id: any) {
    // Verificar permisos primero
    this.checkUserPermissions();

    // Solo permitir eliminar si el usuario tiene permisos
    if (!this.allowDelete) {
      this._sweetAlertService.showTopEnd({
        type: 'error',
        title: 'Acceso denegado',
        message: 'No tienes permisos para eliminar manuales',
      });
      return;
    }

    this._sweetAlertService
      .showConfirmationAlert('¿Estas seguro de eliminar el manual?')
      .then((confirm: any) => {
        if (confirm.isConfirmed) {
          this.deleteDataApi(id);
        }
      });
  }

  /**
   * Refresca la página actual
   */
  getPageRefresh(): void {
    // Verificar permisos de nuevo por si el usuario ha cambiado
    this.checkUserPermissions();

    this.apiManualListPagination();
  }

  /**
   * Cambia la página desde el paginador
   * @param event evento del paginador
   */
  getPage(event: any): void {
    this.pagination.next({
      ...this.pagination.getValue(),
      page: event.page,
    });
  }

  /**
   * Cambia la página
   * @param page número de página
   */
  pageChange(page: any): void {
    const currentPage = this.pagination.getValue();
    currentPage.page = page;
    this.pagination.next(currentPage);
  }

  /**
   * Cambia el número de elementos por página
   * @param perPage número de elementos por página
   */
  perPageChange(perPage: any): void {
    const currentPage = this.pagination.getValue();
    currentPage.perPage = perPage;
    currentPage.page = 1;
    this.pagination.next(currentPage);
  }

  /**
   * Busca un texto en la lista
   * @param event texto de búsqueda
   */
  searchChange(event: string): void {
    const currentPage = this.pagination.getValue();
    currentPage.search = event;
    currentPage.page = 1;
    this.pagination.next(currentPage);
  }

  getInputValue(event: Event): string {
    return (event.target as HTMLInputElement)?.value || '';
  }

  getSelectValue(event: Event): number {
    return Number((event.target as HTMLSelectElement)?.value || 10);
  }
}
