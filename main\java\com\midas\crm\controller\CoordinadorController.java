package com.midas.crm.controller;

import com.midas.crm.entity.DTO.asesor.AsignacionAsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsignacionMasivaDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.CoordinadorService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;

@RestController
@RequiredArgsConstructor
@RequestMapping("${api.route.coordinadores}")
@Slf4j
public class CoordinadorController {

    private final CoordinadorService coordinadorService;
    private final UserRepository userRepository;

    /**
     * Asigna asesores a un coordinador
     * Implementado con programación funcional para mejor manejo de errores
     */
    @PostMapping("/asignar-asesores")
    public ResponseEntity<?> asignarAsesoresACoordinador(@RequestBody AsignacionAsesorDTO asignacionDTO) {
        return Optional.ofNullable(asignacionDTO)
                .map(dto -> {
                    try {
                        log.debug("Intentando asignar asesores al coordinador ID: {}", dto.getCoordinadorId());
                        CoordinadorDTO coordinador = coordinadorService.asignarAsesoresACoordinador(dto);
                        log.info("Asesores asignados exitosamente al coordinador ID: {}", dto.getCoordinadorId());
                        return ResponseEntity.ok(coordinador);
                    } catch (Exception e) {
                        log.error("Error al asignar asesores: {}", e.getMessage());
                        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "Error al asignar asesores: " + e.getMessage(),
                                        null));
                    }
                })
                .orElseGet(() -> {
                    log.error("Datos de asignación nulos");
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Datos de asignación inválidos",
                                    null));
                });
    }

    /**
     * Obtiene todos los coordinadores de forma paginada
     * Implementado con programación funcional para mejor manejo de datos y errores
     */
    @GetMapping
    public ResponseEntity<?> obtenerTodosLosCoordinadores(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        try {
            log.debug("Obteniendo coordinadores paginados: página {}, tamaño {}", page, size);

            // Función para crear la respuesta a partir de la página de coordinadores
            Function<Page<CoordinadorDTO>, Map<String, Object>> crearRespuesta = pageCoordinadores -> {
                Map<String, Object> response = new HashMap<>();
                response.put("coordinadores", pageCoordinadores.getContent());
                response.put("totalItems", pageCoordinadores.getTotalElements());
                response.put("totalPages", pageCoordinadores.getTotalPages());
                response.put("currentPage", pageCoordinadores.getNumber());
                response.put("size", pageCoordinadores.getSize());
                response.put("hasNext", pageCoordinadores.hasNext());
                response.put("hasPrevious", pageCoordinadores.hasPrevious());
                return response;
            };

            // Obtener los coordinadores y aplicar la función para crear la respuesta
            return Optional.of(coordinadorService.obtenerTodosLosCoordinadoresPaginado(page, size))
                    .map(crearRespuesta)
                    .map(ResponseEntity::ok)
                    .orElseGet(() -> ResponseEntity.noContent().build());

        } catch (Exception e) {
            log.error("Error al obtener coordinadores paginados: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al obtener coordinadores paginados: " + e.getMessage(),
                            null));
        }
    }


    /**
     * Obtiene un coordinador por su ID
     * Implementado con programación funcional para mejor manejo de errores
     */
    @GetMapping("/{coordinadorId}")
    public ResponseEntity<?> obtenerCoordinadorPorId(@PathVariable Long coordinadorId) {
        return Optional.ofNullable(coordinadorId)
                .map(id -> {
                    try {
                        log.debug("Buscando coordinador con ID: {}", id);
                        CoordinadorDTO coordinador = coordinadorService.obtenerCoordinadorPorId(id);
                        log.debug("Coordinador encontrado: {}", coordinador.getNombre());
                        return ResponseEntity.ok(coordinador);
                    } catch (Exception e) {
                        log.error("Error al obtener coordinador con ID {}: {}", id, e.getMessage());
                        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "Error al obtener coordinador: " + e.getMessage(),
                                        null));
                    }
                })
                .orElseGet(() -> {
                    log.error("ID de coordinador nulo");
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "ID de coordinador inválido",
                                    null));
                });
    }

    /**
     * Obtiene la lista de asesores sin coordinador asignado
     * Implementado con programación funcional para mejor manejo de datos y errores
     */
    @GetMapping("/asesores-disponibles")
    public ResponseEntity<?> obtenerAsesoresSinCoordinador() {
        try {
            log.debug("Obteniendo asesores sin coordinador asignado");

            return Optional.ofNullable(coordinadorService.obtenerAsesoresSinCoordinador())
                    .filter(asesores -> !asesores.isEmpty())
                    .map(asesores -> {
                        log.debug("Se encontraron {} asesores sin coordinador", asesores.size());
                        return ResponseEntity.ok(asesores);
                    })
                    .orElseGet(() -> {
                        log.debug("No se encontraron asesores sin coordinador");
                        return ResponseEntity.ok(Collections.emptyList());
                    });
        } catch (Exception e) {
            log.error("Error al obtener asesores sin coordinador: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al obtener asesores sin coordinador: " + e.getMessage(),
                            null));
        }
    }

    /**
     * Elimina un asesor de un coordinador
     * Implementado con programación funcional para mejor manejo de resultados y errores
     */
    @DeleteMapping("/{coordinadorId}/asesores/{asesorId}")
    public ResponseEntity<?> eliminarAsesorDeCoordinador(
            @PathVariable Long coordinadorId,
            @PathVariable Long asesorId) {

        // Validar que los IDs no sean nulos
        if (coordinadorId == null || asesorId == null) {
            log.error("IDs de coordinador o asesor nulos");
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "IDs de coordinador o asesor inválidos",
                            null));
        }

        try {
            log.debug("Intentando eliminar asesor ID: {} del coordinador ID: {}", asesorId, coordinadorId);

            // Usar programación funcional para manejar el resultado
            return Optional.of(coordinadorService.eliminarAsesorDeCoordinador(coordinadorId, asesorId))
                    .filter(Boolean::booleanValue)
                    .map(eliminado -> {
                        log.info("Asesor ID: {} removido exitosamente del coordinador ID: {}", asesorId, coordinadorId);
                        return ResponseEntity.ok(
                                new GenericResponse<>(
                                        GenericResponseConstants.SUCCESS,
                                        "Asesor removido del coordinador exitosamente",
                                        true));
                    })
                    .orElseGet(() -> {
                        log.warn("El asesor ID: {} no pertenece al coordinador ID: {}", asesorId, coordinadorId);
                        return ResponseEntity.badRequest()
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "El asesor no pertenece a este coordinador",
                                        false));
                    });
        } catch (Exception e) {
            log.error("Error al eliminar asesor ID: {} del coordinador ID: {}: {}",
                    asesorId, coordinadorId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al eliminar asesor: " + e.getMessage(),
                            null));
        }
    }

    /**
     * Asigna múltiples asesores a un coordinador de forma masiva
     * Implementado con programación funcional para mejor manejo de datos y errores
     */
    @PostMapping("/asignar-masivo")
    public ResponseEntity<?> asignarAsesoresMasivo(@RequestBody AsignacionMasivaDTO asignacionDTO) {
        return Optional.ofNullable(asignacionDTO)
                .filter(dto -> dto.getCoordinadorId() != null && dto.getAsesorIds() != null && !dto.getAsesorIds().isEmpty())
                .map(dto -> {
                    try {
                        log.debug("Intentando asignar {} asesores al coordinador ID: {}",
                                dto.getAsesorIds().size(), dto.getCoordinadorId());

                        // Verificar si el coordinador existe y es válido
                        return userRepository.findById(dto.getCoordinadorId())
                                .filter(coordinador -> coordinador.getRole() == Role.COORDINADOR)
                                .map(coordinador -> {
                                    // Procesar los asesores usando programación funcional
                                    int asignadosExitosamente = dto.getAsesorIds().stream()
                                            .map(asesorId -> userRepository.findById(asesorId).orElse(null))
                                            .filter(asesor -> asesor != null && asesor.getRole() == Role.ASESOR)
                                            .map(asesor -> {
                                                // Crear un objeto User con solo el ID para el coordinador
                                                User coordinadorRef = new User(dto.getCoordinadorId());
                                                asesor.setCoordinador(coordinadorRef);
                                                return userRepository.save(asesor);
                                            })
                                            .mapToInt(asesor -> 1)
                                            .sum();

                                    log.info("Se asignaron {} asesores al coordinador ID: {}",
                                            asignadosExitosamente, dto.getCoordinadorId());

                                    return ResponseEntity.ok(
                                            new GenericResponse<>(
                                                    GenericResponseConstants.SUCCESS,
                                                    "Se asignaron " + asignadosExitosamente + " asesores al coordinador",
                                                    asignadosExitosamente));
                                })
                                .orElseGet(() -> {
                                    log.error("Coordinador no encontrado o no es un coordinador válido: {}", dto.getCoordinadorId());
                                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
                                            .body(new GenericResponse<>(
                                                    GenericResponseConstants.ERROR,
                                                    "Coordinador no encontrado o no es un coordinador válido",
                                                    null));
                                });
                    } catch (Exception e) {
                        log.error("Error al asignar asesores masivamente: {}", e.getMessage(), e);
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(new GenericResponse<>(
                                        GenericResponseConstants.ERROR,
                                        "Error al asignar asesores: " + e.getMessage(),
                                        null));
                    }
                })
                .orElseGet(() -> {
                    log.error("Datos de asignación masiva inválidos");
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Datos de asignación masiva inválidos",
                                    null));
                });
    }
}