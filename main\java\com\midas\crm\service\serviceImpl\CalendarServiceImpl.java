package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Calendar;
import com.midas.crm.entity.DTO.calendar.CalendarDTO;
import com.midas.crm.entity.DTO.calendar.CalendarResponseDTO;
import com.midas.crm.entity.User;
import com.midas.crm.mapper.CalendarMapper;
import com.midas.crm.repository.CalendarRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.CalendarService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.validator.CalendarValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CalendarServiceImpl implements CalendarService {

    private final CalendarRepository calendarRepository;
    private final UserRepository userRepository;
    private final CalendarValidator calendarValidator;

    @Override
    public GenericResponse<List<Calendar>> getAll() {
        try {
            List<Calendar> result = calendarRepository.findByDeletedFalse();
            return new GenericResponse<>(1, "Agendas listadas correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al listar las agendas: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<List<Calendar>> getFilterByUser(Long userId) {
        try {
            List<Calendar> result = calendarRepository.findByUserCreateId(userId);
            return new GenericResponse<>(1, "Agendas del usuario listadas correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al listar las agendas: " + e.getMessage(), null);
        }
    }


    private CalendarResponseDTO mapToResponseDTO(Calendar c) {
        return CalendarMapper.toResponseDTO(c);
    }


    @Override
    public GenericResponse<List<CalendarResponseDTO>> getFilterByDates(CalendarDTO filter) {
        try {
            if (filter.getFechaInicio() == null || filter.getFechaFinal() == null) {
                return new GenericResponse<>(0, "Las fechas son obligatorias", null);
            }

            List<CalendarResponseDTO> result = calendarRepository.findAll().stream()
                    .filter(c -> !Boolean.TRUE.equals(c.getDeleted()))
                    .filter(c -> !c.getFechaInicio().isAfter(filter.getFechaFinal()))
                    .filter(c -> !c.getFechaFinal().isBefore(filter.getFechaInicio()))
                    .filter(c -> filter.getUserCreateId() == null ||
                            (c.getUserCreate() != null && c.getUserCreate().getId().equals(filter.getUserCreateId())))
                    .map(this::mapToResponseDTO)
                    .toList();

            return new GenericResponse<>(1, "Agendas filtradas correctamente", result);
        } catch (Exception e) {
            e.printStackTrace();  // para debug
            return new GenericResponse<>(0, "Operación fallida", null);
        }
    }



    @Override
    public GenericResponse<Calendar> getById(Long id) {
        try {
            Calendar result = calendarRepository.findById(id)
                    .filter(c -> !Boolean.TRUE.equals(c.getDeleted()))
                    .orElseThrow(() -> new RuntimeException("Agenda no encontrada"));
            return new GenericResponse<>(1, "Agenda obtenida correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al obtener la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<?> create(CalendarDTO dto) {
        try {
            var errors = calendarValidator.validate(dto);
            if (!errors.isEmpty()) {
                return new GenericResponse<>(0, "Errores de validación", errors);
            }
            Calendar result = CalendarMapper.toEntity(dto, userRepository);
            result = calendarRepository.save(result);
            return new GenericResponse<>(1, "Agenda creada correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al crear la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<?> update(CalendarDTO dto, Long id) {
        try {
            var errors = calendarValidator.validate(dto);
            if (!errors.isEmpty()) {
                return new GenericResponse<>(0, "Errores de validación", errors);
            }

            Optional<Calendar> optional = calendarRepository.findById(id);
            if (optional.isEmpty()) {
                return new GenericResponse<>(0, "Agenda no encontrada para actualizar", null);
            }

            Calendar calendar = optional.get();
            CalendarMapper.mapDtoToEntity(dto, calendar, userRepository);
            Calendar result = calendarRepository.save(calendar);
            return new GenericResponse<>(1, "Agenda actualizada correctamente", result);
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al actualizar la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<String> delete(Long id) {
        try {
            Optional<Calendar> optional = calendarRepository.findById(id);
            if (optional.isPresent()) {
                Calendar calendar = optional.get();
                calendar.setDeleted(true);
                calendarRepository.save(calendar);
                return new GenericResponse<>(1, "Agenda eliminada correctamente", "Eliminado");
            } else {
                return new GenericResponse<>(0, "No se encontró la agenda", null);
            }
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al eliminar la agenda: " + e.getMessage(), null);
        }
    }

    @Override
    public GenericResponse<String> restore(Long id) {
        try {
            Optional<Calendar> optional = calendarRepository.findById(id);
            if (optional.isPresent()) {
                Calendar calendar = optional.get();
                if (Boolean.FALSE.equals(calendar.getDeleted())) {
                    return new GenericResponse<>(0, "La agenda ya fue restaurada previamente", null);
                }
                calendar.setDeleted(false);
                calendarRepository.save(calendar);
                return new GenericResponse<>(1, "Agenda restaurada correctamente", "Restaurado");
            } else {
                return new GenericResponse<>(0, "No se encontró la agenda", null);
            }
        } catch (Exception e) {
            return new GenericResponse<>(0, "Error al restaurar la agenda: " + e.getMessage(), null);
        }
    }


}