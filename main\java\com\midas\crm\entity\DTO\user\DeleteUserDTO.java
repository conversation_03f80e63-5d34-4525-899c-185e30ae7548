package com.midas.crm.entity.DTO.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO para la eliminación de usuarios
 * Contiene información sobre el tipo de eliminación (lógica o definitiva)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeleteUserDTO {
    private Long userId;
    private boolean permanent; // true para eliminación definitiva, false para baja lógica
}
