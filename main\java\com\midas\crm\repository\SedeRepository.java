package com.midas.crm.repository;

import com.midas.crm.entity.Sede;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SedeRepository extends JpaRepository<Sede, Long> {

    Optional<Sede> findByNombre(String nombre);

    boolean existsByNombre(String nombre);

    @Query("SELECT s FROM Sede s WHERE s.estado = 'A'")
    List<Sede> findAllActive();

    @Query("SELECT s FROM Sede s WHERE " +
            "(:search IS NULL OR LOWER(s.nombre) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
            "LOWER(s.ciudad) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
            "LOWER(s.direccion) LIKE LOWER(CONCAT('%', :search, '%')))")
    Page<Sede> findBySearchTerm(@Param("search") String search, Pageable pageable);
}
