package com.midas.crm.entity.DTO.user;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserWithCoordinadorDTO {
    private Long id;
    private String username;
    private String password;
    private String nombre;
    private String apellido;
    private String dni;
    private String telefono;
    private String email;
    private LocalDateTime fechaCreacion;
    private LocalDate fechaCese;
    private String estado;
    private Role role;
    private String token;
    private LocalDateTime deletionTime;
    private String sede;
    private String tokenPassword;
    private UserDTO coordinador;
}
