import { OpcionRespuestaEncuesta } from './opcion-respuesta-encuesta.model';

/**
 * Enum que define los tipos de preguntas disponibles
 */
export enum TipoPregunta {
  OPCION_MULTIPLE = 'OPCION_MULTIPLE',    // Una sola opción seleccionable
  SELECCION_MULTIPLE = 'SELECCION_MULTIPLE', // Múltiples opciones seleccionables
  ESCALA_LIKERT = 'ESCALA_LIKERT',      // Escala de valoración (1-5, 1-7, etc.)
  TEXTO_LIBRE = 'TEXTO_LIBRE',        // Respuesta de texto libre
  FECHA = 'FECHA',              // Respuesta de tipo fecha
  NUMERO = 'NUMERO'              // Respuesta numérica
}

/**
 * Modelo que representa una pregunta de encuesta
 */
export interface PreguntaEncuesta {
  id: number;
  enunciado: string;
  descripcion?: string;
  orden: number;
  tipo: TipoPregunta;
  esObligatoria: boolean;
  encuestaId: number;
  opciones?: OpcionRespuestaEncuesta[];
  estado: string; // A: Activo, I: Inactivo
  fechaCreacion: string;
  fechaActualizacion: string;
  
  // Estadísticas
  totalRespuestas?: number;
  promedioRespuestasNumericas?: number;
}
