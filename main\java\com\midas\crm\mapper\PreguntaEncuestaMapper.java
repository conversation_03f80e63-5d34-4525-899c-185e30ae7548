package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.encuesta.OpcionRespuestaEncuestaDTO;
import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaDTO;
import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.PreguntaEncuesta;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper para convertir entre entidades PreguntaEncuesta y sus DTOs
 */
public final class PreguntaEncuestaMapper {

    private PreguntaEncuestaMapper() {}

    /**
     * Convierte un DTO de creación a una entidad PreguntaEncuesta
     */
    public static PreguntaEncuesta toEntity(PreguntaEncuestaCreateDTO dto, Encuesta encuesta) {
        PreguntaEncuesta pregunta = new PreguntaEncuesta();
        pregunta.setEnunciado(dto.getEnunciado());
        pregunta.setDescripcion(dto.getDescripcion());
        pregunta.setOrden(dto.getOrden());
        pregunta.setTipo(dto.getTipo());
        pregunta.setEsObligatoria(dto.getEsObligatoria());
        pregunta.setEncuesta(encuesta);
        pregunta.setEstado("A");
        return pregunta;
    }

    /**
     * Convierte una entidad PreguntaEncuesta a un DTO
     */
    public static PreguntaEncuestaDTO toDTO(PreguntaEncuesta pregunta) {
        if (pregunta == null) return null;

        PreguntaEncuestaDTO dto = new PreguntaEncuestaDTO();
        dto.setId(pregunta.getId());
        dto.setEnunciado(pregunta.getEnunciado());
        dto.setDescripcion(pregunta.getDescripcion());
        dto.setOrden(pregunta.getOrden());
        dto.setTipo(pregunta.getTipo());
        dto.setEsObligatoria(pregunta.getEsObligatoria());
        dto.setEstado(pregunta.getEstado());
        dto.setFechaCreacion(pregunta.getFechaCreacion());
        dto.setFechaActualizacion(pregunta.getFechaActualizacion());

        if (pregunta.getEncuesta() != null) {
            dto.setEncuestaId(pregunta.getEncuesta().getId());
        }

        return dto;
    }

    /**
     * Convierte una entidad PreguntaEncuesta a un DTO incluyendo sus opciones
     */
    public static PreguntaEncuestaDTO toDTOWithOpciones(PreguntaEncuesta pregunta, List<OpcionRespuestaEncuestaDTO> opciones) {
        PreguntaEncuestaDTO dto = toDTO(pregunta);
        if (dto != null) {
            dto.setOpciones(opciones);
        }
        return dto;
    }
}
