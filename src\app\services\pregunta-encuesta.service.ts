import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import { PreguntaEncuesta } from '@app/models/backend/encuesta/pregunta-encuesta.model';
import { PreguntaEncuestaCreateRequest } from '@app/models/backend/encuesta/encuesta.model';

@Injectable({
  providedIn: 'root'
})
export class PreguntaEncuestaService {
  private baseUrl = `${environment.url}api/preguntas-encuesta`;

  constructor(private http: HttpClient) { }

  /**
   * Crea una nueva pregunta para una encuesta
   */
  create(pregunta: PreguntaEncuestaCreateRequest): Observable<GenericResponse<PreguntaEncuesta>> {
    return this.http.post<GenericResponse<PreguntaEncuesta>>(`${this.baseUrl}`, pregunta);
  }

  /**
   * Obtiene una pregunta por su ID
   */
  getById(id: number): Observable<GenericResponse<PreguntaEncuesta>> {
    return this.http.get<GenericResponse<PreguntaEncuesta>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene una pregunta completa (con opciones) por su ID
   */
  getCompleta(id: number): Observable<GenericResponse<PreguntaEncuesta>> {
    return this.http.get<GenericResponse<PreguntaEncuesta>>(`${this.baseUrl}/${id}/completa`);
  }

  /**
   * Actualiza una pregunta existente
   */
  update(id: number, pregunta: PreguntaEncuestaCreateRequest): Observable<GenericResponse<PreguntaEncuesta>> {
    return this.http.put<GenericResponse<PreguntaEncuesta>>(`${this.baseUrl}/${id}`, pregunta);
  }

  /**
   * Elimina una pregunta (cambio de estado a inactivo)
   */
  delete(id: number): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene todas las preguntas de una encuesta
   */
  getByEncuestaId(encuestaId: number): Observable<GenericResponse<PreguntaEncuesta[]>> {
    return this.http.get<GenericResponse<PreguntaEncuesta[]>>(`${this.baseUrl}/encuesta/${encuestaId}`);
  }

  /**
   * Obtiene estadísticas de respuestas para una pregunta específica
   */
  getEstadisticas(id: number): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(`${this.baseUrl}/${id}/estadisticas`);
  }

  /**
   * Reordena las preguntas de una encuesta
   */
  reordenar(encuestaId: number, nuevosOrdenes: number[]): Observable<GenericResponse<PreguntaEncuesta[]>> {
    return this.http.put<GenericResponse<PreguntaEncuesta[]>>(`${this.baseUrl}/encuesta/${encuestaId}/reordenar`, nuevosOrdenes);
  }
}
