package com.midas.crm.mapper;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.DTO.curso.CursoCreateDTO;
import com.midas.crm.entity.DTO.curso.CursoDTO;
import com.midas.crm.entity.DTO.curso.CursoUpdateDTO;
import com.midas.crm.entity.User;

import java.util.Objects;

public final class CursoMapper {

    private CursoMapper() {}

    public static Curso toEntity(CursoCreateDTO dto, User user) {
        Curso curso = new Curso();
        curso.setNombre(dto.getNombre());
        curso.setDescripcion(dto.getDescripcion());
        curso.setFechaInicio(dto.getFechaInicio());
        curso.setFechaFin(dto.getFechaFin());
        curso.setEstado("A");
        curso.setVideoUrl(dto.getVideoUrl());
        curso.setUsuario(user);
        return curso;
    }

    public static CursoDTO toDTO(Curso curso) {
        if (curso == null) return null;

        return new CursoDTO(
                curso.getId(),
                curso.getNombre(),
                curso.getDescripcion(),
                curso.getFechaInicio(),
                curso.getFechaFin(),
                curso.getEstado(),
                curso.getVideoUrl(),
                curso.getUsuario() != null ? UserMapper.toDTO(curso.getUsuario()) : null
        );
    }

    public static void updateEntity(Curso curso, CursoUpdateDTO dto) {
        if (dto.getNombre() != null) curso.setNombre(dto.getNombre());
        if (dto.getDescripcion() != null) curso.setDescripcion(dto.getDescripcion());
        if (dto.getFechaInicio() != null) curso.setFechaInicio(dto.getFechaInicio());
        if (dto.getFechaFin() != null) curso.setFechaFin(dto.getFechaFin());
        if (dto.getEstado() != null) curso.setEstado(dto.getEstado());
        if (dto.getVideoUrl() != null) curso.setVideoUrl(dto.getVideoUrl());
    }
}
