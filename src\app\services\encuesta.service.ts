import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import { Encuesta, EncuestaCreateRequest, EncuestaUpdateRequest } from '@app/models/backend/encuesta/encuesta.model';

@Injectable({
  providedIn: 'root'
})
export class EncuestaService {
  private baseUrl = `${environment.url}api/encuestas`;

  constructor(private http: HttpClient) { }

  /**
   * Obtiene todas las encuestas
   */
  getAll(): Observable<GenericResponse<Encuesta[]>> {
    return this.http.get<GenericResponse<Encuesta[]>>(`${this.baseUrl}`);
  }

  /**
   * Obtiene todas las encuestas activas
   */
  getAllActivas(): Observable<GenericResponse<Encuesta[]>> {
    return this.http.get<GenericResponse<Encuesta[]>>(`${this.baseUrl}/activas`);
  }

  /**
   * Obtiene todas las encuestas disponibles para el usuario autenticado
   */
  getDisponibles(): Observable<GenericResponse<Encuesta[]>> {
    return this.http.get<GenericResponse<Encuesta[]>>(`${this.baseUrl}/disponibles`);
  }

  /**
   * Obtiene todas las encuestas creadas por el usuario autenticado
   */
  getCreadas(): Observable<GenericResponse<Encuesta[]>> {
    return this.http.get<GenericResponse<Encuesta[]>>(`${this.baseUrl}/creadas`);
  }

  /**
   * Obtiene una encuesta por su ID
   */
  getById(id: number): Observable<GenericResponse<Encuesta>> {
    return this.http.get<GenericResponse<Encuesta>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene una encuesta completa (con preguntas y opciones) por su ID
   */
  getCompleta(id: number): Observable<GenericResponse<Encuesta>> {
    return this.http.get<GenericResponse<Encuesta>>(`${this.baseUrl}/${id}/completa`);
  }

  /**
   * Crea una nueva encuesta
   */
  create(encuesta: EncuestaCreateRequest): Observable<GenericResponse<Encuesta>> {
    return this.http.post<GenericResponse<Encuesta>>(`${this.baseUrl}`, encuesta);
  }

  /**
   * Actualiza una encuesta existente
   */
  update(id: number, encuesta: EncuestaUpdateRequest): Observable<GenericResponse<Encuesta>> {
    return this.http.put<GenericResponse<Encuesta>>(`${this.baseUrl}/${id}`, encuesta);
  }

  /**
   * Elimina una encuesta (cambio de estado a inactivo)
   */
  delete(id: number): Observable<GenericResponse<void>> {
    return this.http.delete<GenericResponse<void>>(`${this.baseUrl}/${id}`);
  }

  /**
   * Obtiene estadísticas generales de una encuesta
   */
  getEstadisticas(id: number): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(`${this.baseUrl}/${id}/estadisticas`);
  }

  /**
   * Obtiene todas las encuestas con paginación y filtro
   */
  getPageable(page: number = 0, size: number = 10, search?: string, sortBy: string = 'id', sortDir: string = 'asc'): Observable<GenericResponse<any>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString())
      .set('sortBy', sortBy)
      .set('sortDir', sortDir);
    
    if (search) {
      params = params.set('search', search);
    }
    
    return this.http.get<GenericResponse<any>>(`${this.baseUrl}/pageable`, { params });
  }
}
