package com.midas.crm.service;

import com.midas.crm.entity.DTO.progreso.ProgresoCreateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUpdateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUsuarioDTO;

import java.util.List;
import java.util.Map;

public interface ProgresoUsuarioService {
    ProgresoUsuarioDTO createProgreso(ProgresoCreateDTO dto);
    ProgresoUsuarioDTO updateProgreso(Long leccionId, Long usuarioId, ProgresoUpdateDTO dto);
    ProgresoUsuarioDTO getProgresoByLeccionAndUsuario(Long leccionId, Long usuarioId);
    List<ProgresoUsuarioDTO> getProgresoByUsuario(Long usuarioId);
    List<ProgresoUsuarioDTO> getProgresoByCursoAndUsuario(Long cursoId, Long usuarioId);
    Map<String, Object> getResumenProgresoCurso(Long cursoId, Long usuarioId);
}
