import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import {
  RespuestaEncuestaUsuario,
  RespuestaEncuestaUsuarioCreateRequest,
} from '@app/models/backend/encuesta/respuesta-encuesta-usuario.model';
import { DetalleRespuestaEncuestaUsuarioCreateRequest } from '@app/models/backend/encuesta/detalle-respuesta-encuesta-usuario.model';

@Injectable({
  providedIn: 'root',
})
export class RespuestaEncuestaUsuarioService {
  private baseUrl = `${environment.url}api/respuestas-encuesta`;

  constructor(private http: HttpClient) {}

  /**
   * Inicia una nueva respuesta de usuario a una encuesta
   */
  iniciar(
    respuesta: RespuestaEncuestaUsuarioCreateRequest
  ): Observable<GenericResponse<RespuestaEncuestaUsuario>> {
    return this.http.post<GenericResponse<RespuestaEncuestaUsuario>>(
      `${this.baseUrl}/iniciar`,
      respuesta
    );
  }

  /**
   * Guarda una respuesta a una pregunta específica
   */
  responderPregunta(
    respuestaEncuestaUsuarioId: number,
    detalle: DetalleRespuestaEncuestaUsuarioCreateRequest
  ): Observable<GenericResponse<RespuestaEncuestaUsuario>> {
    return this.http.post<GenericResponse<RespuestaEncuestaUsuario>>(
      `${this.baseUrl}/${respuestaEncuestaUsuarioId}/responder`,
      detalle
    );
  }

  /**
   * Finaliza una respuesta de encuesta
   */
  finalizar(
    respuestaEncuestaUsuarioId: number
  ): Observable<GenericResponse<RespuestaEncuestaUsuario>> {
    return this.http.post<GenericResponse<RespuestaEncuestaUsuario>>(
      `${this.baseUrl}/${respuestaEncuestaUsuarioId}/finalizar`,
      {}
    );
  }

  /**
   * Obtiene una respuesta de encuesta por su ID
   */
  getById(id: number): Observable<GenericResponse<RespuestaEncuestaUsuario>> {
    return this.http.get<GenericResponse<RespuestaEncuestaUsuario>>(
      `${this.baseUrl}/${id}`
    );
  }

  /**
   * Obtiene todas las respuestas de un usuario a una encuesta específica
   */
  getByUsuarioAndEncuesta(
    usuarioId: number,
    encuestaId: number
  ): Observable<GenericResponse<RespuestaEncuestaUsuario[]>> {
    return this.http.get<GenericResponse<RespuestaEncuestaUsuario[]>>(
      `${this.baseUrl}/usuario/${usuarioId}/encuesta/${encuestaId}`
    );
  }

  /**
   * Obtiene todas las respuestas a una encuesta específica
   */
  getByEncuesta(
    encuestaId: number
  ): Observable<GenericResponse<RespuestaEncuestaUsuario[]>> {
    return this.http.get<GenericResponse<RespuestaEncuestaUsuario[]>>(
      `${this.baseUrl}/encuesta/${encuestaId}`
    );
  }

  /**
   * Verifica si un usuario ha completado una encuesta
   */
  hasCompletado(
    usuarioId: number,
    encuestaId: number
  ): Observable<GenericResponse<boolean>> {
    return this.http.get<GenericResponse<boolean>>(
      `${this.baseUrl}/usuario/${usuarioId}/encuesta/${encuestaId}/completada`
    );
  }

  /**
   * Obtiene la respuesta en progreso de un usuario para una encuesta específica
   * Filtra las respuestas del usuario para encontrar una que no esté completada
   */
  getRespuestaEnProgreso(
    usuarioId: number,
    encuestaId: number
  ): Observable<GenericResponse<RespuestaEncuestaUsuario | null>> {
    return this.getByUsuarioAndEncuesta(usuarioId, encuestaId).pipe(
      map((response) => {
        if (response.rpta === 1 && response.data && response.data.length > 0) {
          // Buscar una respuesta no completada
          const respuestaEnProgreso = response.data.find((r) => !r.completada);

          if (respuestaEnProgreso) {
            return {
              rpta: 1,
              msg: 'Respuesta en progreso encontrada',
              data: respuestaEnProgreso,
            };
          }
        }

        // Si no hay respuesta en progreso
        return {
          rpta: 0,
          msg: 'No se encontró respuesta en progreso',
          data: null,
        };
      })
    );
  }

  /**
   * Obtiene estadísticas de respuestas para una encuesta
   */
  getEstadisticas(encuestaId: number): Observable<GenericResponse<any>> {
    return this.http.get<GenericResponse<any>>(
      `${this.baseUrl}/encuesta/${encuestaId}/estadisticas`
    );
  }

  /**
   * Obtiene la última respuesta completada de un usuario para una encuesta específica
   * @param usuarioId ID del usuario
   * @param encuestaId ID de la encuesta
   * @returns Observable con la respuesta completada o null si no existe
   */
  getUltimaRespuestaCompletada(
    usuarioId: number,
    encuestaId: number
  ): Observable<GenericResponse<RespuestaEncuestaUsuario | null>> {
    return new Observable<GenericResponse<RespuestaEncuestaUsuario | null>>(
      (observer) => {
        this.getByUsuarioAndEncuesta(usuarioId, encuestaId).subscribe({
          next: (response) => {
            if (
              response.rpta === 1 &&
              response.data &&
              response.data.length > 0
            ) {
              // Filtrar respuestas completadas
              const respuestasCompletadas = response.data.filter(
                (r) => r.completada
              );

              if (respuestasCompletadas.length > 0) {
                // Obtener la última respuesta completada (la más reciente)
                const ultimaRespuesta =
                  respuestasCompletadas[respuestasCompletadas.length - 1];

                // Obtener los detalles completos de esta respuesta
                this.getById(ultimaRespuesta.id).subscribe({
                  next: (detalleResponse) => {
                    if (detalleResponse.rpta === 1 && detalleResponse.data) {
                      observer.next({
                        rpta: 1,
                        msg: 'Respuesta completada encontrada',
                        data: detalleResponse.data,
                      });
                    } else {
                      observer.next({
                        rpta: 0,
                        msg: 'No se encontraron detalles de la respuesta completada',
                        data: null,
                      });
                    }
                    observer.complete();
                  },
                  error: (error) => {
                    console.error(
                      'Error al obtener detalles de la respuesta completada:',
                      error
                    );
                    observer.next({
                      rpta: 0,
                      msg: 'Error al obtener detalles de la respuesta completada',
                      data: null,
                    });
                    observer.complete();
                  },
                });
              } else {
                // No hay respuestas completadas
                observer.next({
                  rpta: 0,
                  msg: 'No se encontraron respuestas completadas',
                  data: null,
                });
                observer.complete();
              }
            } else {
              // No hay respuestas
              observer.next({
                rpta: 0,
                msg: 'No se encontraron respuestas',
                data: null,
              });
              observer.complete();
            }
          },
          error: (error) => {
            console.error('Error al obtener respuestas completadas:', error);
            observer.next({
              rpta: 0,
              msg: 'Error al obtener respuestas completadas',
              data: null,
            });
            observer.complete();
          },
        });
      }
    );
  }
}
