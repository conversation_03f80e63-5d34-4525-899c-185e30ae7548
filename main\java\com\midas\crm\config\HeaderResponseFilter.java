package com.midas.crm.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.midas.crm.utils.MidasConstants;


import java.io.IOException;

@Component
@Slf4j
public class HeaderResponseFilter implements Filter {

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		// No se requiere ninguna acción de inicialización
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		try {
			HttpServletResponse myResponse = (HttpServletResponse) response;
			myResponse.setHeader(MidasConstants.HEADER_RESPONSE_NAME_SERVER, MidasConstants.HEADER_RESPONSE_VALUE_SERVER);
			log.debug("HeaderResponseFilter.myResponse {}", myResponse.getHeaderNames());
			chain.doFilter(request, myResponse);
		} catch (IOException e) {
			// Manejar específicamente errores de "Broken pipe" o "Connection reset"
			if (e.getMessage() != null && (e.getMessage().contains("Broken pipe")
					|| e.getMessage().contains("Connection reset"))) {
				log.debug("Cliente cerró la conexión: {}", e.getMessage());
				// No propagar la excepción, ya que es un comportamiento normal cuando el cliente cierra la conexión
			} else {
				// Para otros errores de IO, registrar y propagar
				log.error("Error de IO en HeaderResponseFilter: {}", e.getMessage());
				throw e;
			}
		} catch (Exception e) {
			log.error("Ocurrió un error en HeaderResponseFilter: {}", e.getMessage(), e);
			throw e; // Propagar la excepción para que otros filtros puedan manejarla si es necesario
		}
	}

	@Override
	public void destroy() {
		// No se requiere ninguna acción de limpieza
	}

}
