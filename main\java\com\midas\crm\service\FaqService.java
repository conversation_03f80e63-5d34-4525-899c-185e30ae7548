package com.midas.crm.service;

import com.midas.crm.entity.DTO.faq.FaqDTO;
import com.midas.crm.entity.DTO.faq.FaqRespuestaDTO;
import com.midas.crm.entity.EstadoFaq;

import java.util.List;

public interface FaqService {
    // Métodos básicos CRUD
    FaqDTO saveFaq(FaqDTO faqDTO);
    FaqDTO updateFaq(FaqDTO faqDTO, Long id);
    void deleteFaq(Long id);
    FaqDTO getFaqById(Long id);
    List<FaqDTO> getAllFaqs();
    List<FaqDTO> getFaqsByCategoria(String categoria);
    FaqDTO incrementViews(Long id);

    // Métodos para preguntas y respuestas de usuarios
    FaqDTO crearPregunta(FaqDTO faqDTO, Long usuarioId);
    FaqDTO responderPregunta(Long faqId, String respuesta, Long usuarioId);
    List<FaqDTO> getPreguntasByUsuario(Long usuarioId);
    List<FaqDTO> getPreguntasSinResponder();
    List<FaqDTO> getPreguntasPublicas();
    List<FaqDTO> getPreguntasPrivadasByUsuario(Long usuarioId);

    // Nuevos métodos para el estado de las preguntas
    FaqDTO cambiarEstadoFaq(Long faqId, EstadoFaq estado);
    List<FaqDTO> getPreguntasPorEstado(EstadoFaq estado);

    // Nuevos métodos para múltiples respuestas
    FaqRespuestaDTO agregarRespuesta(Long faqId, FaqRespuestaDTO respuestaDTO, Long usuarioId);
    FaqRespuestaDTO actualizarRespuesta(Long respuestaId, FaqRespuestaDTO respuestaDTO);
    void eliminarRespuesta(Long respuestaId);
    List<FaqRespuestaDTO> getRespuestasByFaqId(Long faqId);
    List<FaqRespuestaDTO> getRespuestasByUsuarioId(Long usuarioId);
}
