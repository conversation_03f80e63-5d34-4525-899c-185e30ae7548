package com.midas.crm.entity.DTO.leccion;

import com.midas.crm.entity.Leccion.TipoLeccion;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LeccionUpdateDTO {
    private String titulo;
    private String descripcion;
    private Integer orden;
    private TipoLeccion tipoLeccion;
    private String videoUrl;
    private String subtitlesUrl;
    private String duracion;
    private String thumbnailUrl;
    private String pdfUrl;
    private String estado;
}
