package com.midas.crm.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.ParseContext;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import org.springframework.context.annotation.Bean;

import java.util.EnumSet;
import java.util.Set;

/**
 * Configuración para JsonPath con Jackson
 *
 * Esta clase configura JsonPath para usar Jackson como proveedor de JSON y mapeo,
 * lo que permite una integración más eficiente con el resto de la aplicación que
 * también utiliza Jackson para la serialización/deserialización de JSON.
 *
 * Proporciona un contexto de análisis optimizado para trabajar con documentos JSON
 * en la aplicación MIDAS CRM.
 */
@org.springframework.context.annotation.Configuration
public class JsonPathConfiguration {

    /**
     * Crea un ObjectMapper personalizado para JsonPath
     *
     * @return ObjectMapper configurado con opciones optimizadas para la aplicación
     */
    @Bean(name = "jsonPathObjectMapper")
    public ObjectMapper jsonPathObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // Registrar módulo para manejar tipos de fecha/hora de Java 8+
        objectMapper.registerModule(new JavaTimeModule());

        // Configurar serialización
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        // Configurar deserialización
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);

        return objectMapper;
    }

    /**
     * Crea un contexto de análisis JsonPath configurado con Jackson
     *
     * @param objectMapper El ObjectMapper personalizado para usar con JsonPath
     * @return Contexto de análisis JsonPath optimizado para la aplicación
     */
    @Bean(name = "parseContextJackson")
    public ParseContext jsonPathParseContextJackson(ObjectMapper objectMapper) {
        // Definir opciones de configuración para JsonPath
        Set<Option> options = EnumSet.of(
            Option.DEFAULT_PATH_LEAF_TO_NULL,  // Devuelve null para rutas que no existen
            Option.SUPPRESS_EXCEPTIONS,        // Suprime excepciones durante la evaluación
            Option.ALWAYS_RETURN_LIST          // Siempre devuelve una lista para consultas de array
        );

        // Crear configuración personalizada para JsonPath
        Configuration config = Configuration.builder()
                .options(options)
                // Usar Jackson para el procesamiento de JSON
                .jsonProvider(new JacksonJsonProvider(objectMapper))
                // Usar Jackson para el mapeo de objetos
                .mappingProvider(new JacksonMappingProvider(objectMapper))
                .build();

        // Devolver el contexto de análisis con la configuración personalizada
        return JsonPath.using(config);
    }
}
