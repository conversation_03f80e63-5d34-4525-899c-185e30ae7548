.image-viewer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  cursor: pointer;
}

.image-viewer-content {
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  cursor: default;
  
  .header {
    position: absolute;
    top: -50px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    color: white;
    
    h2 {
      margin: 0;
      font-size: 1.5rem;
      text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
    }
  }
}

.image-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  
  img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    
    &.loaded {
      opacity: 1;
    }
  }
}

// Hover effect
.image-container img:hover {
  transform: scale(1.02);
  transition: transform 0.3s ease;
}
