package com.midas.crm.controller;

import com.midas.crm.entity.DTO.progreso.ProgresoCreateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUpdateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUsuarioDTO;
import com.midas.crm.service.ProgresoUsuarioService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.progreso}")
@RequiredArgsConstructor
public class ProgresoUsuarioController {

    private final ProgresoUsuarioService progresoUsuarioService;

    /**
     * Crea un nuevo progreso
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<ProgresoUsuarioDTO>> createProgreso(@Valid @RequestBody ProgresoCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(progresoUsuarioService::createProgreso)
            .map(progreso -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Progreso registrado exitosamente", progreso)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Actualiza un progreso existente
     * Implementado con programación funcional
     */
    @PutMapping("/leccion/{leccionId}/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<ProgresoUsuarioDTO>> updateProgreso(
            @PathVariable Long leccionId,
            @PathVariable Long usuarioId,
            @Valid @RequestBody ProgresoUpdateDTO dto) {
        return Optional.ofNullable(dto)
            .map(updateDto -> progresoUsuarioService.updateProgreso(leccionId, usuarioId, updateDto))
            .map(progreso -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Progreso actualizado exitosamente", progreso)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene progreso por lección y usuario
     * Implementado con programación funcional
     */
    @GetMapping("/leccion/{leccionId}/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<ProgresoUsuarioDTO>> getProgresoByLeccionAndUsuario(
            @PathVariable Long leccionId,
            @PathVariable Long usuarioId) {
        return Optional.of(new Long[]{leccionId, usuarioId})
            .map(ids -> progresoUsuarioService.getProgresoByLeccionAndUsuario(ids[0], ids[1]))
            .map(progreso -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Progreso encontrado", progreso)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene progreso por usuario
     * Implementado con programación funcional
     */
    @GetMapping("/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<List<ProgresoUsuarioDTO>>> getProgresoByUsuario(@PathVariable Long usuarioId) {
        return Optional.ofNullable(usuarioId)
            .map(progresoUsuarioService::getProgresoByUsuario)
            .map(progresos -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Progreso del usuario", progresos)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene progreso por curso y usuario
     * Implementado con programación funcional
     */
    @GetMapping("/curso/{cursoId}/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<List<ProgresoUsuarioDTO>>> getProgresoByCursoAndUsuario(
            @PathVariable Long cursoId,
            @PathVariable Long usuarioId) {
        return Optional.of(new Long[]{cursoId, usuarioId})
            .map(ids -> progresoUsuarioService.getProgresoByCursoAndUsuario(ids[0], ids[1]))
            .map(progresos -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Progreso del usuario en el curso", progresos)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Marca una lección como completada
     * Implementado con programación funcional
     */
    @PostMapping("/leccion/completar")
    public ResponseEntity<GenericResponse<ProgresoUsuarioDTO>> completarLeccion(@Valid @RequestBody ProgresoCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(progresoUsuarioService::createProgreso)
            .map(progreso -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Lección marcada como completada", progreso)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene lecciones completadas por curso y usuario
     * Implementado con programación funcional
     */
    @GetMapping("/curso/{cursoId}/usuario/{usuarioId}/lecciones")
    public ResponseEntity<GenericResponse<List<ProgresoUsuarioDTO>>> getLeccionesCompletadas(
            @PathVariable Long cursoId,
            @PathVariable Long usuarioId) {
        return Optional.of(new Long[]{cursoId, usuarioId})
            .map(ids -> progresoUsuarioService.getProgresoByCursoAndUsuario(ids[0], ids[1]))
            .map(lecciones -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Lecciones completadas", lecciones)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene resumen de progreso por curso y usuario
     * Implementado con programación funcional
     */
    @GetMapping("/resumen/curso/{cursoId}/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getResumenProgresoCurso(
            @PathVariable Long cursoId,
            @PathVariable Long usuarioId) {
        return Optional.of(new Long[]{cursoId, usuarioId})
            .map(ids -> progresoUsuarioService.getResumenProgresoCurso(ids[0], ids[1]))
            .map(resumen -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Resumen de progreso del curso", resumen)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }
}
