import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EncuestaService } from '@app/services/encuesta.service';
import { RespuestaEncuestaUsuarioService } from '@app/services/respuesta-encuesta-usuario.service';
import { NotificationService } from '@app/services/notification/notification.service';
import { Encuesta } from '@app/models/backend/encuesta/encuesta.model';
import { TipoPregunta } from '@app/models/backend/encuesta/pregunta-encuesta.model';
import { forkJoin, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { GeneralService } from '@app/services/general.service';

@Component({
  selector: 'app-responder-encuesta',
  templateUrl: './responder-encuesta.component.html',
  styleUrls: ['./responder-encuesta.component.scss'],
})
export class ResponderEncuestaComponent implements OnInit, OnDestroy {
  encuestaId: number;
  encuesta: Encuesta | null = null;
  respuestaId: number;

  // Formulario
  form: FormGroup;

  // Estado
  loading = false;
  submitting = false;
  error = false;
  encuestaCompletada = false;

  // Respuesta completada
  respuestaCompletada: any = null;

  // Para manejar selección múltiple
  selectedOptions: Map<number, number[]> = new Map();

  // Enums
  tipoPregunta = TipoPregunta;

  // Tiempo
  tiempoRestante: number | null = null;
  tiempoInterval: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private fb: FormBuilder,
    private encuestaService: EncuestaService,
    private respuestaService: RespuestaEncuestaUsuarioService,
    private notificationService: NotificationService,
    private generalService: GeneralService
  ) {
    // Inicializar el formulario con un grupo vacío para evitar errores
    this.form = this.fb.group({});
  }

  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    this.encuestaId = idParam ? +idParam : 0;
    this.loadEncuesta();
  }

  ngOnDestroy(): void {
    if (this.tiempoInterval) {
      clearInterval(this.tiempoInterval);
    }
  }

  loadEncuesta(): void {
    this.loading = true;

    // Obtener el ID del usuario actual
    const usuarioId = this.generalService.getUserId();

    // Primero verificamos si el usuario ya ha completado la encuesta
    if (usuarioId) {
      this.respuestaService
        .hasCompletado(usuarioId, this.encuestaId)
        .subscribe({
          next: (response) => {
            if (response.rpta === 1 && response.data === true) {
              // El usuario ya ha completado la encuesta
              this.encuestaCompletada = true;

              // Mostrar mensaje informativo
              this.notificationService.info(
                'Ya has completado esta encuesta anteriormente'
              );

              // Redirigir al listado de encuestas
              this.router.navigate(['/encuestas']);

              return;
            } else {
              // El usuario no ha completado la encuesta, cargarla normalmente
              this.cargarEncuesta();
            }
          },
          error: (error) => {
            console.error(
              'Error al verificar si la encuesta está completada:',
              error
            );
            // Si hay un error en la verificación, intentamos cargar la encuesta de todos modos
            this.cargarEncuesta();
          },
        });
    } else {
      // Si no hay usuario (encuesta anónima), cargar la encuesta directamente
      this.cargarEncuesta();
    }
  }

  /**
   * Carga los datos de la encuesta
   */
  cargarEncuesta(): void {
    this.encuestaService.getCompleta(this.encuestaId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.encuesta = response.data;
          this.iniciarRespuesta();
        } else {
          console.error('Respuesta vacía al cargar encuesta');
          this.error = true;
          this.notificationService.error(
            'Error al cargar la encuesta. Por favor, intente nuevamente.'
          );
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error al cargar encuesta:', error);
        this.loading = false;
        this.error = true;

        // Verificar si el error está relacionado con una encuesta ya completada
        if (
          error.error &&
          error.error.msg &&
          error.error.msg.includes('completada')
        ) {
          this.encuestaCompletada = true;
          this.error = false;
          this.notificationService.info(
            'Ya has completado esta encuesta anteriormente'
          );

          // Redirigir al listado de encuestas
          this.router.navigate(['/encuestas']);
        } else {
          this.notificationService.error(
            'Error al cargar la encuesta. Por favor, intente nuevamente.'
          );
        }
      },
    });
  }

  /**
   * Carga la encuesta y la respuesta completada por el usuario
   * Este método ya no se usa directamente, pero se mantiene por compatibilidad
   */
  cargarEncuestaYRespuestaCompletada(): void {
    // Redirigir al listado de encuestas
    this.router.navigate(['/encuestas']);
  }

  iniciarRespuesta(): void {
    // Verificar que tengamos un ID de encuesta válido
    if (!this.encuestaId || this.encuestaId <= 0) {
      console.error('ID de encuesta inválido:', this.encuestaId);
      this.notificationService.error(
        'Error al cargar la encuesta. ID inválido.'
      );
      this.error = true;
      this.loading = false;
      return;
    }

    // Inicializar el formulario incluso si falla la petición
    this.initForm();

    // Obtener el ID del usuario actual
    const usuarioId = this.generalService.getUserId();

    // Verificar si tenemos un ID de usuario válido
    if (!usuarioId) {
      console.warn(
        'No se pudo obtener el ID de usuario, intentando iniciar encuesta anónima'
      );
    } else {
      console.log('Iniciando encuesta con usuario ID:', usuarioId);
    }

    // Primero intentamos obtener una respuesta en progreso
    if (usuarioId) {
      this.respuestaService
        .getRespuestaEnProgreso(usuarioId, this.encuestaId)
        .subscribe({
          next: (response) => {
            if (response && response.rpta === 1 && response.data) {
              console.log('Encontrada respuesta en progreso:', response.data);
              this.respuestaId = response.data.id;
              this.notificationService.info(
                'Continuando con la encuesta en progreso'
              );

              // Iniciar temporizador si hay tiempo límite
              if (this.encuesta && this.encuesta.tiempoLimite) {
                this.tiempoRestante = this.encuesta.tiempoLimite * 60; // Convertir a segundos
                this.iniciarTemporizador();
              }

              // Cargar las respuestas existentes en el formulario
              this.cargarRespuestasExistentes(response.data);
              return;
            }

            // Si no hay respuesta en progreso, intentar iniciar una nueva
            this.iniciarNuevaRespuesta(usuarioId);
          },
          error: (error) => {
            console.error('Error al buscar respuestas existentes:', error);
            // Si falla, intentar iniciar una nueva respuesta
            this.iniciarNuevaRespuesta(usuarioId);
          },
        });
    } else {
      // Si no hay usuario, intentar iniciar una encuesta anónima
      this.iniciarNuevaRespuesta(undefined);
    }
  }

  /**
   * Inicia una nueva respuesta de encuesta
   */
  iniciarNuevaRespuesta(usuarioId: number | undefined): void {
    this.respuestaService
      .iniciar({
        encuestaId: this.encuestaId,
        usuarioId: usuarioId,
      })
      .subscribe({
        next: (response) => {
          if (response && response.data) {
            this.respuestaId = response.data.id;
            this.notificationService.success('Encuesta iniciada correctamente');

            // Iniciar temporizador si hay tiempo límite
            if (this.encuesta && this.encuesta.tiempoLimite) {
              this.tiempoRestante = this.encuesta.tiempoLimite * 60; // Convertir a segundos
              this.iniciarTemporizador();
            }
          } else {
            console.error('Respuesta vacía al iniciar encuesta');
            this.notificationService.error(
              'Error al iniciar la encuesta. Respuesta vacía.'
            );
          }
        },
        error: (error) => {
          console.error('Error al iniciar respuesta:', error);

          // Verificar si el error es porque ya existe una respuesta en progreso
          if (
            error.error &&
            error.error.msg &&
            error.error.msg.includes('Ya existe una respuesta en progreso')
          ) {
            this.notificationService.info(
              'Ya existe una respuesta en progreso para esta encuesta'
            );

            // Intentar obtener la respuesta en progreso
            if (usuarioId) {
              this.obtenerRespuestaEnProgreso(usuarioId);
            }
          } else {
            this.notificationService.error(
              'Error al iniciar la encuesta: ' +
                (error.error?.msg || 'Error desconocido')
            );
          }
        },
      });
  }

  /**
   * Obtiene la respuesta en progreso para el usuario actual
   */
  obtenerRespuestaEnProgreso(usuarioId: number): void {
    this.respuestaService
      .getRespuestaEnProgreso(usuarioId, this.encuestaId)
      .subscribe({
        next: (response) => {
          if (response && response.rpta === 1 && response.data) {
            console.log('Encontrada respuesta en progreso:', response.data);
            this.respuestaId = response.data.id;
            this.notificationService.info(
              'Continuando con la encuesta en progreso'
            );

            // Cargar las respuestas existentes en el formulario
            this.cargarRespuestasExistentes(response.data);

            // Iniciar temporizador si hay tiempo límite
            if (this.encuesta && this.encuesta.tiempoLimite) {
              this.tiempoRestante = this.encuesta.tiempoLimite * 60; // Convertir a segundos
              this.iniciarTemporizador();
            }
          } else {
            console.error('No se encontró ninguna respuesta en progreso');
            this.notificationService.error(
              'No se pudo encontrar la respuesta en progreso'
            );
          }
        },
        error: (error) => {
          console.error('Error al obtener respuesta en progreso:', error);
          this.notificationService.error(
            'Error al obtener la respuesta en progreso'
          );
        },
      });
  }

  /**
   * Carga las respuestas existentes en el formulario
   */
  cargarRespuestasExistentes(respuesta: any): void {
    if (!respuesta || !respuesta.detalles || !this.form) {
      return;
    }

    // Iterar sobre los detalles de respuesta y cargarlos en el formulario
    respuesta.detalles.forEach((detalle: any) => {
      const controlName = `pregunta_${detalle.preguntaId}`;
      const control = this.form.get(controlName);

      if (control) {
        if (detalle.opcionId) {
          control.setValue(detalle.opcionId.toString());
        } else if (detalle.respuestaTexto) {
          control.setValue(detalle.respuestaTexto);
        } else if (
          detalle.respuestaNumero !== null &&
          detalle.respuestaNumero !== undefined
        ) {
          control.setValue(detalle.respuestaNumero);
        } else if (detalle.respuestaFecha) {
          control.setValue(new Date(detalle.respuestaFecha));
        }
      }
    });
  }

  initForm(): void {
    this.form = this.fb.group({});

    // Crear controles para cada pregunta
    if (this.encuesta && this.encuesta.preguntas) {
      this.encuesta.preguntas.forEach((pregunta) => {
        switch (pregunta.tipo) {
          case TipoPregunta.OPCION_MULTIPLE:
          case TipoPregunta.SELECCION_MULTIPLE:
          case TipoPregunta.ESCALA_LIKERT:
            this.form.addControl(
              `pregunta_${pregunta.id}`,
              this.fb.control(
                null,
                pregunta.esObligatoria ? Validators.required : null
              )
            );
            break;
          case TipoPregunta.TEXTO_LIBRE:
            this.form.addControl(
              `pregunta_${pregunta.id}`,
              this.fb.control(
                '',
                pregunta.esObligatoria ? Validators.required : null
              )
            );
            break;
          case TipoPregunta.NUMERO:
            this.form.addControl(
              `pregunta_${pregunta.id}`,
              this.fb.control(
                null,
                pregunta.esObligatoria ? Validators.required : null
              )
            );
            break;
          case TipoPregunta.FECHA:
            this.form.addControl(
              `pregunta_${pregunta.id}`,
              this.fb.control(
                null,
                pregunta.esObligatoria ? Validators.required : null
              )
            );
            break;
        }
      });
    }
  }

  iniciarTemporizador(): void {
    this.tiempoInterval = setInterval(() => {
      if (this.tiempoRestante !== null) {
        this.tiempoRestante--;

        if (this.tiempoRestante <= 0) {
          clearInterval(this.tiempoInterval);
          this.notificationService.error(
            'Se ha agotado el tiempo para responder la encuesta'
          );
          this.router.navigate(['/encuestas']);
        }
      }
    }, 1000);
  }

  formatTiempo(): string {
    if (!this.tiempoRestante) return '';

    const minutos = Math.floor(this.tiempoRestante / 60);
    const segundos = this.tiempoRestante % 60;

    return `${minutos.toString().padStart(2, '0')}:${segundos
      .toString()
      .padStart(2, '0')}`;
  }

  onSubmit(): void {
    // Verificar si el formulario está inicializado
    if (!this.form) {
      console.error('El formulario no está inicializado');
      this.notificationService.error(
        'Error en el formulario. Por favor, recargue la página.'
      );
      return;
    }

    // Verificar si tenemos un ID de respuesta válido
    if (!this.respuestaId) {
      console.error('No hay ID de respuesta');

      // Intentar iniciar la respuesta nuevamente
      const usuarioId = this.generalService.getUserId();
      this.notificationService.info('Iniciando encuesta...');

      this.respuestaService
        .iniciar({
          encuestaId: this.encuestaId,
          usuarioId: usuarioId || undefined,
        })
        .subscribe({
          next: (response) => {
            if (response && response.data) {
              this.respuestaId = response.data.id;
              this.notificationService.success(
                'Encuesta iniciada correctamente, intente enviar nuevamente'
              );
            } else {
              this.notificationService.error(
                'Error al iniciar la encuesta. Por favor, recargue la página.'
              );
            }
          },
          error: (error) => {
            console.error('Error al reintentar iniciar respuesta:', error);
            this.notificationService.error(
              'Error al iniciar la encuesta. Por favor, recargue la página.'
            );
          },
        });

      return;
    }

    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.notificationService.error(
        'Por favor, complete todos los campos obligatorios'
      );
      return;
    }

    this.submitting = true;

    // Guardar las respuestas antes de finalizar
    const respuestas: any[] = [];

    if (this.encuesta && this.encuesta.preguntas) {
      this.encuesta.preguntas.forEach((pregunta) => {
        const controlName = `pregunta_${pregunta.id}`;
        const valor = this.form.get(controlName)?.value;

        if (valor !== null && valor !== undefined) {
          let detalleRespuesta: any = {
            respuestaEncuestaUsuarioId: this.respuestaId,
            preguntaId: pregunta.id,
          };

          switch (pregunta.tipo) {
            case TipoPregunta.OPCION_MULTIPLE:
            case TipoPregunta.ESCALA_LIKERT:
              // Convertir el valor de string a número para el backend
              detalleRespuesta.opcionId =
                typeof valor === 'string' ? parseInt(valor, 10) : valor;
              break;
            case TipoPregunta.SELECCION_MULTIPLE:
              // Para selección múltiple, enviamos solo la primera opción seleccionada
              // ya que el backend no soporta múltiples opciones en una sola respuesta
              if (Array.isArray(valor) && valor.length > 0) {
                // Convertir el valor de string a número para el backend si es necesario
                detalleRespuesta.opcionId =
                  typeof valor[0] === 'string'
                    ? parseInt(valor[0], 10)
                    : valor[0];

                // Si hay más opciones seleccionadas, enviamos una respuesta por cada opción
                if (valor.length > 1) {
                  for (let i = 1; i < valor.length; i++) {
                    const opcionId =
                      typeof valor[i] === 'string'
                        ? parseInt(valor[i], 10)
                        : valor[i];
                    const respuestaAdicional = {
                      respuestaEncuestaUsuarioId: this.respuestaId,
                      preguntaId: pregunta.id,
                      opcionId: opcionId,
                    };

                    const indexAdicional = respuestas.length;
                    respuestas[indexAdicional] = this.respuestaService
                      .responderPregunta(this.respuestaId, respuestaAdicional)
                      .pipe(
                        catchError((error) => {
                          console.error(
                            `Error al guardar respuesta adicional para pregunta ${pregunta.id}:`,
                            error
                          );
                          return of(null);
                        })
                      );
                  }
                }
              } else {
                // Convertir el valor de string a número para el backend si es necesario
                detalleRespuesta.opcionId =
                  typeof valor === 'string' ? parseInt(valor, 10) : valor;
              }
              break;
            case TipoPregunta.TEXTO_LIBRE:
              detalleRespuesta.respuestaTexto = valor;
              break;
            case TipoPregunta.NUMERO:
              detalleRespuesta.respuestaNumero = valor;
              break;
            case TipoPregunta.FECHA:
              // Convertir fecha a formato ISO si es necesario
              if (valor instanceof Date) {
                detalleRespuesta.respuestaFecha = valor.toISOString();
              } else {
                detalleRespuesta.respuestaFecha = valor;
              }
              break;
          }

          // Guardar la respuesta
          const index = respuestas.length;
          respuestas[index] = this.respuestaService
            .responderPregunta(this.respuestaId, detalleRespuesta)
            .pipe(
              catchError((error) => {
                console.error(
                  `Error al guardar respuesta para pregunta ${pregunta.id}:`,
                  error
                );
                return of(null); // Continuar con las demás respuestas incluso si una falla
              })
            );
        }
      });
    }

    // Si no hay respuestas para guardar, finalizar directamente
    if (respuestas.length === 0) {
      this.finalizarEncuesta();
      return;
    }

    // Esperar a que todas las respuestas se guarden antes de finalizar
    // Convertir el array a un objeto para forkJoin
    const respuestasObj: { [key: string]: any } = {};
    respuestas.forEach((resp, index) => {
      respuestasObj[`resp${index}`] = resp;
    });

    forkJoin(respuestasObj).subscribe({
      next: (results) => {
        console.log('Respuestas guardadas:', results);
        this.finalizarEncuesta();
      },
      error: (error) => {
        console.error('Error al guardar respuestas:', error);
        this.notificationService.error('Error al guardar las respuestas');
        this.submitting = false;
      },
    });
  }

  /**
   * Finaliza la encuesta enviando la solicitud al servidor
   */
  finalizarEncuesta(): void {
    // Verificar que tengamos un ID de respuesta válido
    if (!this.respuestaId) {
      console.error('No hay ID de respuesta para finalizar');
      this.notificationService.error(
        'Error al finalizar la encuesta: No hay ID de respuesta'
      );
      this.submitting = false;
      return;
    }

    console.log('Finalizando encuesta con ID de respuesta:', this.respuestaId);

    this.respuestaService.finalizar(this.respuestaId).subscribe({
      next: (response) => {
        console.log('Encuesta finalizada correctamente:', response);
        this.notificationService.success('Encuesta completada correctamente');
        this.router.navigate(['/encuestas']);
      },
      error: (error) => {
        console.error('Error al finalizar encuesta:', error);
        this.notificationService.error(
          'Error al finalizar la encuesta: ' +
            (error.error?.message || 'Error desconocido')
        );
        this.submitting = false;
      },
    });
  }

  /**
   * Verifica si una opción está seleccionada para una pregunta de selección múltiple
   */
  isOptionSelected(preguntaId: number, opcionId: number): boolean {
    const selectedOpts = this.selectedOptions.get(preguntaId);
    return selectedOpts ? selectedOpts.includes(opcionId) : false;
  }

  /**
   * Maneja el cambio en un checkbox de selección múltiple
   */
  onCheckboxChange(
    preguntaId: number,
    opcionId: number,
    isChecked: boolean
  ): void {
    // Inicializar el array si no existe
    if (!this.selectedOptions.has(preguntaId)) {
      this.selectedOptions.set(preguntaId, []);
    }

    const selectedOpts = this.selectedOptions.get(preguntaId) || [];

    if (isChecked) {
      // Agregar la opción si está marcada
      if (!selectedOpts.includes(opcionId)) {
        selectedOpts.push(opcionId);
      }
    } else {
      // Quitar la opción si está desmarcada
      const index = selectedOpts.indexOf(opcionId);
      if (index !== -1) {
        selectedOpts.splice(index, 1);
      }
    }

    // Actualizar el mapa y el formulario
    this.selectedOptions.set(preguntaId, selectedOpts);

    // Verificar que el formulario exista antes de actualizar el valor
    if (this.form) {
      const control = this.form.get(`pregunta_${preguntaId}`);
      if (control) {
        control.setValue(selectedOpts.length > 0 ? selectedOpts : null);
      }
    }
  }
}
