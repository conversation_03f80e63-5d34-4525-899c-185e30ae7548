package com.midas.crm.entity.DTO.user;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class UserDTO {
    private Long id;
    private String username;
    private String nombre;
    private String apellido;
    private String dni;
    private String telefono;
    private String email;
    private LocalDateTime fechaCreacion;
    private LocalDate fechaCese;
    private String estado;
    private Role role;
    private String sede; // Este es el campo sedeNombre
    private Long sede_id; // Cambiado de sedeId a sede_id para coincidir con el frontend
    private UserCoordinadorDTO coordinador; // Información del coordinador

    // Constructor sin coordinador (para compatibilidad con código existente)
    public UserDTO(Long id, String username, String nombre, String apellido, String dni,
                  String telefono, String email, LocalDateTime fechaCreacion,
                  LocalDate fechaCese, String estado, Role role, String sede, Long sede_id) {
        this.id = id;
        this.username = username;
        this.nombre = nombre;
        this.apellido = apellido;
        this.dni = dni;
        this.telefono = telefono;
        this.email = email;
        this.fechaCreacion = fechaCreacion;
        this.fechaCese = fechaCese;
        this.estado = estado;
        this.role = role;
        this.sede = sede;
        this.sede_id = sede_id;
        this.coordinador = null;
    }

    // Constructor con coordinador
    public UserDTO(Long id, String username, String nombre, String apellido, String dni,
                  String telefono, String email, LocalDateTime fechaCreacion,
                  LocalDate fechaCese, String estado, Role role, String sede, Long sede_id,
                  UserCoordinadorDTO coordinador) {
        this.id = id;
        this.username = username;
        this.nombre = nombre;
        this.apellido = apellido;
        this.dni = dni;
        this.telefono = telefono;
        this.email = email;
        this.fechaCreacion = fechaCreacion;
        this.fechaCese = fechaCese;
        this.estado = estado;
        this.role = role;
        this.sede = sede;
        this.sede_id = sede_id;
        this.coordinador = coordinador;
    }
}