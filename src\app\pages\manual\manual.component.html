<div class="w-full px-4 sm:px-6 lg:px-10 xl:px-20 py-6">
  <div
    class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6 sm:p-8 transition-all"
  >
    <!-- Cabecera -->
    <div
      class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6"
    >
      <div>
        <h2 class="text-xl font-semibold text-blue-600">Lista de manuales</h2>
        <p class="text-sm text-gray-500 dark:text-gray-300 mt-1">
          Manuales registrados hasta el momento
        </p>
      </div>
      <div
        class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full sm:w-auto"
      >
        <input
          type="text"
          placeholder="Buscar manual..."
          class="w-full sm:w-64 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-800 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
          (input)="searchChange(getInputValue($event))"
        />
        <button
          *ngIf="allowAddNew"
          (click)="openModal()"
          class="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          Agregar manual
        </button>
        <button
          (click)="getPageRefresh()"
          class="flex items-center gap-2 bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 text-white px-4 py-2 rounded-md font-medium transition shadow"
          title="Actualizar"
        >
          <!-- Ícono: Heroicon "arrow-path" (actualizar) -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M4.5 12a7.5 7.5 0 0112.772-5.303M19.5 12a7.5 7.5 0 01-12.772 5.303M15 9h3.75V5.25M9 15H5.25v3.75"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Indicador de carga -->
    <div
      *ngIf="loadingData"
      class="flex items-center mx-auto my-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-100 dark:border-blue-800 rounded-md max-w-md"
    >
      <mat-spinner [diameter]="24" color="primary" class="mr-3"></mat-spinner>
      <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
        Cargando manuales...
      </span>
    </div>

    <!-- Tabla -->
    <div class="overflow-x-auto rounded-xl shadow">
      <table
        class="min-w-full border border-gray-200 dark:border-gray-700 text-sm text-left text-gray-700 dark:text-white"
      >
        <thead
          class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-white uppercase text-xs"
        >
          <tr>
            <th class="px-6 py-3">N°</th>
            <th class="px-6 py-3">Nombre</th>
            <th class="px-6 py-3">Tipo</th>
            <th class="px-6 py-3">Archivo</th>
            <th class="px-6 py-3">Estado</th>
            <th class="px-6 py-3">Creado</th>
            <th class="px-6 py-3">Modificado</th>
            <th class="px-6 py-3 text-center">Acciones</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-100 dark:divide-gray-700">
          <tr
            *ngFor="let item of paginationResult.data"
            class="bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <td class="px-6 py-4">{{ item.index }}</td>
            <td class="px-6 py-4">{{ item.nombre }}</td>
            <td class="px-6 py-4">{{ item.tipoText }}</td>
            <td class="px-6 py-4">
              <a
                [href]="item.archivo"
                target="_blank"
                class="flex items-center text-blue-600 dark:text-blue-400 hover:underline"
              >
                <i
                  class="fa-solid fa-file-pdf text-red-600 dark:text-red-400 mr-2"
                ></i>
                Ver archivo
              </a>
            </td>

            <td class="px-6 py-4">
              <span
                [ngClass]="
                  item.isActive
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                "
                class="px-2 py-1 text-xs font-semibold rounded-full"
              >
                {{ item.isActive ? "Activo" : "Inactivo" }}
              </span>
            </td>
            <td class="px-6 py-4">
              {{ item.createdAt | date : "dd/MM/yyyy HH:mm" }}
            </td>
            <td class="px-6 py-4">
              {{ item.updatedAt | date : "dd/MM/yyyy HH:mm" }}
            </td>
            <td class="px-6 py-4 text-center space-x-2">
              <!-- Botón Editar -->
              <button
                *ngIf="allowEditing"
                (click)="editDataGet(item.id)"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition"
                title="Editar"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 inline"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M15.232 5.232l3.536 3.536M9 11l6-6 3 3-6 6H9v-3zM4 20h16"
                  />
                </svg>
              </button>

              <!-- Botón Eliminar -->
              <button
                *ngIf="allowDelete"
                (click)="deleteRow(item.id)"
                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition"
                title="Eliminar"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 inline"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </td>
          </tr>

          <!-- Fila vacía -->
          <tr
            *ngIf="paginationResult.data.length === 0 && !loadingData"
            class="bg-white dark:bg-gray-900"
          >
            <td
              colspan="8"
              class="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
            >
              No se encontraron manuales. Puedes agregar uno nuevo usando el
              botón Agregar manual.
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Paginación -->
    <div
      class="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4"
    >
      <div>
        <label class="text-sm text-gray-700 dark:text-gray-300 mr-2"
          >Items por página:</label
        >
        <select
          class="px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white"
          (change)="perPageChange(getSelectValue($event))"
        >
          <option *ngFor="let count of countElements" [value]="count">
            {{ count }}
          </option>
        </select>
      </div>
      <div class="flex gap-2">
        <button
          class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          [disabled]="paginationResult.currentPage === 1"
          (click)="pageChange(paginationResult.currentPage - 1)"
        >
          Anterior
        </button>
        <span class="text-sm pt-1 block text-gray-800 dark:text-white">
          Página {{ paginationResult.currentPage }} de
          {{ paginationResult.lastPage }}
        </span>
        <button
          class="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded text-sm bg-white dark:bg-gray-800 text-gray-800 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          [disabled]="
            paginationResult.currentPage === paginationResult.lastPage
          "
          (click)="pageChange(paginationResult.currentPage + 1)"
        >
          Siguiente
        </button>
      </div>
    </div>
  </div>
</div>
