<div class="container mx-auto p-4">
  <div *ngIf="loading" class="flex justify-center my-8">
    <mat-spinner></mat-spinner>
  </div>

  <div
    *ngIf="error"
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded my-4"
  >
    Error al cargar la encuesta. Por favor, intente nuevamente.
  </div>

  <div *ngIf="encuesta && !loading && !error">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">
        {{ encuesta.titulo }}
      </h1>

      <div class="flex gap-2">
        <button
          mat-raised-button
          color="accent"
          [routerLink]="['/encuestas']"
          class="bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-700 dark:hover:bg-blue-800 px-4 py-2 rounded-md transition-colors flex items-center gap-2"
        >
          <mat-icon class="text-white">arrow_back</mat-icon>
          <span>Volver</span>
        </button>
      </div>
    </div>

    <div
      class="bg-blue-100 dark:bg-blue-900 border border-blue-400 dark:border-blue-800 text-blue-700 dark:text-blue-300 px-4 py-3 rounded mb-4 flex items-center"
    >
      <mat-icon class="mr-2">info</mat-icon>
      <div>
        <p class="font-medium">Modo de visualización</p>
        <p class="text-sm">
          Esta vista es solo para visualizar la estructura de la encuesta. Para
          responder, utilice el botón "RESPONDER" desde el listado de encuestas.
        </p>
      </div>
    </div>

    <mat-card class="mb-6 dark:bg-gray-800">
      <mat-card-content>
        <p class="dark:text-gray-300 mb-4">{{ encuesta.descripcion }}</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div *ngIf="encuesta.fechaInicio" class="flex items-center gap-2">
            <mat-icon class="text-gray-500 dark:text-gray-400">event</mat-icon>
            <span class="dark:text-gray-300"
              >Fecha de inicio:
              {{ encuesta.fechaInicio | date : "dd/MM/yyyy" }}</span
            >
          </div>

          <div *ngIf="encuesta.fechaFin" class="flex items-center gap-2">
            <mat-icon class="text-gray-500 dark:text-gray-400"
              >event_busy</mat-icon
            >
            <span class="dark:text-gray-300"
              >Fecha de fin: {{ encuesta.fechaFin | date : "dd/MM/yyyy" }}</span
            >
          </div>

          <div *ngIf="encuesta.tiempoLimite" class="flex items-center gap-2">
            <mat-icon class="text-gray-500 dark:text-gray-400">timer</mat-icon>
            <span class="dark:text-gray-300"
              >Tiempo límite: {{ encuesta.tiempoLimite }} minutos</span
            >
          </div>

          <div class="flex items-center gap-2">
            <mat-icon class="text-gray-500 dark:text-gray-400"
              >visibility</mat-icon
            >
            <span class="dark:text-gray-300"
              >Anónima: {{ encuesta.esAnonima ? "Sí" : "No" }}</span
            >
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <h2 class="text-xl font-bold text-gray-800 dark:text-white mb-4">
      Preguntas
    </h2>

    <div
      *ngIf="encuesta.preguntas && encuesta.preguntas.length > 0"
      class="space-y-4"
    >
      <mat-card
        *ngFor="let pregunta of encuesta.preguntas; let i = index"
        class="dark:bg-gray-800"
      >
        <mat-card-header>
          <mat-card-title class="dark:text-white"
            >{{ i + 1 }}. {{ pregunta.enunciado }}</mat-card-title
          >
          <mat-card-subtitle
            *ngIf="pregunta.descripcion"
            class="dark:text-gray-300"
          >
            {{ pregunta.descripcion }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content class="mt-4">
          <div [ngSwitch]="pregunta.tipo">
            <!-- Opciones múltiples -->
            <div *ngSwitchCase="'OPCION_MULTIPLE'" class="ml-4">
              <div *ngFor="let opcion of pregunta.opciones" class="mb-2">
                <mat-radio-button [disabled]="true" class="dark:text-gray-300">
                  {{ opcion.texto }}
                </mat-radio-button>
              </div>
            </div>

            <!-- Selección múltiple -->
            <div *ngSwitchCase="'SELECCION_MULTIPLE'" class="ml-4">
              <div *ngFor="let opcion of pregunta.opciones" class="mb-2">
                <mat-checkbox [disabled]="true" class="dark:text-gray-300">
                  {{ opcion.texto }}
                </mat-checkbox>
              </div>
            </div>

            <!-- Escala Likert -->
            <div *ngSwitchCase="'ESCALA_LIKERT'" class="ml-4">
              <div class="flex justify-between">
                <div
                  *ngFor="let opcion of pregunta.opciones"
                  class="text-center"
                >
                  <div class="mb-2">
                    <mat-radio-button
                      [disabled]="true"
                      class="dark:text-gray-300"
                    ></mat-radio-button>
                  </div>
                  <div class="text-sm dark:text-gray-300">
                    {{ opcion.texto }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Texto libre -->
            <div *ngSwitchCase="'TEXTO_LIBRE'" class="ml-4">
              <mat-form-field appearance="outline" class="w-full">
                <mat-label>Respuesta</mat-label>
                <textarea
                  matInput
                  [disabled]="true"
                  rows="3"
                  placeholder="Escriba su respuesta aquí"
                  class="dark:bg-gray-700 dark:text-white"
                ></textarea>
              </mat-form-field>
            </div>

            <!-- Fecha -->
            <div *ngSwitchCase="'FECHA'" class="ml-4">
              <mat-form-field appearance="outline">
                <mat-label>Fecha</mat-label>
                <input
                  matInput
                  [matDatepicker]="picker"
                  [disabled]="true"
                  class="dark:bg-gray-700 dark:text-white"
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="picker"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
              </mat-form-field>
            </div>

            <!-- Número -->
            <div *ngSwitchCase="'NUMERO'" class="ml-4">
              <mat-form-field appearance="outline">
                <mat-label>Número</mat-label>
                <input
                  matInput
                  type="number"
                  [disabled]="true"
                  class="dark:bg-gray-700 dark:text-white"
                />
              </mat-form-field>
            </div>
          </div>

          <div
            *ngIf="pregunta.esObligatoria"
            class="mt-2 text-sm text-red-500 dark:text-red-400"
          >
            * Pregunta obligatoria
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <div
      *ngIf="!encuesta.preguntas || encuesta.preguntas.length === 0"
      class="p-6 text-center text-gray-500 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg"
    >
      Esta encuesta no tiene preguntas.
    </div>
  </div>
</div>
