<div class="p-2 md:p-3 bg-gradient-to-br min-h-screen">
  <!-- C<PERSON><PERSON><PERSON> con título y botones de acción -->
  <mat-card class="mb-1 rounded-lg shadow-sm bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 overflow-hidden">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 p-2 md:p-3">
      <div class="flex-1 min-w-[180px]">
        <h1 class="text-lg font-semibold text-gray-800 dark:text-white m-0 flex items-center">
          <span class="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 p-1 rounded-lg mr-1.5">
            <mat-icon class="text-lg">school</mat-icon>
          </span>
          Cursos disponibles
        </h1>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">Explora y gestiona los cursos de formación</p>
      </div>

      <div class="flex flex-col sm:flex-row items-center gap-1.5 w-full md:w-auto">
        <div class="relative flex items-center w-full sm:w-56 h-8 bg-gray-50 dark:bg-gray-700/60 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 focus-within:border-blue-400 dark:focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-100 dark:focus-within:ring-blue-900/30 transition-all duration-200">
          <div class="flex items-center justify-center w-8 h-8 text-gray-400 dark:text-gray-500">
            <mat-icon class="text-base">search</mat-icon>
          </div>
          <input
            type="text"
            [(ngModel)]="searchTerm"
            placeholder="Buscar cursos"
            class="flex-1 h-full border-none bg-transparent text-sm text-gray-700 dark:text-gray-200 outline-none placeholder:text-gray-400 dark:placeholder:text-gray-500"
          >
          <button
            *ngIf="searchTerm"
            class="flex items-center justify-center w-7 h-7 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 bg-transparent border-none cursor-pointer transition-colors"
            (click)="searchTerm = ''"
            matTooltip="Limpiar búsqueda"
          >
            <mat-icon class="text-sm">close</mat-icon>
          </button>
        </div>

        <div class="flex items-center gap-1.5 w-full sm:w-auto">
          <button
            mat-flat-button
            color="primary"
            class="h-8 px-2 rounded-lg shadow-sm flex-1 sm:flex-none font-medium text-sm transition-all duration-200 hover:shadow"
            (click)="onCreateCurso()"
          >
            <mat-icon class="mr-1 text-base">add</mat-icon>
            <span class="hidden sm:inline">Nuevo Curso</span>
          </button>

          <button
            mat-icon-button
            class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors h-8 w-8"
            (click)="loadCursos()"
            matTooltip="Actualizar lista"
          >
            <mat-icon class="text-base">refresh</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </mat-card>

  <!-- Indicador de carga -->
  <div class="flex justify-center items-center min-h-[150px]" *ngIf="loading">
    <app-spinner></app-spinner>
  </div>

  <!-- Mensaje de error -->
  <div class="flex justify-center items-center min-h-[150px]" *ngIf="error">
    <mat-card class="p-4 text-center max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-100 dark:border-red-900/50">
      <div class="bg-red-50 dark:bg-red-900/20 rounded-full p-2 w-12 h-12 mx-auto mb-2 flex items-center justify-center">
        <mat-icon color="warn" class="text-2xl h-8 w-8">error</mat-icon>
      </div>
      <p class="mb-3 text-gray-700 dark:text-gray-300 text-sm">{{ error }}</p>
      <button mat-flat-button color="primary" (click)="loadCursos()" class="rounded-lg px-3 py-1 text-sm">Reintentar</button>
    </mat-card>
  </div>

  <!-- Lista de cursos -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 mt-3" *ngIf="!loading && !error && filterCursos().length > 0">
    <mat-card
      class="flex flex-col rounded-lg overflow-hidden shadow-sm hover:shadow-md group bg-white dark:bg-gray-800 h-full border border-gray-100 dark:border-gray-700 transition-all duration-300 hover:-translate-y-0.5"
      *ngFor="let curso of filterCursos()"
    >
      <!-- Video del curso si existe -->
      <div class="w-full relative" *ngIf="curso.videoUrl">
        <app-curso-video-player [videoUrl]="curso.videoUrl" [title]="curso.nombre"></app-curso-video-player>
        <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <!-- Imagen por defecto si no hay video -->
      <div class="w-full relative" *ngIf="!curso.videoUrl">
        <div class="w-full relative" style="padding-top: 52%;">
          <img [src]="cursoPlaceholderUrl" alt="Imagen del curso" class="absolute top-0 left-0 w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500">
          <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
      </div>

      <div class="px-3 pt-2 pb-0">
        <div class="flex flex-row items-start justify-between gap-1.5">
          <div class="flex-1 min-w-0">
            <h3 class="text-sm font-medium mb-0.5 text-gray-800 dark:text-white line-clamp-2 leading-tight">
              {{ curso.nombre }}
            </h3>
          </div>
          <div *ngIf="curso.estado" class="flex-shrink-0">
            <span
              class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium whitespace-nowrap"
              [ngClass]="{
                'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300': curso.estado === 'A',
                'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300': curso.estado === 'I'
              }"
            >
              {{ curso.estado === 'A' ? 'Activo' : 'Inactivo' }}
            </span>
          </div>
        </div>
      </div>

      <mat-card-content class="flex-1 flex flex-col px-3 py-1.5">
        <p class="text-xs text-gray-600 dark:text-gray-400 my-1 line-clamp-2 overflow-hidden">{{ curso.descripcion }}</p>

        <div class="mt-2 pt-1.5 border-t border-gray-100 dark:border-gray-700/50">
          <div class="flex items-center mb-1 text-xs" *ngIf="curso.fechaInicio">
            <mat-icon class="text-xs h-3.5 w-3.5 mr-1 text-gray-400 dark:text-gray-500">event</mat-icon>
            <span class="text-gray-600 dark:text-gray-400">Inicio: {{ curso.fechaInicio | arrayToDate | date:'dd/MM/yyyy' }}</span>
          </div>

          <div class="flex items-center mb-1 text-xs" *ngIf="curso.fechaFin">
            <mat-icon class="text-xs h-3.5 w-3.5 mr-1 text-gray-400 dark:text-gray-500">event_busy</mat-icon>
            <span class="text-gray-600 dark:text-gray-400">Fin: {{ curso.fechaFin | arrayToDate | date:'dd/MM/yyyy' }}</span>
          </div>

          <div class="flex items-center mb-1 text-xs" *ngIf="curso.usuario">
            <mat-icon class="text-xs h-3.5 w-3.5 mr-1 text-gray-400 dark:text-gray-500">person</mat-icon>
            <span class="text-gray-600 dark:text-gray-400">Instructor: {{ curso.usuario.nombre }} {{ curso.usuario.apellido }}</span>
          </div>
        </div>
      </mat-card-content>

      <mat-card-actions class="p-0 m-0 flex flex-col border-t border-gray-100 dark:border-gray-700/50">
        <div class="flex justify-between p-1 border-b border-gray-50 dark:border-gray-700/30">
          <button
            mat-button
            color="primary"
            (click)="onViewCurso(curso.id)"
            matTooltip="Ver detalles del curso"
            class="flex-1 mx-0.5 min-w-0 px-1 text-xs h-7 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
          >
            <mat-icon class="text-sm h-4 w-4 mr-0.5">visibility</mat-icon> Ver
          </button>

          <button
            mat-button
            color="accent"
            (click)="onEditCurso(curso.id)"
            matTooltip="Editar curso"
            class="flex-1 mx-0.5 min-w-0 px-1 text-xs h-7 rounded-md hover:bg-amber-50 dark:hover:bg-amber-900/20 transition-colors"
          >
            <mat-icon class="text-sm h-4 w-4 mr-0.5">edit</mat-icon> Editar
          </button>

          <button
            mat-button
            color="warn"
            (click)="onDeleteCurso(curso.id)"
            matTooltip="Eliminar curso"
            class="flex-1 mx-0.5 min-w-0 px-1 text-xs h-7 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
          >
            <mat-icon class="text-sm h-4 w-4 mr-0.5">delete</mat-icon>
          </button>
        </div>

        <div class="flex justify-between p-1 bg-gray-50/80 dark:bg-gray-700/30">
          <button
            mat-button
            color="primary"
            (click)="onAsignarUsuarios(curso)"
            matTooltip="Asignar usuarios al curso"
            class="flex-1 mx-0.5 min-w-0 px-1 text-xs h-7 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors focus:outline-none focus:ring-0 active:bg-blue-100 dark:active:bg-blue-900/30"
          >
            <mat-icon class="text-sm h-4 w-4 mr-0.5">group_add</mat-icon> Asignar
          </button>

          <button
            mat-button
            color="primary"
            (click)="onVerAlumnos(curso)"
            matTooltip="Ver alumnos asignados"
            class="flex-1 mx-0.5 min-w-0 px-1 text-xs h-7 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors focus:outline-none focus:ring-0 active:bg-blue-100 dark:active:bg-blue-900/30"
          >
            <mat-icon class="text-sm h-4 w-4 mr-0.5">people</mat-icon> Ver Alumnos
          </button>
        </div>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Mensaje cuando no hay cursos -->
  <div class="flex justify-center items-center min-h-[150px]" *ngIf="!loading && !error && filterCursos().length === 0">
    <mat-card class="p-4 text-center max-w-sm bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-full p-2 w-12 h-12 mx-auto mb-2 flex items-center justify-center">
        <mat-icon class="text-2xl h-8 w-8 text-blue-500 dark:text-blue-400">school</mat-icon>
      </div>
      <h2 class="text-base font-medium mb-1 text-gray-800 dark:text-white">No hay cursos disponibles</h2>
      <p *ngIf="searchTerm" class="mb-3 text-gray-500 dark:text-gray-400 text-xs">No se encontraron cursos que coincidan con "{{ searchTerm }}"</p>
      <p *ngIf="!searchTerm" class="mb-3 text-gray-500 dark:text-gray-400 text-xs">Aún no se han creado cursos en el sistema</p>
      <button mat-flat-button color="primary" (click)="onCreateCurso()" class="rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-0 active:shadow-none">
        <mat-icon class="mr-1 text-base">add</mat-icon> Crear primer curso
      </button>
    </mat-card>
  </div>
</div>
