package com.midas.crm.controller;

import com.midas.crm.entity.DTO.cuestionario.RespuestaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUpdateDTO;
import com.midas.crm.service.RespuestaService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.respuesta}")
@RequiredArgsConstructor
@Slf4j
public class RespuestaController {

    private final RespuestaService respuestaService;

    /**
     * Crea una nueva respuesta
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<RespuestaDTO>> createRespuesta(@Valid @RequestBody RespuestaCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(respuestaService::createRespuesta)
            .map(respuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta creada exitosamente", respuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las respuestas
     * Implementado con programación funcional
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<RespuestaDTO>>> listRespuestas() {
        List<RespuestaDTO> respuestas = respuestaService.listRespuestas();
        return ResponseEntity.ok(
            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de respuestas", respuestas)
        );
    }

    /**
     * Obtiene las respuestas por pregunta
     * Implementado con programación funcional
     */
    @GetMapping("/pregunta/{preguntaId}")
    public ResponseEntity<GenericResponse<List<RespuestaDTO>>> listRespuestasByPreguntaId(@PathVariable Long preguntaId) {
        return Optional.ofNullable(preguntaId)
            .map(respuestaService::listRespuestasByPreguntaId)
            .map(respuestas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Listado de respuestas por pregunta", respuestas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una respuesta por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<RespuestaDTO>> getRespuesta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(respuestaService::getRespuestaById)
            .map(respuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta encontrada", respuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Actualiza una respuesta existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<RespuestaDTO>> updateRespuesta(@PathVariable Long id, @Valid @RequestBody RespuestaUpdateDTO dto) {
        return Optional.ofNullable(dto)
            .map(updateDto -> respuestaService.updateRespuesta(id, updateDto))
            .map(respuesta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta actualizada exitosamente", respuesta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina una respuesta
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Object>> deleteRespuesta(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().build();
        }

        respuestaService.deleteRespuesta(id);
        return ResponseEntity.ok(
            new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta eliminada exitosamente", null)
        );
    }
}
