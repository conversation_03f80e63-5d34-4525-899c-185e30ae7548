<div class="w-full">
  <!-- C<PERSON>cera con título y controles -->
  <div class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6 mb-6">
    <div
      class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6"
    >
      <div>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
          Rendimiento de Leads por Asesor
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Top asesores por cantidad de leads generados
        </p>
      </div>

      <!-- Controles de filtro -->
      <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
        <!-- Selector de período -->
        <div class="min-w-[140px]">
          <label
            class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Período
          </label>
          <select
            [value]="periodoControl.value"
            (change)="onPeriodoChange($event)"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option *ngFor="let periodo of periodos" [value]="periodo.value">
              {{ periodo.label }}
            </option>
          </select>
        </div>

        <!-- Selector de sede -->
        <div class="min-w-[160px]">
          <label
            class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Sede
          </label>
          <select
            [value]="sedeControl.value"
            (change)="onSedeChange($event)"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="todas">Todas las sedes</option>
            <option *ngFor="let sede of sedes" [value]="sede.id">
              {{ sede.nombre }}
            </option>
          </select>
        </div>

        <!-- Selector de supervisor -->
        <div class="min-w-[160px]">
          <label
            class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Supervisor
          </label>
          <select
            [value]="supervisorControl.value"
            (change)="onSupervisorChange($event)"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            [disabled]="supervisores.length === 0"
          >
            <option value="todos">Todos los supervisores</option>
            <option
              *ngFor="let supervisor of supervisores"
              [value]="supervisor.id"
            >
              {{ supervisor.nombre }}
            </option>
          </select>
        </div>

        <!-- Selector de fecha -->
        <div class="min-w-[140px]">
          <label
            class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            Fecha
          </label>
          <input
            type="date"
            [value]="fechaControl.value"
            (change)="onFechaChange($event)"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <!-- Botón de refrescar -->
        <div class="flex items-end">
          <button
            type="button"
            (click)="refrescarDatos()"
            [disabled]="loading"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white text-sm font-medium rounded-md transition-colors duration-200 flex items-center gap-2"
          >
            <svg
              class="w-4 h-4"
              [class.animate-spin]="loading"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Actualizar
          </button>
        </div>
      </div>
    </div>

    <!-- Contadores de resumen -->
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
      <!-- Total de leads -->
      <div
        class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-blue-100 text-sm font-medium">Total Leads</p>
            <p class="text-2xl font-bold">{{ totalLeadsHoy }}</p>
          </div>
          <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- Total de asesores activos -->
      <div
        class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-green-100 text-sm font-medium">Asesores Activos</p>
            <p class="text-2xl font-bold">{{ totalAsesoresActivos }}</p>
          </div>
          <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
              />
            </svg>
          </div>
        </div>
      </div>

      <!-- Promedio de leads por asesor -->
      <div
        class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-purple-100 text-sm font-medium">
              Promedio por Asesor
            </p>
            <p class="text-2xl font-bold">{{ promedioLeadsPorAsesor }}</p>
          </div>
          <div class="bg-purple-400 bg-opacity-30 rounded-full p-3">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
              />
              <path
                d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Gráfico de rendimiento -->
  <div class="bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6">
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200">
        Top 10 Asesores - {{ getPeriodoLabel() }}
      </h4>

      <!-- Indicador de carga -->
      <div *ngIf="loading" class="flex items-center gap-2 text-gray-500">
        <svg
          class="animate-spin w-4 h-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        <span class="text-sm">Cargando...</span>
      </div>
    </div>

    <!-- Contenedor del gráfico -->
    <div class="relative h-[400px] w-full">
      <canvas #chartRendimiento></canvas>

      <!-- Mensaje cuando no hay datos -->
      <div
        *ngIf="!loading && rendimientoData.length === 0"
        class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg"
      >
        <div class="text-center">
          <svg
            class="mx-auto w-12 h-12 text-gray-400 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          <p class="text-gray-500 dark:text-gray-400">
            No hay datos disponibles para mostrar
          </p>
          <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">
            Intenta ajustar los filtros o la fecha seleccionada
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
