package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "video_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "leccion_id", nullable = false, unique = true)
    private Leccion leccion;

    @Column(name = "formato", length = 20)
    private String formato; // mp4, webm, etc.

    @Column(name = "resolucion", length = 20)
    private String resolucion; // 720p, 1080p, etc.

    @Column(name = "tamano_bytes")
    private Long tamanoBytes;

    @Column(name = "duracion_segundos")
    private Integer duracionSegundos;

    @Column(name = "fecha_subida")
    private LocalDateTime fechaSubida;

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;
}
