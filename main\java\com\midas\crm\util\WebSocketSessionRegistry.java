package com.midas.crm.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WebSocketSessionRegistry {
    private final Map<String, Set<String>> userSessionIds = new ConcurrentHashMap<>();
    private final Map<String, Object> sessions = new ConcurrentHashMap<>();

    public void registerSession(String userId, String sessionId) {
        // Si ya existe una sesión para este usuario, cerrar las sesiones anteriores
        Set<String> userSessions = userSessionIds.computeIfAbsent(userId, k -> ConcurrentHashMap.newKeySet());

        // Si ya hay una sesión activa, cerrar las sesiones anteriores
        if (!userSessions.isEmpty()) {
            // Mantener solo la sesión más reciente
            for (String existingSessionId : userSessions) {
                if (!existingSessionId.equals(sessionId)) {
                    sessions.remove(existingSessionId);
                    log.info("Cerrando sesión WebSocket duplicada para usuario: {}, sessionId: {}", userId, existingSessionId);
                }
            }
            userSessions.clear();
        }

        // Registrar la nueva sesión
        userSessions.add(sessionId);
        sessions.put(sessionId, sessionId);
        log.info("Registrada nueva sesión WebSocket para usuario: {}, sessionId: {}", userId, sessionId);
    }

    public void removeSession(String sessionId) {
        Object session = sessions.remove(sessionId);
        if (session != null) {
            // Encontrar y eliminar la sesión de la lista de sesiones del usuario
            for (Map.Entry<String, Set<String>> entry : userSessionIds.entrySet()) {
                if (entry.getValue().remove(sessionId)) {
                    log.info("Eliminada sesión WebSocket para usuario: {}, sessionId: {}", entry.getKey(), sessionId);
                    break;
                }
            }
        }
    }

    /**
     * Obtiene todas las sesiones activas para un usuario
     */
    public Set<String> getSessionsForUser(String userId) {
        return userSessionIds.getOrDefault(userId, ConcurrentHashMap.newKeySet());
    }

    /**
     * Verifica si un usuario tiene alguna sesión activa
     */
    public boolean isUserConnected(String userId) {
        Set<String> sessions = userSessionIds.get(userId);
        return sessions != null && !sessions.isEmpty();
    }

    /**
     * Obtiene el número de sesiones activas para un usuario
     */
    public int getSessionCount(String userId) {
        Set<String> sessions = userSessionIds.get(userId);
        return sessions != null ? sessions.size() : 0;
    }

    /**
     * Cierra todas las sesiones para un usuario específico
     */
    public void closeAllSessionsForUser(String userId) {
        Set<String> userSessions = userSessionIds.get(userId);
        if (userSessions != null) {
            for (String sessionId : userSessions) {
                sessions.remove(sessionId);
                log.info("Eliminando sesión WebSocket para usuario: {}, sessionId: {}", userId, sessionId);
            }
            userSessionIds.remove(userId);
        }
    }
}