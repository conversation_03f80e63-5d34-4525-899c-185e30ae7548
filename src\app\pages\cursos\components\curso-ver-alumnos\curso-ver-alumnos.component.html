<div class="w-full max-h-[80vh] overflow-y-auto p-3" [ngClass]="{'dark-theme': isDarkTheme}">
  <h2 mat-dialog-title class="text-lg font-medium mb-3">Alumnos del Curso: {{ curso.nombre }}</h2>

  <mat-dialog-content class="p-0 m-0">
    <div *ngIf="loading" class="flex flex-col items-center justify-center p-4 text-center">
      <mat-spinner diameter="32"></mat-spinner>
      <p class="mt-2 text-sm">{{ loadingMessage || 'Cargando alumnos...' }}</p>
    </div>

    <div *ngIf="error && !loading" class="flex items-center text-red-500 p-3 bg-red-50 rounded my-2">
      <mat-icon class="mr-2 text-sm">error</mat-icon>
      <span class="text-sm">{{ error }}</span>
    </div>

    <div *ngIf="!loading && !error && usuariosAsignados.length > 0" class="mt-2 max-h-[60vh] overflow-y-auto">
      <div class="space-y-2">
        <div *ngFor="let asignacion of usuariosAsignados; let i = index"
             class="bg-gray-50 dark:bg-gray-800 rounded-lg p-2 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700">
          <div class="flex items-start">
            <!-- Avatar -->
            <div class="mr-3 flex-shrink-0">
              <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium"
                   [ngStyle]="{'background-color': getAvatarColor(asignacion.usuario.username)}">
                {{ getInitials(asignacion.usuario.nombre, asignacion.usuario.apellido) }}
              </div>
            </div>

            <!-- Información del alumno -->
            <div class="flex-1 min-w-0 overflow-hidden">
              <!-- Nombre y username -->
              <div class="flex items-center flex-wrap mb-1">
                <span class="font-medium text-sm mr-1">{{ asignacion.usuario.nombre || '' }} {{ asignacion.usuario.apellido || '' }}</span>
                <span class="text-xs text-gray-500 dark:text-gray-400">({{ asignacion.usuario.username }})</span>
              </div>

              <!-- Detalles -->
              <div class="text-xs text-gray-600 dark:text-gray-300 space-y-1 mb-2">
                <!-- Email -->
                <div class="flex items-center overflow-hidden text-ellipsis whitespace-nowrap">
                  <mat-icon class="text-gray-500 dark:text-gray-400 mr-1 !text-sm !w-4 !h-4 !min-w-4">email</mat-icon>
                  <span>{{ asignacion.usuario.email }}</span>
                </div>

                <!-- DNI -->
                <div class="flex items-center overflow-hidden text-ellipsis whitespace-nowrap">
                  <mat-icon class="text-gray-500 dark:text-gray-400 mr-1 !text-sm !w-4 !h-4 !min-w-4">badge</mat-icon>
                  <span>{{ asignacion.usuario.dni }}</span>
                </div>

                <!-- Sede (si existe) -->
                <div *ngIf="asignacion.usuario.sede" class="flex items-center overflow-hidden text-ellipsis whitespace-nowrap">
                  <mat-icon class="text-gray-500 dark:text-gray-400 mr-1 !text-sm !w-4 !h-4 !min-w-4">location_on</mat-icon>
                  <span>{{ asignacion.usuario.sede }}</span>
                </div>

                <!-- Progreso -->
                <div class="flex items-center mt-2">
                  <mat-icon class="text-gray-500 dark:text-gray-400 mr-1 !text-sm !w-4 !h-4 !min-w-4">school</mat-icon>
                  <div class="flex-1 ml-1">
                    <div class="flex justify-between text-xs mb-1">
                      <span class="font-medium">Progreso:</span>
                      <span class="font-semibold">{{ getPorcentajeProgresoAlumno(asignacion) }}%</span>
                    </div>
                    <div class="h-1.5 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                      <div class="h-full rounded-full transition-all duration-300"
                           [style.width.%]="getPorcentajeProgresoAlumno(asignacion)"
                           [style.background-color]="getColorProgreso(getPorcentajeProgresoAlumno(asignacion))">
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Rol y estado de completado -->
              <div class="flex items-center flex-wrap gap-2">
                <span class="px-2 py-0.5 text-[10px] font-medium uppercase rounded-full"
                      [ngClass]="{
                        'bg-red-500 text-white': asignacion.usuario.role?.toLowerCase() === 'admin',
                        'bg-blue-500 text-white': asignacion.usuario.role?.toLowerCase() === 'coordinador',
                        'bg-green-500 text-white': asignacion.usuario.role?.toLowerCase() === 'asesor',
                        'bg-orange-500 text-white': asignacion.usuario.role?.toLowerCase() === 'auditor',
                        'bg-purple-500 text-white': asignacion.usuario.role?.toLowerCase() === 'programador',
                        'bg-amber-700 text-white': ['backoffice', 'backofficeseguimiento', 'backofficetramitador'].includes(asignacion.usuario.role?.toLowerCase() || ''),
                        'bg-gray-200 text-gray-700': !asignacion.usuario.role
                      }">
                  {{ asignacion.usuario.role }}
                </span>

                <!-- Estado de completado -->
                <span *ngIf="getPorcentajeProgresoAlumno(asignacion) === 100 || haCompletadoCurso(asignacion)"
                      class="flex items-center px-2 py-0.5 text-[10px] font-medium rounded-full bg-green-500 text-white">
                  <mat-icon class="!text-[10px] !w-3 !h-3 !min-w-3 mr-0.5">check_circle</mat-icon>
                  Completado
                </span>
              </div>
            </div>

            <!-- Acciones -->
            <div class="ml-2">
              <button mat-icon-button color="warn" (click)="onRemoveAlumno(asignacion.usuario); $event.stopPropagation();"
                      matTooltip="Eliminar alumno del curso" class="!w-6 !h-6 !min-w-6 opacity-70 hover:opacity-100 transition-opacity">
                <mat-icon class="!text-sm !w-4 !h-4">person_remove</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="p-0 mt-3">
    <button mat-button (click)="onClose()" class="text-sm">Cerrar</button>
  </mat-dialog-actions>
</div>
