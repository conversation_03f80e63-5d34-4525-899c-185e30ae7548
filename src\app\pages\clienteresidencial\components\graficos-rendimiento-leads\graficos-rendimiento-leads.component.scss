// Estilos específicos para el componente de gráficos de rendimiento de leads

.graficos-rendimiento-container {
  .filter-controls {
    @apply flex flex-wrap gap-4 items-end;
    
    .filter-group {
      @apply flex flex-col;
      
      label {
        @apply text-xs font-medium text-gray-700 dark:text-gray-300 mb-1;
      }
      
      select, input {
        @apply px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md;
        @apply bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100;
        @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
        @apply transition-colors duration-200;
        
        &:disabled {
          @apply bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed;
        }
      }
    }
  }
  
  .stats-cards {
    @apply grid grid-cols-1 sm:grid-cols-3 gap-4;
    
    .stat-card {
      @apply rounded-lg p-4 text-white;
      
      &.leads-card {
        @apply bg-gradient-to-r from-blue-500 to-blue-600;
      }
      
      &.asesores-card {
        @apply bg-gradient-to-r from-green-500 to-green-600;
      }
      
      &.promedio-card {
        @apply bg-gradient-to-r from-purple-500 to-purple-600;
      }
      
      .stat-content {
        @apply flex items-center justify-between;
        
        .stat-info {
          .stat-label {
            @apply text-sm font-medium opacity-90;
          }
          
          .stat-value {
            @apply text-2xl font-bold;
          }
        }
        
        .stat-icon {
          @apply bg-opacity-30 rounded-full p-3;
          
          svg {
            @apply w-6 h-6;
          }
        }
      }
    }
  }
  
  .chart-container {
    @apply bg-white dark:bg-gray-900 shadow-md rounded-2xl p-6;
    
    .chart-header {
      @apply flex items-center justify-between mb-4;
      
      h4 {
        @apply text-md font-semibold text-gray-800 dark:text-gray-200;
      }
      
      .loading-indicator {
        @apply flex items-center gap-2 text-gray-500;
        
        svg {
          @apply w-4 h-4;
        }
        
        span {
          @apply text-sm;
        }
      }
    }
    
    .chart-wrapper {
      @apply relative w-full;
      height: 400px;
      
      canvas {
        @apply w-full h-full;
      }
      
      .no-data-message {
        @apply absolute inset-0 flex items-center justify-center;
        @apply bg-gray-50 dark:bg-gray-800 rounded-lg;
        
        .no-data-content {
          @apply text-center;
          
          svg {
            @apply mx-auto w-12 h-12 text-gray-400 mb-4;
          }
          
          p {
            @apply text-gray-500 dark:text-gray-400;
            
            &.subtitle {
              @apply text-sm text-gray-400 dark:text-gray-500 mt-1;
            }
          }
        }
      }
    }
  }
  
  .refresh-button {
    @apply px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400;
    @apply text-white text-sm font-medium rounded-md;
    @apply transition-colors duration-200 flex items-center gap-2;
    
    svg {
      @apply w-4 h-4;
      
      &.spinning {
        @apply animate-spin;
      }
    }
    
    &:disabled {
      @apply cursor-not-allowed;
    }
  }
}

// Animaciones personalizadas
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// Responsive adjustments
@media (max-width: 640px) {
  .graficos-rendimiento-container {
    .filter-controls {
      @apply flex-col items-stretch;
      
      .filter-group {
        @apply w-full;
      }
    }
    
    .stats-cards {
      @apply grid-cols-1;
    }
  }
}

@media (max-width: 768px) {
  .graficos-rendimiento-container {
    .chart-container {
      @apply p-4;
      
      .chart-wrapper {
        height: 300px;
      }
    }
  }
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .graficos-rendimiento-container {
    .chart-container {
      canvas {
        // Chart.js will handle dark mode colors via configuration
      }
    }
  }
}

// Hover effects
.stat-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

// Loading states
.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75;
  @apply flex items-center justify-center rounded-lg;
  
  .loading-spinner {
    @apply w-8 h-8 text-blue-600 animate-spin;
  }
}
