package com.midas.crm.entity.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO para las estadísticas por sede
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EstadisticaSedeDTO {

    private String sede;
    private String supervisor;
    private String vendedor;
    private Integer tomaDatos;
    private Integer interesadosSeguro;
    private Integer interesadosEnergia;
    private Integer interesadosLowi;

    // Constructor adicional para facilitar la creación desde consultas
    public EstadisticaSedeDTO(String sede, String supervisor, String vendedor, Long tomaDatos, Long interesadosSeguro, Long interesadosEnergia, Long interesadosLowi) {
        this.sede = sede;
        this.supervisor = supervisor;
        this.vendedor = vendedor;
        this.tomaDatos = tomaDatos != null ? tomaDatos.intValue() : 0;
        this.interesadosSeguro = interesadosSeguro != null ? interesadosSeguro.intValue() : 0;
        this.interesadosEnergia = interesadosEnergia != null ? interesadosEnergia.intValue() : 0;
        this.interesadosLowi = interesadosLowi != null ? interesadosLowi.intValue() : 0;
    }
}
