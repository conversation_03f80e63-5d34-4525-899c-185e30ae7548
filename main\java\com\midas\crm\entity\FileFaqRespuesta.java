package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "file_faq_respuestas")
@Data
public class FileFaqRespuesta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    private String type;

    private Long size;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String url;
}
