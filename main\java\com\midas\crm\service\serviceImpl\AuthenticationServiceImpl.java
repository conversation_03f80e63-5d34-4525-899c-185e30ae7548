package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.security.jwt.JwtProvider;
import com.midas.crm.service.AuthenticationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class AuthenticationServiceImpl implements AuthenticationService {

    private final AuthenticationManager authenticationManager;
    private final JwtProvider jwtProvider;
    private final UserRepository userRepository;

    @Override
    public User signInAndReturnJWT(User signInRequest) {
        // Buscar el usuario por username - ahora manejamos la excepción en el controlador
        User user = userRepository.findByUsername(signInRequest.getUsername())
                .orElseThrow(() -> new UsernameNotFoundException("Usuario no encontrado"));

        // Verificar que el usuario esté activo
        if (!"A".equals(user.getEstado())) {
            throw new UsernameNotFoundException("Usuario inactivo");
        }

        try {
            // Autenticar al usuario
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            signInRequest.getUsername(),
                            signInRequest.getPassword()
                    )
            );

            // Generar el token JWT
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            String jwt = jwtProvider.generateToken(userPrincipal);

            // Crear una copia limpia del usuario para evitar problemas de LazyInitializationException
            // Optimizado para reducir tiempo de procesamiento
            User signInUser = new User();
            signInUser.setId(user.getId());
            signInUser.setUsername(user.getUsername());
            signInUser.setNombre(user.getNombre());
            signInUser.setApellido(user.getApellido());
            signInUser.setDni(user.getDni());
            signInUser.setTelefono(user.getTelefono());
            signInUser.setEmail(user.getEmail());
            signInUser.setFechaCreacion(user.getFechaCreacion());
            signInUser.setFechaCese(user.getFechaCese());
            signInUser.setEstado(user.getEstado());
            signInUser.setRole(user.getRole());
            signInUser.setSede(user.getSede());
            signInUser.setToken(jwt);

            // No incluimos las colecciones asesores ni coordinador para evitar LazyInitializationException
            // Estas se cargarán en el controlador según sea necesario



            return signInUser;
        } catch (Exception e) {
            throw e; // Relanzamos la excepción para que sea manejada por el controlador
        }
    }
}
