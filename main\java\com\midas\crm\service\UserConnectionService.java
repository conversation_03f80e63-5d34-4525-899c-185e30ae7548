package com.midas.crm.service;

import com.midas.crm.entity.DTO.websocket.UserStatusDTO;
import com.midas.crm.entity.User;

import java.util.List;

/**
 * Servicio para gestionar las conexiones de usuarios
 */
public interface UserConnectionService {

    /**
     * Registra la conexión de un usuario
     * @param userId ID del usuario que se conecta
     * @return DTO con el estado actualizado del usuario
     */
    UserStatusDTO connectUser(Long userId);

    /**
     * Registra la desconexión de un usuario
     * @param userId ID del usuario que se desconecta
     * @return DTO con el estado actualizado del usuario
     */
    UserStatusDTO disconnectUser(Long userId);

    /**
     * Obtiene la lista de todos los usuarios con su estado de conexión
     * @return Lista de DTOs con el estado de conexión de cada usuario
     */
    List<UserStatusDTO> getAllUserStatuses();

    /**
     * Verifica si un usuario está conectado
     * @param userId ID del usuario a verificar
     * @return true si el usuario está conectado, false en caso contrario
     */
    boolean isUserConnected(Long userId);

    /**
     * Actualiza la actividad de un usuario (para mantener la sesión activa)
     * @param userId ID del usuario que realiza la actividad
     */
    void updateUserActivity(Long userId);

    /**
     * Elimina una sesión específica
     * @param sessionId ID de la sesión a eliminar
     */
    void removeSession(String sessionId);

    /**
     * Marca a todos los usuarios como desconectados
     * Útil cuando se reinicia el servidor o cuando hay problemas de conexión
     */
    void markAllUsersOffline();
}