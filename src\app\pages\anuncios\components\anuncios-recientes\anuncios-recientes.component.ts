import { Component, OnInit, AfterViewInit, On<PERSON><PERSON>roy } from '@angular/core';
import { PageEvent } from '@angular/material/paginator';
import {
  Observable,
  BehaviorSubject,
  Subscription,
  filter,
  map,
  combineLatest,
  take
} from 'rxjs';
import { AnuncioResponse } from '@app/pages/anuncios/store/save/save.models';
import { WebSocketService } from '@app/services/websocket/WebSocketService';
import { Router } from '@angular/router';
import { ThemeService } from '@app/services/theme.service';
import { NotificationService } from '@app/services';
import { SedeUserService } from '@app/services/sede-user.service';

@Component({
  selector: 'app-anuncios-recientes',
  templateUrl: './anuncios-recientes.component.html',
  styleUrls: ['./anuncios-recientes.component.scss'],
})
export class AnunciosRecientesComponent
  implements OnInit, AfterViewInit, On<PERSON><PERSON>roy
{
  // Propiedad para el tema oscuro
  isDarkTheme: boolean = false;

  // Anuncios y filtrado
  anuncios: AnuncioResponse[] = [];
  anunciosSubject = new BehaviorSubject<AnuncioResponse[]>([]);
  anunciosFiltrados$: Observable<AnuncioResponse[]>;
  loading: boolean = true;
  searchTerm: string = '';
  searchTermSubject = new BehaviorSubject<string>('');
  maxAnuncios: number = 6; // Tamaño predeterminado que coincide con el backend

  // Variables para paginación
  currentPage: number = 0; // Cambiado a 0 para coincidir con la convención de Spring (0-based)
  totalPages: number = 1;
  totalElements: number = 0;

  // Variable para almacenar el total real de elementos (sin modificar)
  realTotalElements: number = 0;

  // Imagen por defecto
  pictureDefault: string =
    'https://firebasestorage.googleapis.com/v0/b/edificacion-app.appspot.com/o/image%2F1637099019171_O5986058_0.jpg?alt=media&token=0a146233-d63b-4702-b28d-6eaddf5e207a';

  // Control de carga de imágenes
  loadedImages: { [key: string]: boolean } = {};

  // Propiedades para la sede
  userSedeId: number | null = null;
  userSedeName: string = '';

  // Propiedad para el rol del usuario
  isAdmin: boolean = false;

  private subscription = new Subscription();

  constructor(
    private webSocketService: WebSocketService,
    private router: Router,
    private themeService: ThemeService,
    private notification: NotificationService,
    private sedeUserService: SedeUserService
  ) {
    // Configurar el observable de anuncios filtrados
    this.anunciosFiltrados$ = combineLatest([
      this.anunciosSubject,
      this.searchTermSubject
    ]).pipe(
      map(([anuncios, searchTerm]) => {
        if (!anuncios || anuncios.length === 0) return [];
        if (!searchTerm.trim()) return anuncios;

        searchTerm = searchTerm.toLowerCase();
        return anuncios.filter((anuncio) => {
          // Formatear la fecha para buscar por día-mes-año
          const fecha = new Date(anuncio.fechaPublicacion);
          const fechaFormateada = `${fecha
            .getDate()
            .toString()
            .padStart(2, '0')}-${(fecha.getMonth() + 1)
            .toString()
            .padStart(2, '0')}-${fecha.getFullYear()}`;

          return (
            anuncio.titulo.toLowerCase().includes(searchTerm) ||
            anuncio.descripcion.toLowerCase().includes(searchTerm) ||
            anuncio.categoria.toLowerCase().includes(searchTerm) ||
            fechaFormateada.includes(searchTerm)
          );
        });
      })
    );
  }

  ngOnInit(): void {
    // Suscribirse al servicio de tema
    this.subscription.add(
      this.themeService.darkMode$.subscribe((isDark: boolean) => {
        this.isDarkTheme = isDark;
      })
    );

    // También verificar el estado actual del tema
    this.checkDarkTheme();

    // Obtener la información del usuario directamente del objeto user en localStorage
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);

        // Verificar si el usuario es ADMIN
        this.isAdmin = user.role === 'ADMIN';

        // Para administradores, asegurar que siempre haya un valor mínimo para totalElements
        if (this.isAdmin && this.totalElements === 0) {
          this.totalElements = 12; // Valor mínimo para que siempre se muestre la paginación
          // Inicializar realTotalElements con un valor por defecto
          this.realTotalElements = 0;
        }

        // Obtener el ID de la sede
        if (user.sede_id) {
          this.userSedeId = user.sede_id;
        }

        // Obtener el nombre de la sede
        if (user.sede && typeof user.sede === 'string') {
          this.userSedeName = user.sede;
        } else if (user.sede && typeof user.sede === 'object' && user.sede.nombre) {
          this.userSedeName = user.sede.nombre;
        } else if (this.userSedeId) {
          // Si tenemos ID pero no nombre, usar "tu sede" como valor predeterminado
          this.userSedeName = 'tu sede';

          // Actualizar el objeto user en localStorage
          user.sede = 'tu sede';
          localStorage.setItem('user', JSON.stringify(user));
        }
      }
    } catch (error) {
      console.error('Error al obtener información de usuario:', error);

      // En caso de error, usar los métodos del servicio como respaldo
      this.userSedeId = this.sedeUserService.getSedeIdSync();
      this.userSedeName = this.sedeUserService.getSedeNameSync();
    }

    // No cargar anuncios si estamos en la página de login
    if (this.router.url.includes('/auth/login')) {
      return;
    }

    // Determinar el tópico correcto según el rol del usuario
    const topico = this.isAdmin
      ? '/topic/anuncios/recientes'  // Tópico general para ADMIN
      : `/topic/anuncios/recientes/sede/${this.userSedeId}`; // Tópico específico para usuarios normales

    // Asegurarse de que estamos suscritos al tópico correcto
    this.webSocketService.subscribeToDynamicTopic(topico, 'TOPIC');

    // Suscribirse a los anuncios recientes desde el servicio WebSocket usando el tópico correcto
    this.subscription.add(
      this.webSocketService
        .getMessagesByDestination(topico)
        .pipe(
          // Filtrar actualizaciones vacías
          filter(
            (anuncios: any) => anuncios &&
            (Array.isArray(anuncios) ? anuncios.length > 0 :
             (anuncios.content && Array.isArray(anuncios.content) ? anuncios.content.length > 0 : false))
          )
        )
        .subscribe((response: any) => {
          // Extraer los anuncios del response (puede ser un array o un objeto con content)
          let anuncios: AnuncioResponse[] = [];
          let totalElements = 0;
          let totalPages = 1;

          if (Array.isArray(response)) {
            anuncios = response;
          } else if (response && response.content && Array.isArray(response.content)) {
            anuncios = response.content;

            // Extraer información de paginación si está disponible
            if (response.totalElements !== undefined) {
              // Guardar el total real de elementos
              this.realTotalElements = response.totalElements;

              // Para administradores, calcular el total de elementos para la paginación
              if (this.isAdmin) {
                // Si ya tenemos un valor para totalElements y es mayor que el real, mantenerlo
                if (this.totalElements > this.realTotalElements) {
                  totalElements = this.totalElements;
                } else {
                  // Si el total real es menor que 12, usar 12 como mínimo para asegurar paginación
                  totalElements = Math.max(12, this.realTotalElements);
                  this.totalElements = totalElements;
                }
              } else {
                // Para usuarios normales, usar el valor real
                totalElements = this.realTotalElements;
                this.totalElements = totalElements;
              }


            }

            if (response.totalPages !== undefined) {
              // Guardar el total real de páginas
              const realTotalPages = response.totalPages;

              // Para administradores, calcular el total de páginas para la paginación
              if (this.isAdmin) {
                // Si el total real es menor que 2, usar 2 como mínimo para asegurar paginación
                totalPages = Math.max(2, realTotalPages);
              } else {
                // Para usuarios normales, usar el valor real
                totalPages = realTotalPages;
              }

              this.totalPages = totalPages;
            }

            // Si hay información de página actual en la respuesta, actualizarla
            if (response.number !== undefined) {
              // El backend usa base 0 para las páginas
              this.currentPage = response.number;
            }
          }

          // Filtrar solo anuncios activos
          anuncios = anuncios.filter(a => !a.estado || a.estado === 'ACTIVO');

          // Si no hay anuncios activos después del filtrado, no continuar
          if (anuncios.length === 0) {
            return;
          }

          // Actualizar directamente los anuncios sin pasar por el store
          this.anuncios = [...anuncios];

          // Actualizar el BehaviorSubject con los nuevos anuncios
          this.anunciosSubject.next(this.anuncios);

          // Precargar imágenes
          this.precargarImagenesDeAnuncios(anuncios);

          // Marcar como cargado
          this.loading = false;
        })
    );

    // Solicitar anuncios recientes (página 0 por defecto)
    this.requestAnunciosRecientes(0);
  }

  /**
   * Solicita anuncios recientes al backend
   * Si el usuario es ADMIN, solicita todos los anuncios sin filtrar por sede
   * @param page Número de página a solicitar (por defecto 0)
   */
  requestAnunciosRecientes(page: number = 0): void {
    // Actualizar la página actual
    this.currentPage = page;

    // Preparar los parámetros de la solicitud
    // Asegurarse de que la página sea un número y esté en base 0 para el backend
    const pageIndex = Number(page);

    // Asegurarse de que el tamaño de página sea un número válido
    const pageSize = Number(this.maxAnuncios);

    const params: any = {
      page: pageIndex,
      size: pageSize
    };

    // Solo incluir sedeId si el usuario NO es ADMIN
    if (!this.isAdmin) {
      params.sedeId = this.userSedeId;
    } else {
      // Si es ADMIN, añadir el rol para que el backend pueda identificarlo
      params.role = 'ADMIN';

      // Asegurarse de que el tamaño de página sea al menos 6 para administradores
      if (pageSize < 6) {
        this.maxAnuncios = 6;
        params.size = 6;
      } else {
        // Usar el tamaño de página seleccionado por el usuario
        params.size = pageSize;
      }
    }

    // Determinar el endpoint correcto
    const endpoint = '/app/anuncios.recientes';

    // Determinar el tópico correcto según el rol del usuario
    const topico = this.isAdmin
      ? '/topic/anuncios/recientes'  // Tópico general para ADMIN
      : `/topic/anuncios/recientes/sede/${this.userSedeId}`; // Tópico específico para usuarios normales

    // Asegurarse de que estamos suscritos al tópico correcto
    this.webSocketService.subscribeToDynamicTopic(topico, 'TOPIC');



    if (this.webSocketService.isConnected()) {
      // Si WebSocket ya está conectado, solicitar anuncios recientes
      this.webSocketService.sendMessage(endpoint, params);
    } else {
      // Si WebSocket no está conectado, esperar a que se conecte
      this.subscription.add(
        this.webSocketService
          .getConnectionStatus()
          .pipe(
            filter((connected) => connected === true),
            take(1)
          )
          .subscribe(() => {
            // WebSocket conectado, solicitar anuncios recientes
            this.webSocketService.sendMessage(endpoint, params);
          })
      );
    }
  }

  /**
   * Maneja el cambio de página en el paginador
   * @param event Evento de paginación
   */
  onPageChange(event: PageEvent): void {
    // Verificar si cambió el tamaño de página
    const sizeChanged = this.maxAnuncios !== event.pageSize;

    // Actualizar el tamaño de página si cambió
    if (sizeChanged) {
      this.maxAnuncios = event.pageSize;

      // Cuando cambia el tamaño de página, volver a la primera página
      this.currentPage = 0;
    } else {
      // Si no cambió el tamaño, actualizar la página actual
      this.currentPage = event.pageIndex;
    }

    // Solicitar la nueva página de anuncios
    // El backend espera un índice base 0
    this.requestAnunciosRecientes(this.currentPage);
  }

  ngAfterViewInit(): void {
    // Precarga la imagen por defecto para tenerla en caché inmediatamente
    this.preloadImage(this.pictureDefault);
  }

  /**
   * Verifica si el tema oscuro está activo
   */
  checkDarkTheme(): void {
    this.themeService.darkMode$.pipe(take(1)).subscribe((isDark: boolean) => {
      this.isDarkTheme = isDark;
    });
  }

  /**
   * Maneja el cambio en la búsqueda
   */
  onSearchChange(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.searchTerm = value;
    this.searchTermSubject.next(value);
  }

  /**
   * Limpia el término de búsqueda
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.searchTermSubject.next('');
  }

  /**
   * Navega a la página de detalle de un anuncio
   */
  verDetalle(id: number): void {
    this.router.navigate(['/anuncios/detalle', id]);
  }

  /**
   * Formatea la diferencia de tiempo para mostrar hace cuánto se publicó un anuncio
   */
  formatTimeDifference(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffMonths = Math.floor(diffDays / 30);

    if (diffMonths > 0) {
      return diffMonths === 1 ? 'Hace 1 mes' : `Hace ${diffMonths} meses`;
    } else if (diffDays > 0) {
      return diffDays === 1 ? 'Hace 1 día' : `Hace ${diffDays} días`;
    } else if (diffHours > 0) {
      return diffHours === 1 ? 'Hace 1 hora' : `Hace ${diffHours} horas`;
    } else if (diffMins > 0) {
      return diffMins === 1 ? 'Hace 1 minuto' : `Hace ${diffMins} minutos`;
    } else {
      return 'Hace unos segundos';
    }
  }

  /**
   * Precarga una imagen para evitar parpadeos al cargar
   */
  preloadImage(src: string): void {
    if (!src) return;

    // Si la imagen ya está en caché, no hacer nada
    if (this.loadedImages[src]) return;

    // Crear un nuevo elemento de imagen para precargar
    const img = new Image();

    // Cuando la imagen cargue, marcarla como cargada
    img.onload = () => {
      // Marcar como cargada
      this.loadedImages[src] = true;
    };

    // Manejar errores de carga
    img.onerror = () => {
      // Intentar con la imagen por defecto si falla
      if (src !== this.pictureDefault) {
        this.preloadImage(this.pictureDefault);
      }
    };

    // Iniciar la carga de la imagen
    img.src = src;
  }

  /**
   * Precarga las imágenes de todos los anuncios
   */
  precargarImagenesDeAnuncios(anuncios: AnuncioResponse[]): void {
    if (!anuncios || anuncios.length === 0) return;

    // Precargar todas las imágenes de los anuncios
    anuncios.forEach((anuncio, index) => {
      if (anuncio.imagenUrl) {
        // Usar un pequeño retraso para escalonar la carga y evitar bloqueos
        setTimeout(() => {
          this.preloadImage(anuncio.imagenUrl);
        }, index * 100); // Escalonar la carga cada 100ms
      }
    });

    // Precargar la imagen por defecto
    this.preloadImage(this.pictureDefault);
  }

  /**
   * Método para obtener los IDs de los anuncios (para depuración)
   */
  getAnuncioIds(anuncios: AnuncioResponse[]): string {
    if (!anuncios || anuncios.length === 0) return 'ninguno';
    return anuncios.map(a => a.id).join(', ');
  }

  /**
   * Divide un array de anuncios en chunks de tamaño específico
   * Esto permite mostrar los anuncios en filas de 2 elementos
   * @param anuncios Array de anuncios a dividir
   * @param startIndex Índice desde donde empezar a dividir (por defecto 4, para mostrar los anuncios después de los primeros 4)
   * @returns Array de arrays de anuncios, cada subarray representa una fila
   */
  getAnunciosChunks(anuncios: AnuncioResponse[], startIndex: number = 4): AnuncioResponse[][] {
    if (!anuncios || anuncios.length <= startIndex) {
      return [];
    }

    // Obtener solo los anuncios a partir del índice de inicio
    const remainingAnuncios = anuncios.slice(startIndex);

    // Dividir en chunks de 2 elementos
    const chunks: AnuncioResponse[][] = [];
    for (let i = 0; i < remainingAnuncios.length; i += 2) {
      chunks.push(remainingAnuncios.slice(i, i + 2));
    }

    return chunks;
  }

  /**
   * Método para forzar la actualización de anuncios recientes
   * Si el usuario es ADMIN, solicita todos los anuncios sin filtrar por sede
   */
  forceUpdate(): void {
    // Preparar los parámetros de la solicitud
    const params: any = {
      page: 0,
      size: this.maxAnuncios
    };

    // Solo incluir sedeId si el usuario NO es ADMIN
    if (!this.isAdmin) {
      params.sedeId = this.userSedeId;
    }

    // Determinar el endpoint correcto
    const endpoint = '/app/anuncios.recientes';

    // Determinar el tópico correcto según el rol del usuario
    const topico = this.isAdmin
      ? '/topic/anuncios/recientes'  // Tópico general para ADMIN
      : `/topic/anuncios/recientes/sede/${this.userSedeId}`; // Tópico específico para usuarios normales

    // Asegurarse de que estamos suscritos al tópico correcto
    this.webSocketService.subscribeToDynamicTopic(topico, 'TOPIC');

    // Enviar la solicitud directamente si WebSocket está conectado
    if (this.webSocketService.isConnected()) {
      this.webSocketService.sendMessage(endpoint, params);
    } else {
      // Si WebSocket no está conectado, intentar conectar y luego enviar
      this.webSocketService.connect();

      // Esperar a que se conecte y luego enviar
      this.subscription.add(
        this.webSocketService
          .getConnectionStatus()
          .pipe(
            filter(connected => connected),
            take(1)
          )
          .subscribe(() => {
            this.webSocketService.sendMessage(endpoint, params);
          })
      );
    }

    this.notification.success('Actualizando anuncios...');
  }

  /**
   * Método para formatear la fecha en formato DD-MM-YYYY
   */
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  ngOnDestroy(): void {
    // Cancelar todas las suscripciones para evitar memory leaks
    this.subscription.unsubscribe();
  }
}