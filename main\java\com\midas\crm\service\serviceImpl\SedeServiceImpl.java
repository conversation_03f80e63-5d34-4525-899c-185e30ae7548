package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.SedeDTO;
import com.midas.crm.entity.DTO.SedePaginadoResponse;
import com.midas.crm.entity.Sede;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.SedeMapper;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.service.SedeService;
import com.midas.crm.utils.MidasErrorMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class SedeServiceImpl implements SedeService {

    private static final Logger logger = LoggerFactory.getLogger(SedeServiceImpl.class);

    private final SedeRepository sedeRepository;

    @Autowired
    public SedeServiceImpl(SedeRepository sedeRepository) {
        this.sedeRepository = sedeRepository;
    }

    @Override
    @Transactional
    public SedeDTO guardarSede(SedeDTO sedeDTO) {
        //logger.info("Guardando nueva sede: {}", sedeDTO.getNombre());

        // Verificar si ya existe una sede con el mismo nombre usando Optional
        return Optional.of(sedeDTO)
                .filter(dto -> !sedeRepository.existsByNombre(dto.getNombre()))
                .map(dto -> {
                    // Transformar, establecer estado y guardar en un flujo funcional
                    Sede sede = SedeMapper.toEntity(dto);
                    sede.setEstado("A"); // A = Activo
                    return SedeMapper.toDTO(sedeRepository.save(sede));
                })
                .orElseThrow(() -> {
                    //logger.warn("Ya existe una sede con el nombre: {}", sedeDTO.getNombre());
                    return new MidasExceptions(MidasErrorMessage.SEDE_ALREADY_EXISTS);
                });
    }

    @Override
    @Transactional
    public SedeDTO actualizarSede(Long id, SedeDTO sedeDTO) {
        //logger.info("Actualizando sede con ID: {}", id);

        return sedeRepository.findById(id)
                .map(sede -> {
                    // Verificar si el nombre ya existe y no es el mismo registro
                    if (!sede.getNombre().equals(sedeDTO.getNombre()) &&
                            sedeRepository.existsByNombre(sedeDTO.getNombre())) {
                        //logger.warn("Ya existe otra sede con el nombre: {}", sedeDTO.getNombre());
                        throw new MidasExceptions(MidasErrorMessage.SEDE_ALREADY_EXISTS);
                    }

                    // Actualizar campos
                    sede.setNombre(sedeDTO.getNombre());
                    sede.setDireccion(sedeDTO.getDireccion());
                    sede.setCiudad(sedeDTO.getCiudad());
                    sede.setProvincia(sedeDTO.getProvincia());
                    sede.setCodigoPostal(sedeDTO.getCodigoPostal());
                    sede.setTelefono(sedeDTO.getTelefono());
                    sede.setEmail(sedeDTO.getEmail());
                    if (sedeDTO.getActivo() != null) {
                        sede.setEstado(sedeDTO.getActivo() ? "A" : "I"); // A = Activo, I = Inactivo
                    }

                    Sede updatedSede = sedeRepository.save(sede);
                    //logger.info("Sede actualizada exitosamente con ID: {}", updatedSede.getId());
                    return SedeMapper.toDTO(updatedSede);
                })
                .orElseThrow(() -> {
                    //logger.warn("No se encontró la sede con ID: {}", id);
                    return new MidasExceptions(MidasErrorMessage.SEDE_NOT_FOUND);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SedeDTO> obtenerSedePorId(Long id) {
        //logger.info("Obteniendo sede por ID: {}", id);
        return sedeRepository.findById(id)
                .map(SedeMapper::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SedeDTO> obtenerTodasLasSedes() {
        //logger.info("Obteniendo todas las sedes");
        return sedeRepository.findAll().stream()
                .map(SedeMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SedeDTO> obtenerSedesActivas() {
        //logger.info("Obteniendo sedes activas");
        return sedeRepository.findAllActive().stream()
                .map(SedeMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean eliminarSede(Long id) {
        //logger.info("Eliminando sede con ID: {}", id);
        return sedeRepository.findById(id)
                .map(sede -> {
                    // Baja lógica
                    sede.setEstado("I"); // I = Inactivo
                    sedeRepository.save(sede);
                    //logger.info("Sede eliminada (baja lógica) exitosamente con ID: {}", id);
                    return true;
                })
                .orElse(false);
    }

    @Override
    @Transactional(readOnly = true)
    public SedePaginadoResponse obtenerSedesPaginadas(String search, Pageable pageable) {
        //logger.info("Obteniendo sedes paginadas con búsqueda: {}, página: {}, tamaño: {}",
                //search, pageable.getPageNumber(), pageable.getPageSize());

        // Usar programación funcional para todo el flujo de transformación
        return Optional.of(sedeRepository.findBySearchTerm(search, pageable))
                .map(sedePage -> {
                    // Transformar entidades a DTOs usando stream
                    List<SedeDTO> sedeDTOs = sedePage.getContent().stream()
                            .map(SedeMapper::toDTO)
                            .collect(Collectors.toList());

                    // Crear y retornar la respuesta paginada
                    SedePaginadoResponse response = new SedePaginadoResponse(
                            sedeDTOs,
                            sedePage.getNumber(),
                            sedePage.getTotalElements(),
                            sedePage.getTotalPages()
                    );

                    //logger.info("Se encontraron {} sedes en total, {} páginas",
                            //response.getTotalItems(), response.getTotalPages());

                    return response;
                })
                .orElse(new SedePaginadoResponse(List.of(), 0, 0, 0));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Sede> obtenerSedePorNombre(String nombre) {
        //logger.info("Obteniendo sede por nombre: {}", nombre);
        return sedeRepository.findByNombre(nombre);
    }
}
