.video-player-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: #1a1a1a;
  margin-bottom: 20px;
}

.video-header {
  padding: 12px 16px;
  background-color: #2c2c2c;
  color: white;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.video-wrapper {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;

  // Estilos para los subtítulos (WebVTT)
  ::cue {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-family: Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
    padding: 4px 8px;
    border-radius: 2px;
  }
}

.video-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;

  &.loaded {
    opacity: 1;
  }
}

app-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #f44336;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.subtitles-control {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;

  button {
    color: white;
  }
}
