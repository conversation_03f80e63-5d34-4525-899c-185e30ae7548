package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.auth.LoginResponseDTO;
import com.midas.crm.entity.User;

public class AuthMapper {

    public static LoginResponseDTO toLoginResponseDTO(User user) {
        LoginResponseDTO dto = new LoginResponseDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setNombre(user.getNombre());
        dto.setApellido(user.getApellido());
        dto.setDni(user.getDni());
        dto.setTelefono(user.getTelefono());
        dto.setEmail(user.getEmail());
        dto.setFechaCreacion(user.getFechaCreacion());
        dto.setFechaCese(user.getFechaCese());
        dto.setEstado(user.getEstado());
        dto.setRole(user.getRole());
        dto.setSede(user.getSedeNombre());
        dto.setToken(user.getToken());
        return dto;
    }
}
