package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.cuestionario.RespuestaCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUpdateDTO;
import com.midas.crm.entity.Pregunta;
import com.midas.crm.entity.Respuesta;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.RespuestaMapper;
import com.midas.crm.repository.PreguntaRepository;
import com.midas.crm.repository.RespuestaRepository;
import com.midas.crm.service.RespuestaService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RespuestaServiceImpl implements RespuestaService {

    private final RespuestaRepository respuestaRepository;
    private final PreguntaRepository preguntaRepository;

    @Override
    @Transactional
    public RespuestaDTO createRespuesta(RespuestaCreateDTO dto) {
        // Obtener la pregunta
        Pregunta pregunta = preguntaRepository.findById(dto.getPreguntaId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PREGUNTA_NOT_FOUND));

        // Crear la respuesta
        Respuesta respuesta = RespuestaMapper.toEntity(dto, pregunta);
        
        return RespuestaMapper.toDTO(respuestaRepository.save(respuesta));
    }

    @Override
    @Transactional(readOnly = true)
    public List<RespuestaDTO> listRespuestas() {
        return respuestaRepository.findAll().stream()
                .map(RespuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<RespuestaDTO> listRespuestasByPreguntaId(Long preguntaId) {
        return respuestaRepository.findByPreguntaIdOrderByOrdenAsc(preguntaId).stream()
                .map(RespuestaMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public RespuestaDTO getRespuestaById(Long id) {
        return respuestaRepository.findById(id)
                .map(RespuestaMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_NOT_FOUND));
    }

    @Override
    @Transactional
    public RespuestaDTO updateRespuesta(Long id, RespuestaUpdateDTO dto) {
        Respuesta respuesta = respuestaRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.RESPUESTA_NOT_FOUND));

        RespuestaMapper.updateEntity(respuesta, dto);
        
        return RespuestaMapper.toDTO(respuestaRepository.save(respuesta));
    }

    @Override
    @Transactional
    public void deleteRespuesta(Long id) {
        if (!respuestaRepository.existsById(id)) {
            throw new MidasExceptions(MidasErrorMessage.RESPUESTA_NOT_FOUND);
        }
        
        respuestaRepository.deleteById(id);
    }
}
