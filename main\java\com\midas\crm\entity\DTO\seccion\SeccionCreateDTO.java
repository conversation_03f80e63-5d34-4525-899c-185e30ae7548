package com.midas.crm.entity.DTO.seccion;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SeccionCreateDTO {
    @NotBlank(message = "El título de la sección es obligatorio")
    private String titulo;
    
    private String descripcion;
    
    private Integer orden;
    
    @NotNull(message = "El ID del módulo es obligatorio")
    private Long moduloId;
    
    private String estado;
}
